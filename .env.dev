# 环境
NODE_ENV=production

# 接口前缀
VITE_API_BASEPATH=dev

# 打包路径
VITE_BASE_PATH=/dist-dev/

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 输出路径
VITE_OUT_DIR=dist-dev

# 标题
VITE_APP_TITLE=文博ERP

VITE_SERVER_URL=http://erp.nibinu.com:58088
VITE_UPLOAD_URL= http://erp.nibinu.com:8890
VITE_WS_URL='ws://8.134.175.217:4041'

VITE_SERVER_TYPE='ws'  #服务器连接协议   ws  http

VITE_DEBUG_MODE = true

VITE_SYNC_PORT=59088  #服务器异步消息端口