# 环境
NODE_ENV=production

# 接口前缀
VITE_API_BASEPATH=pro

# 打包路径
VITE_BASE_PATH=/vue-element-plus-admin/

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 输出路径
VITE_OUT_DIR=dist-pro

# 标题
VITE_APP_TITLE=ElementAdmin

VITE_SERVER_URL=http://erp.nibinu.com:58088
VITE_WS_URL='ws://8.134.175.217:4041'

VITE_SERVER_TYPE='ws'  #服务器连接协议   ws  http

VITE_SYNC_PORT=59088  #服务器异步消息端口