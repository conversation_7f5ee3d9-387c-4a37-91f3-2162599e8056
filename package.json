{"name": "vue-element-plus-admin", "version": "1.9.9", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "author": "Archer <<EMAIL>>", "private": false, "enabledApiProposals": ["extensionRuntime"], "scripts": {"i": "pnpm install", "dev": "vite --mode base", "test": "vite --mode test", "demo": "vite --mode demo", "ts:check": "vue-tsc --noEmit", "build:release": "vite build --mode pro", "build:test": "vite build --mode test", "build:demo": "vite build --mode demo", "build:gitee": "vite build --mode gitee", "build:dev": "npm run ts:check && vite build --mode dev", "serve:release": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "serve:test": "vite preview --mode test", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "prepare": "", "p": "plop", "analysis": "windicss-analysis", "fix-memory-limit": "cross-env LIMIT=8096 increase-memory-limit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/iconify": "^3.1.0", "@msgpack/msgpack": "3.0.0-beta2", "@popperjs/core": "^2.11.8", "@teihin/vue3-kind-editor": "^1.0.1", "@vue-flow/background": "^1.2.0", "@vue-flow/controls": "^1.1.0", "@vue-flow/core": "^1.27.1", "@vue-flow/minimap": "^1.2.0", "@vueuse/core": "^9.13.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^2.2.1", "animate.css": "^4.1.1", "axios": "^1.3.5", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.3", "echarts": "^5.4.2", "echarts-wordcloud": "^2.1.0", "element-plus": "2.3.3", "increase-memory-limit": "^1.0.7", "intro.js": "^7.0.1", "js-base64": "^3.7.5", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "pako": "^2.1.0", "pinia": "^2.0.34", "qrcode": "^1.5.1", "qs": "^6.11.1", "url": "^0.11.0", "vue": "^3.3", "vue-i18n": "9.2.2", "vue-plugin-hiprint": "^0.0.56", "vue-router": "^4.1.6", "vue-types": "^5.0.2", "vue-virtual-scroller": "2.0.0-beta.8", "vxe-table": "4.9.30", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@iconify/json": "^2.2.48", "@intlify/unplugin-vue-i18n": "^0.10.0", "@purge-icons/generated": "^0.9.0", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@types/pako": "^2.0.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.14", "consola": "^3.0.1", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.17.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.10.0", "less": "^4.1.3", "lint-staged": "^13.2.1", "plop": "^3.1.2", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.7", "rimraf": "^5.0.0", "rollup": "^3.20.2", "stylelint": "^15.4.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^11.0.0", "stylelint-config-standard": "^32.0.0", "stylelint-order": "^6.0.3", "terser": "^5.16.9", "typescript": "5.0.4", "unplugin-vue-define-options": "^1.3.3", "vite": "4.2.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.8.10", "vue-tsc": "^1.2.0", "windicss": "^3.5.6", "windicss-analysis": "^0.3.5"}, "engines": {"node": ">= 14.18.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}