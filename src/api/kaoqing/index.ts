//考勤相关接口
import request from '@/config/axios'

//新增设备
export const addKaoqingDeviceApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/add_device', data: param })
}

//修改
export const updateKaoqingDeviceApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/user/checkin/update_device', data: param })
}

//删除
export const delKaoqingDeviceApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/del_device', data: param })
}

//查询
interface SearchParam {
    page: number,
    count: number
}
export const getKaoqingDeviceListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/user/checkin/list_device',params })
}

//同步考勤机用户
//修改
export const updateKaoqingDeviceUserApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/data_update', data: param })
}

//同步考勤机用户打卡
//修改
export const updateKaoqingDeviceUserKaoqingApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/data_query', data: param })
}

//查询考勤人员列表
export const getKaoqingUserListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/user/checkin/list_user',params })
}

//删除用户指纹
export const delUserFingerInfoApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/del_data_finger', data: param })
}
//删除用户人脸
export const delUserFaceInfoApi = (param): Promise<IResponse> => {
    return request.post({ url: '/user/checkin/del_data_face', data: param })
}

//---------------------考勤方案--------------------------
//新增
export const addKaoqingConfigApi = (param): Promise<IResponse> => {
    return request.post({ url: '/checkin/program/add', data: param })
}
//修改
export const updateKaoqingConfigApi = (param): Promise<IResponse> => {
    param.ids = [param.id]
    return request.post({ url: '/checkin/program/update', data: param })
}
//删除
export const delKaoqingConfigApi = (param): Promise<IResponse> => {
    return request.post({ url: '/checkin/program/del', data: param })
}
//查询
interface SearchParam {
    page: number,
    count: number
}
export const getKaoqingConfigListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/checkin/program/list',params })
}
export const getKaoqingConfigInfoApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/checkin/program/info',params })
}

//--------------------------考勤流水----------------------------
//新增
export const addKaoqingWaterApi = (param): Promise<IResponse> => {
    return request.post({ url: '/checkin/water/add', data: param })
}
//修改
export const updateKaoqingWaterApi = (param): Promise<IResponse> => {
    return request.post({ url: '/checkin/water/update', data: param })
}
//删除
export const delKaoqingWaterApi = (param): Promise<IResponse> => {
    return request.post({ url: '/checkin/water/del', data: param })
}
//查询考勤流水
export const getKaoqingWaterListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/checkin/water/list',params })
}
//查询考勤日报
export const getKaoqingDayListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/checkin/water/list_daily',params })
}
//查询考勤日报
export const getKaoqingMounthListApi = (params: SearchParam): Promise<IResponse<string[]>> => {
    return request.get({ url: '/checkin/water/list_monthly',params })
}




//考勤日报查询导出
export const exportKaoqingDayListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/checkin/water/export_daily', params:restParams })
}
//考勤月报查询导出
export const exportKaoqingMounthListApi = (params:SearchParam): Promise<IResponse> => {
    const { reset, ...restParams } = params;
    return request.get({ url: '/checkin/water/export_monthly', params:restParams })
}