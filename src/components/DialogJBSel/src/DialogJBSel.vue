<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElDatePicker,ElRadioGroup,ElRadioButton,ElCheckbox,ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getOvertimeListApi, getUserListApi } from '@/api/usermanage'
import { setHotPoint } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const overtimeData = reactive([]) //人员列表
//定义搜索条件
const defaultCondition = {
    年份: '2024',
    月份: '01月',
    确认情况:'',
    overtime_man_name: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  param: any //额外传入参数
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    const currentDate = new Date();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    searchCondition.月份 = `${currentMonth}月`;
    arraySel.splice(0, arraySel.length)
    getOvertimeList()
  }
})


//获取明细
const getOvertimeList = async (page = 1) => {
    // Object.assign(dayData, generateDaysForCurrentMonth(Number(searchCondition.年份), Number(searchCondition.月份.replace('月', ''))));
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)
    tmp.月份 = tmp.月份.replace('月', '')
    const ret = await getOvertimeListApi(tmp)
    if (ret) {
        overtimeData.splice(0, overtimeData.length, ...ret.data);
        totleCount.value = ret.count
    }
}

//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getOvertimeList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getOvertimeList(val)
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  console.log(currentRow.value)
  if (currentRow.value?.id == undefined) {
    ElMessage.warning('请选择一条数据')
    return
  }

  closeDlg()
  //返回数据给上层
  emit('onSubmit', [currentRow.value])

}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const arraySel = reactive([])
const onChangeCheck = (row,value)=>{
  console.log(row,value)
  if(value)
  {
    arraySel.push(row)
  }
  else
  {
    const index = arraySel.findIndex(item=>item.id==row.id)
    arraySel.splice(index,1)
  }
  console.log(arraySel)
}

//提交选择的数据
const onSubmitMul = () => {
  console.log(currentRow.value)
  if (arraySel.length <= 0) {
    if (currentRow.value != null) {
      arraySel.push(currentRow.value)
    }
    else {
      ElMessage.warning('请选择一条数据')
      return
    }

  }

  closeDlg()
  //返回数据给上层
  emit('onSubmit', [...arraySel])

  // setHotPoint(
  //   {
  //     path_name: '/erp/user/list',
  //     hot_id: currentRow.value?.id
  //   }
  // )
}
</script>

<template>
  <Dialog v-model="show" title="选择加班调休记录" max-height="60vh" @close="closeDlg">
    <div class="flex flex-col justify-center items-center mb-3">
        <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">年份</div>
            <el-date-picker @change="getOvertimeList" @calendar-change="getOvertimeList" :clearable="false"
                v-model="searchCondition.年份" type="year" placeholder="Pick a year" format="YYYY"
                value-format="YYYY" />
        </div>

        <el-radio-group @change="(value) => { getOvertimeList() }" v-model="searchCondition.月份">
            <el-radio-button v-for="month in 12" :key="month" :label="`${String(month).padStart(2, '0')}月`"
                :value="month" />
        </el-radio-group>
    </div>
    <el-table ref="tableRef" :data="overtimeData" style="width: 100%; margin-bottom: 20px;color: #666;" row-key="guuid"
      border highlight-current-row @current-change="handleCurrentChange" @row-dblclick="onSubmit"
      header-cell-class-name="tableHeader">

      <el-table-column align="center" show-overflow-tooltip fixed prop="name" :label="'-'" width="40">
        <template #default="scope">
          <el-checkbox v-model="scope.row.check" label="" size="large" @change="onChangeCheck(scope.row,$event)"/>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip fixed prop="id" :label="t('userTable.id')" width="80" />
      <el-table-column show-overflow-tooltip fixed prop="username" :label="t('userTable.userid')" />
      <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('userTable.username')" />
    </el-table>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmitMul" class="mr-4">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  // font-weight: 400;
}
</style>