<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { ref, watch, reactive } from 'vue';
import { ElCheckbox, ElButton, ElTable, ElTableColumn, ElPagination, ElMessage, ElInput } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import { getUserListApi } from '@/api/usermanage'
import { setHotPoint } from '@/api/extra';

const { t } = useI18n()
const show = ref(false);

const userData = reactive([]) //人员列表
//查询条件
const searchCondition = reactive({
  usrname_or_resname: '',
  部门: '',
  page: 1,
  count: 50
})
//总条数
const totleCount = ref(0)

//当前选中行
const currentRow = ref(null)
const handleCurrentChange = (val) => {
  currentRow.value = val
}

const props = defineProps<{
  show: boolean,
  title: string,
  param: any //额外传入参数
}>()
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
  if (val) {
    show.value = true
    arraySel.splice(0, arraySel.length)
    getUserList()
  }
})
// //监听show的显示设置 同步到外部
// watch(() => show.value, async(val) => {
//   if (!val) {
//     closeDlg()
//   }
// })



//查询用户列表
const getUserList = async (page = 1) => {
  searchCondition.page = page
  const ret = await getUserListApi(searchCondition);
  if (ret) {
    userData.splice(0, userData.length, ...ret.data);
    totleCount.value = parseInt(ret.count)
  }
}
//page控件发生切换
const handlePageSizeChange = (val: number) => {
  getUserList()
}
//page控件发生切换
const handlePageCurrentChange = (val: number) => {
  getUserList(val)
}

//定义通知
const emit = defineEmits(['update:show', 'onSubmit'])
//提交选择的数据
const onSubmit = () => {
  console.log(currentRow.value)
  if (currentRow.value?.id == undefined) {
    ElMessage.warning('请选择一条数据')
    return
  }

  closeDlg()
  //返回数据给上层
  emit('onSubmit', [currentRow.value])

  // setHotPoint(
  //   {
  //     path_name: '/erp/user/list',
  //     hot_id: currentRow.value?.id
  //   }
  // )
}
//关闭
const closeDlg = () => {
  emit('update:show', false)
  show.value = false
}

const arraySel = reactive([])
const onChangeCheck = (row, value) => {
  console.log(row, value)
  if (value) {
    arraySel.push(row)
  }
  else {
    const index = arraySel.findIndex(item => item.id == row.id)
    arraySel.splice(index, 1)
  }
  console.log(arraySel)
}

//提交选择的数据
const onSubmitMul = () => {
  console.log(currentRow.value)
  if (arraySel.length <= 0) {
    if (currentRow.value != null) {
      arraySel.push(currentRow.value)
    }
    else {
      ElMessage.warning('请选择一条数据')
      return
    }

  }

  closeDlg()
  //返回数据给上层
  emit('onSubmit', [...arraySel])

  // setHotPoint(
  //   {
  //     path_name: '/erp/user/list',
  //     hot_id: currentRow.value?.id
  //   }
  // )
}

const onSelectAll = (param) => {
  console.log(param)
  arraySel.splice(0, arraySel.length)
  if (param.length > 0) {
    param.forEach(item => {
      arraySel.push(item)
    })
  }
  userData.forEach(item => {
    item.check = param.length > 0
  })
}
</script>

<template>
  <Dialog v-model="show" :title="title" max-height="60vh" @close="closeDlg">
    <div class="mb-5 flex justify-center">
      <el-input style="width: 300px;margin-right: 10px;" placeholder="请输入部门名称" v-model="searchCondition.部门" />
      <el-input style="width: 300px;margin-right: 10px;" placeholder="请输入员工名称或编号"
        v-model="searchCondition.usrname_or_resname" />
      <el-button type="primary" @click="getUserList(1)">{{ t('button.search') }}</el-button>
    </div>
    <el-table ref="tableRef" :data="userData" style="width: 100%; margin-bottom: 20px;color: #666;" row-key="guuid"
      border highlight-current-row @current-change="handleCurrentChange" @row-dblclick="onSubmit"
      @select-all="onSelectAll">
      header-cell-class-name="tableHeader">

      <el-table-column type="selection" align="center" show-overflow-tooltip fixed prop="name" :label="'-'" width="40">
        <template #default="scope">
          <el-checkbox v-model="scope.row.check" label="" size="large" @change="onChangeCheck(scope.row, $event)" />
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip fixed prop="id" :label="t('userTable.id')" width="80" />
      <el-table-column show-overflow-tooltip fixed prop="username" :label="t('userTable.userid')" />
      <el-table-column show-overflow-tooltip fixed prop="resident_name" :label="t('userTable.username')" />
    </el-table>
    <el-pagination class="flex justify-end" v-model:current-page="searchCondition.page"
      v-model:page-size="searchCondition.count" :page-sizes="[10, 50, 100, 300]" :background="true"
      layout="sizes, prev, pager, next" :total="totleCount" @size-change="handlePageSizeChange"
      @current-change="handlePageCurrentChange" />
    <template #footer>
      <div class="flex justify-end">
        <ElButton type="primary" @click="onSubmitMul" class="mr-4">
          {{ t('msg.ok') }}
        </ElButton>
        <ElButton @click="closeDlg">{{ t('common.channel') }}</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  // font-weight: 400;
}
</style>