<script setup lang="ts">
import { ElBadge,ElButton,ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useCache } from '@/hooks/web/useCache'
import { resetRouter } from '@/router'
import { useRouter } from 'vue-router'
import { loginOutApi } from '@/api/login'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { reactive, ref, onMounted, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { DialogUserInfo } from "@/components/DialogUserInfo";
import { DialogImportTask } from "@/components/DialogImportTask";
import { getManFreshApi, getMessageListApi, updateMessageApi } from '@/api/tj'
import { globalState } from '@/api/tool/globalState'
import { cloneDeep } from 'lodash-es'

const tagsViewStore = useTagsViewStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('user-info')
const { t } = useI18n()
const { wsCache } = useCache()
const { replace } = useRouter()
const appStore = useAppStore()

const userinfo = reactive({})
onMounted(async () => {
  const info = wsCache.get(appStore.getUserInfo)
  Object.assign(userinfo, info)

  // await getManFresh()
  await getMessageList()
})

const bShowImport = ref(false)
const showImportDlg = () => {
  bShowImport.value = true
}
const messageLength = ref(0)
const messageData = reactive([])
const getMessageList = async () => {
  let ret = await getMessageListApi({
    状态: '未读',
    用户编号: userinfo.id,
    page: 1,
    count:1000
  })
  if(ret) {
    console.log(ret) 
    messageLength.value = ret.data.length
    Object.assign(messageData, ret.data)
  }
}

const getManFresh = async() => {
  let ret = await getManFreshApi({})
  if (ret) {
    console.log(ret)
  }
}

const onShowMsg = async (item) => {
  console.log(item)
  globalState.notification = item;
  //修改已读
  let tmp = cloneDeep(item)
  tmp.ids = [item.id]
  tmp.msg_status = '已读'
  let ret = await updateMessageApi(tmp)
  if(ret) {
    await getMessageList()
  }
}

watch(() => globalState.updateMessage, async (param) => {
  if (param) {
    getMessageList()
  }
  globalState.updateMessage = null;
})
</script>

<template>
  <div>
    <ElDropdown :class="prefixCls" trigger="click" class="mr-5" :max-height="500">
      <div class="flex items-center">
        <el-badge :value="messageLength" :max="99" class="item" >
          <ElButton title='任务' size='small' type="success">
              <Icon  icon="tabler:message" />
              消息
          </ElButton>
        </el-badge>
      </div>
      <template #dropdown>
        <ElDropdownMenu>
          <ElDropdownItem divided v-for="item in messageData" :key="item.id" @click="onShowMsg(item)">
            <span class="text-red-500">{{ '【 '+item.msg_status+' 】' }}</span><div >{{item.msg_text }}</div>
          </ElDropdownItem>
        </ElDropdownMenu>
      </template>

      
    </ElDropdown>
    <ElDropdown :class="prefixCls" trigger="click">
      <div class="flex items-center">
        <ElButton title='任务' size='small' type="info" class='mr-5'>
            <Icon  icon="mdi:import-export-bold" />
            任务
        </ElButton>
      </div>
      <template #dropdown>
        <ElDropdownMenu>
          <ElDropdownItem divided>
            <div @click="showImportDlg">导入任务</div>
          </ElDropdownItem>
        </ElDropdownMenu>
      </template>

      
    </ElDropdown>
    <DialogImportTask v-model:show='bShowImport' mod='' cmd="" /> 
  </div>

</template>
