<script setup lang="ts">
import {ref, watch} from 'vue'
import { ElMenu,ElMenuItem } from 'element-plus'
import { useCache } from '@/hooks/web/useCache'
import { useI18n } from '@/hooks/web/useI18n'
import router, {getAbilityTabMap} from '@/router'
import { usePermissionStore } from '@/store/modules/permission'
import { useRouter } from 'vue-router'
import { RouteRecordRaw } from 'vue-router'
import { closeOneTagByPath } from '@/api/tool'
import { globalState } from '@/api/tool/globalState'


const { t } = useI18n()

const { wsCache } = useCache()
const permissionStore = usePermissionStore()
const { addRoute,push,currentRoute,removeRoute } = useRouter()
const abilityTabMap = getAbilityTabMap()

const handleSelect = async (key: string) => {
  console.log(key)
  //切换tab后同时更新路由
  await reFreshMenu(key,abilityTabMap)
  console.log('切换大路由')
  // closeOneTagByPath(permissionStore.addRouters[0].path)
  // closeOneTagByPath(permissionStore.addRouters[0].redirect)
  //调跳转到路由的第一个页面
  push({ path:permissionStore.addRouters[0].path })
}

const getMap = (map)=>{

  //用ROLE的角色设置过滤真实的TAB
  const roleMap = wsCache.get('roleRouters')
  let arrayRet:any[] = []
  for(let item of map)
  {
    let bFind = false
    for(let one of roleMap)
    {
      if(one["name"] == item.name)
      {
        bFind = true
        break
      }
    } 
    if(bFind) 
    {
      arrayRet.push(item)
    }
  }

  return arrayRet
}
 
//console.log( getAbilityTabMap())    

const tabMap = getMap(abilityTabMap)

 

const reFreshMenu = async (tabName:string,abilityTabMap)=>{
  console.log("进入刷新")
  const { wsCache } = useCache()
  const routersMap =  wsCache.get('roleRouters')

  //找到对应的权限列表
  let srcMap:AppRouteRecordRaw[] = []
  for(let i=0;i<abilityTabMap.length;i++)
  {
    if(abilityTabMap[i].name == tabName)
    {
      srcMap = abilityTabMap[i].routes
      break
    }
  }

  //找到对应的用户角色权限
  let roleMap = []
  for(let item of routersMap)
  {
    if(item["name"] == tabName)
    {
      roleMap = item["routes"]
      break
    }
  }


  await permissionStore.generateRoutes(srcMap, roleMap).catch(() => {})

  // router.getRoutes().forEach((route) => {
  //   if(!['/','/redirect','/login','/404'].includes(route.path as string))
  //     removeRoute(route.name as string)
  // })

 
  permissionStore.getAddRouters.forEach((route) => {
    addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  })
  permissionStore.setIsAddRouters(true)
}

const activeIndex = ref('');
//根据当前路由确定TAB当前选中位置
const roleMap = wsCache.get('roleRouters')
for(let one of roleMap)
{
  let bFind = false
  for(let item of one["routes"])
  {    
    if((item == currentRoute.value.path)) //找到
    {
      activeIndex.value = one.name
      bFind = true
      break
    }

  }
  if(bFind)
  {
    break
  }
}


watch(() => globalState.notification, async (param) => {
  console.log('来了')
  if (param) {
    //销售订单
    if (param.src_obj_name == 'sell_order') {
      await reFreshMenu('saleRouterMap', abilityTabMap)
      activeIndex.value = 'saleRouterMap'
      push({
        path: '/salemanage/salemanage',
        query:{
            id:'',
            sell_order_num:param.src_attr_value
        },    
      })
    }
    else if(param.src_obj_name =='buy_order') {
      await reFreshMenu('procureRouterMap', abilityTabMap)
      activeIndex.value = 'procureRouterMap'
      push({
        path: '/purchasemanage/purchasemanage',
        query:{
            buy_order_num:param.src_attr_value
        },    
      })
    }
    else if(param.src_obj_name =="oem_order") {
      await reFreshMenu('oemRouterMap', abilityTabMap)
      activeIndex.value = 'oemRouterMap'
      push({
        path: '/oemmanage/oemorderlist',
        query:{
          oem_order_num:param.src_attr_value
        },    
      })
    }
  }
  globalState.notification = null;
});

</script>

<template>
  <div class=" bg-[#545c64] h-[48%] w-[100%] flex items-center ">
    <ElMenu
    :default-active="activeIndex"
    class="ElMenu-demo h-[100%] w-[100%]"
    mode="horizontal"
    background-color="#545c64"
    text-color="#fff"
    active-text-color="#00BA80"
    @select="handleSelect"
  >
    <ElMenuItem class="!ml-4" v-for="(item,index) in tabMap" :key="index" :index="item.name">
      {{ t(item.title) }}
    </ElMenuItem>
  </ElMenu>
  </div>
</template>

<style lang="less" scoped>
.el-menu--horizontal {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: none; 
    border-right: none;
}
</style>
