<script setup lang="ts">
import {  computed, ref} from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { ElImage } from 'element-plus'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()
const title = computed(() => appStore.getTitle)

const onJumpUrl = () => {
  let token = JSON.parse(sessionStorage.getItem('Token')||'{}')
  let userInfo = sessionStorage.getItem('userInfo')
  window.open('http://admin.mbtoys.net:8088/#/bigScreen?Token='+JSON.parse(token.v)+'&userInfo='+userInfo, '_blank'); 
}
const mmmode = ref('')
mmmode.value = import.meta.env.VITE_TEST_MODE
console.log('====',mmmode.value)
</script>

<template>
  <div class=" bg-light-50 h-[52%] flex items-center" :class="mmmode" style="background: rgba(240, 242, 245, 1);">{{ mmmode }}
    <router-link
      :class="[
        prefixCls,
        true ? `${prefixCls}__Top` : '',
        'flex !h-[var(--logo-height)] items-center cursor-pointer pl-16px relative',
        'dark:bg-[var(--el-bg-color)]'
      ]"
      to="/"
    >
      <img
        v-if="mmmode==undefined"
        src="@/assets/imgs/wenbologo.png"
        class=" h-[calc(var(--logo-height)-20px)]"
      />
      <div
        :class="[
          'ml-16px text-16px font-700',
        ''
        ]"
      >
        {{ title }}
      </div>
    </router-link>
    <div class="ml-auto mr-6 flex" v-if="mmmode==undefined">
      <!-- <el-image class="xiangpian w-[50px] h-[50px] mt-4 cursor-pointer" title="大屏监控" src="/computer.jpg"  @click="onJumpUrl"/> -->
      <Icon icon="ep:data-analysis" :size="24"  @click="onJumpUrl"  class="xiangpian w-[50px] h-[50px] mt-4 cursor-pointer"/>
    </div>
  </div>
</template>

<style lang="less" scoped>
.test{
  background-color: red !important;
}
.demo{
  background-color: #cbffd0 !important;
}
</style>