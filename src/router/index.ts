import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import {  type App } from 'vue'
import { Layout, getParentLayout } from '@/utils/routerHelper'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()


export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/customermanage/percustomermanage',
    name: 'Root',
    meta: {
      hidden: true
    }
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirecta',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/print',
    component: () => import('@/views/PrintManage/print.vue'),
    name: 'print',
    meta: {
      hidden: true,
      title: 'print',
      noTagsView: true
    }
  }
]

export const asyncRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/analysis',
    name: 'Dashboard',
    meta: {
      title: t('router.dashboard'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'analysis',
        component: () => import('@/views/Dashboard/Analysis.vue'),
        name: 'Analysis',
        meta: {
          title: t('router.analysis'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'workplace',
        component: () => import('@/views/Dashboard/Workplace.vue'),
        name: 'Workplace',
        meta: {
          title: t('router.workplace'),
          noCache: true
        }
      }
    ]
  },
  {
    path: '/external-link',
    component: Layout,
    meta: {},
    name: 'ExternalLink',
    children: [
      {
        path: 'https://element-plus-admin-doc.cn/',
        name: 'DocumentLink',
        meta: {
          title: t('router.document'),
          icon: 'clarity:document-solid'
        }
      }
    ]
  },
  {
    path: '/guide',
    component: Layout,
    name: 'Guide',
    meta: {},
    children: [
      {
        path: 'index',
        component: () => import('@/views/Guide/Guide.vue'),
        name: 'GuideDemo',
        meta: {
          title: t('router.guide'),
          icon: 'cib:telegram-plane'
        }
      }
    ]
  },
  {
    path: '/components',
    component: Layout,
    name: 'ComponentsDemo',
    meta: {
      title: t('router.component'),
      icon: 'bx:bxs-component',
      alwaysShow: true
    },
    children: [
      {
        path: 'form',
        component: getParentLayout(),
        redirect: '/components/form/default-form',
        name: 'Form',
        meta: {
          title: t('router.form'),
          alwaysShow: true
        },
        children: [
          {
            path: 'default-form',
            component: () => import('@/views/Components/Form/DefaultForm.vue'),
            name: 'DefaultForm',
            meta: {
              title: t('router.defaultForm')
            }
          },
          {
            path: 'use-form',
            component: () => import('@/views/Components/Form/UseFormDemo.vue'),
            name: 'UseForm',
            meta: {
              title: 'UseForm'
            }
          },
          {
            path: 'ref-form',
            component: () => import('@/views/Components/Form/RefForm.vue'),
            name: 'RefForm',
            meta: {
              title: 'RefForm'
            }
          }
        ]
      },
      {
        path: 'table',
        component: getParentLayout(),
        redirect: '/components/table/default-table',
        name: 'TableDemo',
        meta: {
          title: t('router.table'),
          alwaysShow: true
        },
        children: [
          {
            path: 'default-table',
            component: () => import('@/views/Components/Table/DefaultTable.vue'),
            name: 'DefaultTable',
            meta: {
              title: t('router.defaultTable')
            }
          },
          {
            path: 'use-table',
            component: () => import('@/views/Components/Table/UseTableDemo.vue'),
            name: 'UseTable',
            meta: {
              title: 'UseTable'
            }
          },
          {
            path: 'ref-table',
            component: () => import('@/views/Components/Table/RefTable.vue'),
            name: 'RefTable',
            meta: {
              title: 'RefTable'
            }
          }
        ]
      },
      {
        path: 'editor-demo',
        component: getParentLayout(),
        redirect: '/components/editor-demo/editor',
        name: 'EditorDemo',
        meta: {
          title: t('router.editor'),
          alwaysShow: true
        },
        children: [
          {
            path: 'editor',
            component: () => import('@/views/Components/Editor/Editor.vue'),
            name: 'Editor',
            meta: {
              title: t('router.richText')
            }
          }
        ]
      },
      {
        path: 'search',
        component: () => import('@/views/Components/Search.vue'),
        name: 'Search',
        meta: {
          title: t('router.search')
        }
      },
      {
        path: 'descriptions',
        component: () => import('@/views/Components/Descriptions.vue'),
        name: 'Descriptions',
        meta: {
          title: t('router.descriptions')
        }
      },
      {
        path: 'image-viewer',
        component: () => import('@/views/Components/ImageViewer.vue'),
        name: 'ImageViewer',
        meta: {
          title: t('router.imageViewer')
        }
      },
      {
        path: 'dialog',
        component: () => import('@/views/Components/Dialog.vue'),
        name: 'Dialog',
        meta: {
          title: t('router.dialog')
        }
      },
      {
        path: 'icon',
        component: () => import('@/views/Components/Icon.vue'),
        name: 'Icon',
        meta: {
          title: t('router.icon')
        }
      },
      {
        path: 'echart',
        component: () => import('@/views/Components/Echart.vue'),
        name: 'Echart',
        meta: {
          title: t('router.echart')
        }
      },
      {
        path: 'count-to',
        component: () => import('@/views/Components/CountTo.vue'),
        name: 'CountTo',
        meta: {
          title: t('router.countTo')
        }
      },
      {
        path: 'qrcode',
        component: () => import('@/views/Components/Qrcode.vue'),
        name: 'Qrcode',
        meta: {
          title: t('router.qrcode')
        }
      },
      {
        path: 'highlight',
        component: () => import('@/views/Components/Highlight.vue'),
        name: 'Highlight',
        meta: {
          title: t('router.highlight')
        }
      },
      {
        path: 'infotip',
        component: () => import('@/views/Components/Infotip.vue'),
        name: 'Infotip',
        meta: {
          title: t('router.infotip')
        }
      },
      {
        path: 'input-password',
        component: () => import('@/views/Components/InputPassword.vue'),
        name: 'InputPassword',
        meta: {
          title: t('router.inputPassword')
        }
      },
      {
        path: 'sticky',
        component: () => import('@/views/Components/Sticky.vue'),
        name: 'Sticky',
        meta: {
          title: t('router.sticky')
        }
      }
    ]
  },
  {
    path: '/hooks',
    component: Layout,
    redirect: '/hooks/useWatermark',
    name: 'Hooks',
    meta: {
      title: 'hooks',
      icon: 'ic:outline-webhook',
      alwaysShow: true
    },
    children: [
      {
        path: 'useWatermark',
        component: () => import('@/views/hooks/useWatermark.vue'),
        name: 'UseWatermark',
        meta: {
          title: 'useWatermark'
        }
      },
      {
        path: 'useCrudSchemas',
        component: () => import('@/views/hooks/useCrudSchemas.vue'),
        name: 'UseCrudSchemas',
        meta: {
          title: 'useCrudSchemas'
        }
      }
    ]
  },
  {
    path: '/level',
    component: Layout,
    redirect: '/level/menu1/menu1-1/menu1-1-1',
    name: 'Level',
    meta: {
      title: t('router.level'),
      icon: 'carbon:skill-level-advanced'
    },
    children: [
      {
        path: 'menu1',
        name: 'Menu1',
        component: getParentLayout(),
        redirect: '/level/menu1/menu1-1/menu1-1-1',
        meta: {
          title: t('router.menu1')
        },
        children: [
          {
            path: 'menu1-1',
            name: 'Menu11',
            component: getParentLayout(),
            redirect: '/level/menu1/menu1-1/menu1-1-1',
            meta: {
              title: t('router.menu11'),
              alwaysShow: true
            },
            children: [
              {
                path: 'menu1-1-1',
                name: 'Menu111',
                component: () => import('@/views/Level/Menu111.vue'),
                meta: {
                  title: t('router.menu111')
                }
              }
            ]
          },
          {
            path: 'menu1-2',
            name: 'Menu12',
            component: () => import('@/views/Level/Menu12.vue'),
            meta: {
              title: t('router.menu12')
            }
          }
        ]
      },
      {
        path: 'menu2',
        name: 'Menu2',
        component: () => import('@/views/Level/Menu2.vue'),
        meta: {
          title: t('router.menu2')
        }
      }
    ]
  },
  {
    path: '/example',
    component: Layout,
    redirect: '/example/example-dialog',
    name: 'Example',
    meta: {
      title: t('router.example'),
      icon: 'ep:management',
      alwaysShow: true
    },
    children: [
      {
        path: 'example-dialog',
        component: () => import('@/views/Example/Dialog/ExampleDialog.vue'),
        name: 'ExampleDialog',
        meta: {
          title: t('router.exampleDialog')
        }
      },
      {
        path: 'example-page',
        component: () => import('@/views/Example/Page/ExamplePage.vue'),
        name: 'ExamplePage',
        meta: {
          title: t('router.examplePage')
        }
      },
      {
        path: 'example-add',
        component: () => import('@/views/Example/Page/ExampleAdd.vue'),
        name: 'ExampleAdd',
        meta: {
          title: t('router.exampleAdd'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/example/example-page'
        }
      },
      {
        path: 'example-edit',
        component: () => import('@/views/Example/Page/ExampleEdit.vue'),
        name: 'ExampleEdit',
        meta: {
          title: t('router.exampleEdit'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/example/example-page'
        }
      },
      {
        path: 'example-detail',
        component: () => import('@/views/Example/Page/ExampleDetail.vue'),
        name: 'ExampleDetail',
        meta: {
          title: t('router.exampleDetail'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/example/example-page'
        }
      }
    ]
  },
  {
    path: '/error',
    component: Layout,
    redirect: '/error/404',
    name: 'Error',
    meta: {
      title: t('router.errorPage'),
      icon: 'ci:error',
      alwaysShow: true
    },
    children: [
      {
        path: '404-demo',
        component: () => import('@/views/Error/404.vue'),
        name: '404Demo',
        meta: {
          title: '404'
        }
      },
      {
        path: '403-demo',
        component: () => import('@/views/Error/403.vue'),
        name: '403Demo',
        meta: {
          title: '403'
        }
      },
      {
        path: '500-demo',
        component: () => import('@/views/Error/500.vue'),
        name: '500Demo',
        meta: {
          title: '500'
        }
      }
    ]
  }
  // {
  //   path: '/authorization',
  //   component: Layout,
  //   redirect: '/authorization/user',
  //   name: 'Authorization',
  //   meta: {
  //     title: t('router.authorization'),
  //     icon: 'eos-icons:role-binding',
  //     alwaysShow: true
  //   },
  //   children: [
  //     {
  //       path: 'user',
  //       component: () => import('@/views/Authorization/User.vue'),
  //       name: 'User',
  //       meta: {
  //         title: t('router.user')
  //       }
  //     },
  //     {
  //       path: 'role',
  //       component: () => import('@/views/Authorization/Role.vue'),
  //       name: 'Role',
  //       meta: {
  //         title: t('router.role')
  //       }
  //     }
  //   ]
  // }
]

//定义每个主功能下的子功能路由
//客户管理
export const customerRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/customermanage',
    component: Layout,
    redirect: '/customermanage/percustomermanage',
    name: 'CustomerManageMin',
    meta: {
      title: t('customer.customermgr'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'客户编码显示'},
      {name:'客户名称显示'}
    ],
    children: [
      {
        path: 'percustomermanage',
        component: () => import('@/views/CustomerManage/PerCustomerManage.vue'),
        name: 'PerCustomerManage',
        meta: {
          title: t('customer.perCustomerMgr'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'comcustomermanage',
        component: () => import('@/views/CustomerManage/ComCustomerManage.vue'),
        name: 'ComCustomerManage',
        meta: {
          title: t('customer.comCustomerMgr'),
          noCache: true
        }
      },
      {
        path: 'addcustomer',
        component: () => import('@/views/CustomerManage/AddCustomer.vue'),
        name: 'AddCustomer',
        meta: {
          title: t('customer.addPerCustomer'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/customermanage/percustomermanage'
        }
      },
      {
        path: 'addcorpcustomer',
        component: () => import('@/views/CustomerManage/AddCorpCustomer.vue'),
        name: 'AddCorpCustomer',
        meta: {
          title: t('customer.addComCustomer'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/customermanage/comcustomermanage'
        }
      },
    ]
  }
]

//销售管理
export const saleRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/salemanage',
    component: Layout,
    redirect: '/salemanage/salemanage',
    name: 'SaleManageMain',
    meta: {
      title: t('router.saleRouterMap'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'销售订单明细查看'},
      {name:'销售订单新增'},
      {name:'销售订单修改'},
      {name:'销售订单价格显示'},
      {name:'销售订单删除'},
      {name:'产品销售价查看'},
      {name:'销售发货单显示价格'},
      {name:'销售退货单显示价格'},
      {name:'销售显示关闭订单'},
      {name:'销售图片查看'},
      {name:'销售文件查看'},
      {name:'销售备料'},
    ],
    children: [
      {
        path: 'salemanage',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'SaleManage',
        meta: {
          title: t('sale.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'saleypmanage',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'SaleYPManage',
        meta: {
          title: t('sale.yplist'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'createsalecheck',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'CreateSaleCheck',
        meta: {
          title: t('sale.list_CreateCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'pmcsalecheck',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'PMCSaleCheck',
        meta: {
          title: t('sale.list_PMCCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'productmgrsalecheck',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'ProductMgrSaleCheck',
        meta: {
          title: t('sale.list_ProductCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'projectmgrsalecheck',
        component: () => import('@/views/SaleManage/SaleManage.vue'),
        name: 'ProjectMgrSaleCheck',
        meta: {
          title: t('sale.list_ProjectCheck'),
          noCache: false,
          affix: true
        }
      },



      {
        path: 'addsale',
        component: () => import('@/views/SaleManage/AddSale.vue'),
        name: 'AddSale',
        meta: {
          title: t('sale.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
        }
      },
      {
        path: 'saleoutlist',
        component: () => import('@/views/SaleManage/SaleOutList.vue'),
        name: 'SaleOutList',
        meta: {
          title: t('sale.out_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addsaleout',
        component: () => import('@/views/SaleManage/AddSaleOut.vue'),
        name: 'AddSaleOut',
        meta: {
          title: t('sale.add_out'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/saleoutlist'
        }
      },
      {
        path: 'lockstone',
        component: () => import('@/views/SaleManage/LockStone.vue'),
        name: 'LockStone',
        meta: {
          title: t('sale.lock'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/salemanage'
        }
      },
      {
        path: 'salereturnlist',
        component: () => import('@/views/SaleManage/SaleReturnList.vue'),
        name: 'SaleReturnList',
        meta: {
          title: t('sale.return_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addsalereturn',
        component: () => import('@/views/SaleManage/AddSaleReturn.vue'),
        name: 'AddSaleReturn',
        meta: {
          title: t('sale.add_return'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/salemanage'
        }
      },
      {
        path: 'saledemand',
        component: () => import('@/views/SaleManage/SaleDemand.vue'),
        name: 'SaleDemand',
        meta: {
          title: t('saledemand.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addpurchase',
        component: () => import('@/views/PurchaseManage/AddPurchase.vue'),
        name: 'AddPurchase',
        meta: {
          title: t('purchase.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/saledemand'
        }
      },
      {
        path: 'addoemorder',
        component: () => import('@/views/OemManage/AddOemOrder.vue'),
        name: 'AddOemOrder',
        meta: {
          title: t('oem.add_order'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/saledemand'
        }
      },
      {
        path: 'showbomstruct',
        component: () => import('@/views/SaleManage/ShowBomStruct.vue'),
        name: 'showbomstruct',
        meta: {
          title: 'BOM结构图',
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/salemanage'
        }
      },
      // {
      //   path: 'dealtimelist',
      //   component: () => import('@/views/SaleManage/DealTimeList.vue'),
      //   name: 'DealTimeList',
      //   meta: {
      //     title: t('dealtime.list'),
      //     noCache: false,
      //     affix: true
      //   }
      // },
      {
        path: 'sellwlprepare',
        component: () => import('@/views/SaleManage/SellWLPrepare.vue'),
        name: 'SellWLPrepare',
        meta: {
          title: t('sale.prepare'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/salemanage/salemanage'
        }
      },
    ]
  },
  {
    path: '/presalemanage',
    component: Layout,
    redirect: '/presalemanage/presalemanage',
    name: 'PreSaleManageMain',
    meta: {
      title: t('presale.mgr'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'报价单明细查看'},
      {name:'报价单新增'},
      {name:'报价单修改'},
      {name:'报价单删除'}
    ],
    children: [
      {//给业务员看的
        path: 'presalemanage',
        component: () => import('@/views/SaleManage/PreSaleManage.vue'),
        name: 'PreSaleManage',
        meta: {
          title: t('presale.list'),
          noCache: true,
          affix: true,
        },
      },
      // { //给工程师看的
      //   path: 'projectsalemanage',
      //   component: () => import('@/views/SaleManage/PreSaleManage.vue'),
      //   name: 'ProjectSaleManage',
      //   meta: {
      //     title: t('presale.project_list'),
      //     noCache: true,
      //     affix: true,
      //   },
      // },
      { //给审核员看的
        path: 'reviewsalemanage',
        component: () => import('@/views/SaleManage/PreSaleManage.vue'),
        name: 'ReviewSaleManage',
        meta: {
          title: t('presale.review_list'),
          noCache: true,
          affix: true,
        },
      },
      {
        path: 'addpresale',
        component: () => import('@/views/SaleManage/AddPreSale.vue'),
        name: 'AddPreSale',
        meta: {
          title: t('presale.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/presalemanage/presalemanage'
        }
      },
      {
        path: 'presaledetail',
        component: () => import('@/views/SaleManage/PreSaleDetail.vue'),
        name: 'PreSaleDetail',
        meta: {
          title: t('presale.detail'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/presalemanage/presalemanage'
        }
      }      
    ],    
  },

  
  {
    path: '/ssmonthlycheck',
    component: Layout,
    redirect: '/ssmonthlycheck/sssellmonthlycheck',
    name: 'SSMonthlyCheck',
    meta: {
      title: '月结对账',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'sssellmonthlycheck',
        component: () => import('@/views/SaleManage/Fin/SSSellMonthlyCheck.vue'),
        name: 'SSSellMonthlyCheck',
        meta: {
          title: t('finance.sell_montylycheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'sssellmonthlycuslist',
        component: () => import('@/views/SaleManage/Fin/SSSellMontylyCusList.vue'),
        name: 'SSSellMontylyCusList',
        meta: {
          title: t('finance.cus_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/ssmonthlycheck/sssellmonthlycheck'
        },
      },
      {
        path: 'sssellmonthlycheckadd',
        component: () => import('@/views/SaleManage/Fin/SSSellMonthlyCheckAdd.vue'),
        name: 'SSSellMonthlyCheckAdd',
        meta: {
          title: t('finance.sell_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/ssmonthlycheck/sssellmonthlycheck'
        }
      },
      {
        path: 'sssellmonthlycheckinfo',
        component: () => import('@/views/SaleManage/Fin/SSSellMonthlyCheckInfo.vue'),
        name: 'SSSellMonthlyCheckInfo',
        meta: {
          title: t('finance.sell_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/ssmonthlycheck/sssellmonthlycheck'
        }
      },
      {
        path: 'sssellmonthlychecktj',
        component: () => import('@/views/SaleManage/Fin/SSSellMonthlyCheckTJ.vue'),
        name: 'SSSellMonthlyCheckTJ',
        meta: {
          title: t('finance.sell_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },

    ]
  },
  {
    path: '/sellreport',
    component: Layout,
    redirect: '/sellreport/sellreportall',
    name: 'SellReport',
    meta: {
      title: '统计报表',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'sellreportall',
        component: () => import('@/views/SaleManage/Report/SellReportAll.vue'),
        name: 'SellReportAll',
        meta: {
          title: t('report.sell_reportall'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'sellreportcus',
        component: () => import('@/views/SaleManage/Report/SellReportCus.vue'),
        name: 'SellReportCus',
        meta: {
          title: t('report.sell_cus'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'sellreportuser',
        component: () => import('@/views/SaleManage/Report/SellReportUser.vue'),
        name: 'SellReportUser',
        meta: {
          title: t('report.sell_user'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'selloutreportcus',
        component: () => import('@/views/SaleManage/Report/SellOutReportCus.vue'),
        name: 'SellOutReportCus',
        meta: {
          title: t('report.sell_out_cus'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'selloutreportuser',
        component: () => import('@/views/SaleManage/Report/SellOutReportUser.vue'),
        name: 'SellOutReportUser',
        meta: {
          title: t('report.sell_out_user'),
          noCache: false,
          affix: true
        }
      },
    ]
  },
  {
    path: '/sellvaluereport',
    component: Layout,
    redirect: '/sellvaluereport/sellvaluereportcus',
    name: 'SellValueReport',
    meta: {
      title: '利润报表',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'sellvaluereportcus',
        component: () => import('@/views/SaleManage/Report/SellValueReportCus.vue'),
        name: 'SellValueReportCus',
        meta: {
          title: t('report.sell_value_cus'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'sellvaluereportorder',
        component: () => import('@/views/SaleManage/Report/SellValueReportOrder.vue'),
        name: 'SellValueReportOrder',
        meta: {
          title: t('report.sell_value_order'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'sellvaluereportout',
        component: () => import('@/views/SaleManage/Report/SellValueReportOut.vue'),
        name: 'SellValueReportOut',
        meta: {
          title: t('report.sell_value_out'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'sellvaluereportuser',
        component: () => import('@/views/SaleManage/Report/SellValueReportUser.vue'),
        name: 'SellValueReportUser',
        meta: {
          title: t('report.sell_value_user'),
          noCache: true,
          affix: true
        }
      },
    ]
  }


]

//采购管理
export const procureRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/purchasemanage',
    component: Layout,
    redirect: '/purchasemanage/purchasemanage',
    name: 'PurchaseMainManage',
    meta: {
      title: t('purchase.manage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'采购订单明细查看'},
      {name:'采购订单新增'},
      {name:'采购订单修改'},
      {name:'采购订单价格显示'},
      {name:'采购质检单显示价格'},
      {name:'采购收货单显示价格'},
      {name:'采购入库显示价格'},
      {name:'采购退货单显示价格'},
      {name:'采购订单删除'},
      {name:'采购显示关闭订单'},
      {name:'供应商编码显示'},
      {name:'供应商名称显示'},
      {name:'采购图片查看'},
      {name:'采购文件查看'},
    ],
    children: [
      {
        path: 'purchasemanage',
        component: () => import('@/views/PurchaseManage/PurchaseManage.vue'),
        name: 'PurchaseManage',
        meta: {
          title: t('purchase.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'purchasecheck',
        component: () => import('@/views/PurchaseManage/PurchaseManage.vue'),
        name: 'PurchaseCheck',
        meta: {
          title: t('purchase.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addpurchase',
        component: () => import('@/views/PurchaseManage/AddPurchase.vue'),
        name: 'AddPurchase',
        meta: {
          title: t('purchase.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/purchasemanage/purchasemanage'
        }
      },
      {
        path: 'receiptlist',
        component: () => import('@/views/PurchaseManage/ReceiptList.vue'),
        name: 'ReceiptList',
        meta: {
          title: t('receipt.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addreceipt',
        component: () => import('@/views/PurchaseManage/AddReceipt.vue'),
        name: 'Addreceipt',
        meta: {
          title: t('receipt.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/purchasemanage/receiptlist'
        }
      },
      {
        path: 'qualitycheck',
        component: () => import('@/views/PurchaseManage/QualityCheck.vue'),
        name: 'QualityCheck',
        meta: {
          title: t('quality.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addqualitycheck',
        component: () => import('@/views/PurchaseManage/AddQualityCheck.vue'),
        name: 'AddQualityCheck',
        meta: {
          title: t('quality.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/purchasemanage/qualitycheck'
        }
      },
      {
        path: 'putinlist',
        component: () => import('@/views/PurchaseManage/PutinList.vue'),
        name: 'PutinList',
        meta: {
          title: t('putin.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'putinchecklist',
        component: () => import('@/views/PurchaseManage/PutinList.vue'),
        name: 'PutinCheckList',
        meta: {
          title: t('putin.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addputin',
        component: () => import('@/views/PurchaseManage/AddPutin.vue'),
        name: 'AddPutin',
        meta: {
          title: t('putin.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/purchasemanage/putinlist'
        }
      },
      {
        path: 'returnlist',
        component: () => import('@/views/PurchaseManage/ReturnList.vue'),
        name: 'ReturnList',
        meta: {
          title: t('return.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addreturn',
        component: () => import('@/views/PurchaseManage/AddReturn.vue'),
        name: 'AddReturn',
        meta: {
          title: t('return.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/purchasemanage/addreturn'
        }
      },
    ]
  },
  {
    path: '/suppliermanage',
    component: Layout,
    redirect: '/suppliermanage/suppliermanage',
    name: 'SupplierManageMain',
    meta: {
      title: t('purchase.supplier'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'suppliermanage',
        component: () => import('@/views/PurchaseManage/SupplierManage.vue'),
        name: 'SupplierManage',
        meta: {
          title: t('purchase.supplier_manage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addsupplier',
        component: () => import('@/views/PurchaseManage/AddSupplier.vue'),
        name: 'AddSupplier',
        meta: {
          title: t('supplier.add_supplier'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/suppliermanage/suppliermanage'
        }
      },
    ]
  },
  {
    path: '/bbmonthlycheck',
    component: Layout,
    redirect: '/bbmonthlycheck/bbsellmonthlycheck',
    name: 'BBMonthlyCheck',
    meta: {
      title: '月结对账',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [

      {
        path: 'bbbuymonthlycheck',
        component: () => import('@/views/PurchaseManage/Fin/BBBuyMonthlyCheck.vue'),
        name: 'BBBuyMonthlyCheck',
        meta: {
          title: t('finance.buy_montylycheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'bbbuymonthlycuslist',
        component: () => import('@/views/PurchaseManage/Fin/BBBuyMontylyCusList.vue'),
        name: 'BBBuyMontylyCusList',
        meta: {
          title: t('finance.supplier_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/bbmonthlycheck/bbbuymonthlycheck'
        },
      },
      {
        path: 'bbbuymonthlycheckadd',
        component: () => import('@/views/PurchaseManage/Fin/BBBuyMonthlyCheckAdd.vue'),
        name: 'BBBuyMonthlyCheckAdd',
        meta: {
          title: t('finance.buy_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/bbmonthlycheck/bbbuymonthlycheck'
        }
      },
      {
        path: 'bbbuymonthlycheckinfo',
        component: () => import('@/views/PurchaseManage/Fin/BBBuyMonthlyCheckInfo.vue'),
        name: 'BBBuyMonthlyCheckInfo',
        meta: {
          title: t('finance.buy_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/bbmonthlycheck/bbbuymonthlycheck'
        }
      },
      {
        path: 'bbbuymonthlychecktj',
        component: () => import('@/views/PurchaseManage/Fin/BBBuyMonthlyCheckTJ.vue'),
        name: 'BBBuyMonthlyCheckTJ',
        meta: {
          title: t('finance.buy_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'buypaymentapplication',
        component: () => import('@/views/PurchaseManage/BuyPaymentApplication.vue'),
        name: 'BuyPaymentApplication',
        meta: {
          title: t('finance.paymentapplication'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/bbmonthlycheck/bbbuymonthlycheck'
        }
      },
      {
        path: 'buypaymentapplicationadd',
        component: () => import('@/views/PurchaseManage/BuyPaymentApplicationAdd.vue'),
        name: 'BuyPaymentApplicationAdd',
        meta: {
          title: t('finance.paymentapplication_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/bbmonthlycheck/bbbuymonthlycheck'
        }
      },
    ]
  },
  {
    path: '/buyreport',
    component: Layout,
    redirect: '/buyreport/buyreportall',
    name: 'BuyReport',
    meta: {
      title: '统计报表',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'buyreportall',
        component: () => import('@/views/PurchaseManage/Report/BuyReportAll.vue'),
        name: 'BuyReportAll',
        meta: {
          title: t('report.buy_reportall'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'buyreportsupplier',
        component: () => import('@/views/PurchaseManage/Report/BuyReportSupplier.vue'),
        name: 'BuyReportSupplier',
        meta: {
          title: t('report.buy_supplier'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'buyreportin',
        component: () => import('@/views/PurchaseManage/Report/BuyReportIn.vue'),
        name: 'BuyReportIn',
        meta: {
          title: t('report.buy_in_supplier'),
          noCache: true,
          affix: true
        }
      },
    ]
  }
]

//工程管理
export const prjectRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/bommanage',
    component: Layout,
    redirect: '/bommanage/bommanage',
    name: 'bomManageMain',
    meta: {
      title: t('project_manage.bom_manage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'bommanage',
        component: () => import('@/views/BomManage/BomManage.vue'),
        name: 'BomManage',
        meta: {
          title: t('project_manage.bom_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'bommanageex',
        component: () => import('@/views/BomManage/BomManageEx.vue'),
        name: 'BomManageEx',
        meta: {
          title: t('project_manage.bom_list2'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
        }
      },
      {
        path: 'addbom',
        component: () => import('@/views/BomManage/AddBom.vue'),
        name: 'AddBom',
        meta: {
          title: t('project_manage.bom_set'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/bommanage/bommanage'
        }
      },
    ]
  },
    {
    path: '/processmanage',
    component: Layout,
    redirect: '/processmanage/processmanage',
    name: 'processManageMain',
    meta: {
      title: t('project_manage.process_manage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'processmanage',
        component: () => import('@/views/ProcessManage/ProcessManage.vue'),
        name: 'ProcessManage',
        meta: {
          title: t('project_manage.process_list'),
          noCache: true,
          affix: true
        }
     },
      // {
      //   path: 'addprocess',
      //   component: () => import('@/views/ProcessManage/AddProcess.vue'),
      //   name: 'AddProcess',
      //   meta: {
      //     title: t('project_manage.process_config'),
      //     noTagsView: true,
      //     noCache: true,
      //     hidden: true,
      //     canTo: true,
      //     activeMenu: '/bommanage/processmanage'
      //   }
      // },
    ]
  }
]

//系统设置
export const sysConfigRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/usermanage',
    component: Layout,
    redirect: '/usermanage/usermanage',
    name: 'sysManageMain',
    meta: {
      title: t('userManage.userManage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'usermanage',
        component: () => import('@/views/UserManage/UserManage.vue'),
        name: 'UserManage',
        meta: {
          title: t('userManage.userManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'deptmanage',
        component: () => import('@/views/UserManage/DeptManage.vue'),
        name: 'DeptManage',
        meta: {
          title: t('userManage.deptManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'adduser',
        component: () => import('@/views/UserManage/AddUser.vue'),
        name: 'AddUser',
        meta: {
          title: t('userTable.addUser'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/usermanage'
        }
      },
      {
        path: 'rolemanage',
        component: () => import('@/views/UserManage/RoleManage.vue'),
        name: 'RoleManage',
        meta: {
          title: t('userManage.roleManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addrole',
        component: () => import('@/views/UserManage/AddRole.vue'),
        name: 'AddRole',
        meta: {
          title: t('roleTable.addRole'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/rolemanage'
        }
      },
      {
        path: 'addroledata',
        component: () => import('@/views/UserManage/AddRoleData.vue'),
        name: 'AddRoleData',
        meta: {
          title: '角色数据权限',
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/usermanage/rolemanage'
        }
      }
    ]
  },
  {
    path: '/productmanage',
    component: Layout,
    redirect: '/productmanage/productmanage',
    name: 'ProductManageMain',
    meta: {
      title: t('product_manage.product_manage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'productmanage',
        component: () => import('@/views/ProductManage/ProductManage.vue'),
        name: 'ProductManage',
        meta: {
          title: t('product_manage.product_manage'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addproduct',
        component: () => import('@/views/ProductManage/AddProduct.vue'),
        name: 'AddProduct',
        meta: {
          title: t('product_manage.add_product'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/productmanage/productmanage'
        }
      },
      {
        path: 'productmodifymul',
        component: () => import('@/views/ProductManage/ProductModifyMul.vue'),
        name: 'ProductModifyMul',
        meta: {
          title: t('product_manage.mul_modify'),
          noCache: false,
          affix: true
        }
      },

      {
        path: 'pdtbommanage',
        component: () => import('@/views/ProductManage/PdtBomManage.vue'),
        name: 'PdtBomManage',
        meta: {
          title: t('project_manage.bom_list'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/productmanage/productmanage'
        }
      },


      {
        path: 'specsmanage',
        component: () => import('@/views/ProductManage/SpecsManage.vue'),
        name: 'SpecsManage',
        meta: {
          title: t('product_manage.specify_manage'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'showbomstructpdt',
        component: () => import('@/views/SaleManage/ShowBomStruct.vue'),
        name: 'showbomstructpdt',
        meta: {
          title: 'BOM结构图',
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/productmanage/productmanage'
        }
      },
    ]
  },
  {
    path: '/systemmanage',
    component: Layout,
    redirect: '/systemmanage/systemcmd',
    name: 'SystemManage',
    meta: {
      title: t('sysmgr.sysmgr'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'systemcmd',
        component: () => import('@/views/SystemManage/SystemCmd.vue'),
        name: 'SystemCmd',
        meta: {
          title: t('sysmgr.sysmgr'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'printmanage',
        component: () => import('@/views/SystemManage/PrintManage.vue'),
        name: 'PrintManage',
        meta: {
          title: t('print.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'messagecenter',
        component: () => import('@/views/SystemManage/MessageCenter.vue'),
        name: 'PrintManage',
        meta: {
          title: t('system.message_center'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'warningcenter',
        component: () => import('@/views/SystemManage/WarningCenter.vue'),
        name: 'WarningCenter',
        meta: {
          title: t('system.waining_center'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'overtimecenter',
        component: () => import('@/views/SystemManage/OvertimeCenter.vue'),
        name: 'OvertimeCenter',
        meta: {
          title: t('system.overtime_center'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'warningdetail',
        component: () => import('@/views/SystemManage/WarningDetail.vue'),
        name: 'WarningDetail',
        meta: {
          title: t('system.warning_detail'),
          noCache: true,
          affix: true,
          hidden: true,
          canTo: true,
        }
      },
      {
        path: 'tmp',
        component: () => import('@/views/Other/tmp.vue'),
        name: 'tmp',
        meta: {
          title:'tmp',
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
        }
      },
    ]
  },
]

//仓库管理
export const storeRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/inventorymanage',
    component: Layout,
    redirect: '/inventorymanage/inventorymanage',
    name: 'InventoryManageMain',
    meta: {
      title: t('store.inventory'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'手动入库审核'},
      {name:'手动出库审核'},
      {name:'仓库调拨审核'},
      { name: '采购入库同页面审核' },
      { name: '删除领料单' },
      
    ],
    children: [
      {
        path: 'inventorymanage',
        component: () => import('@/views/StoreManage/InventoryManage.vue'),
        name: 'InventoryManage',
        meta: {
          title: t('store.inventory_search'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'inventorydetail',
        component: () => import('@/views/StoreManage/InventoryDetail.vue'),
        name: 'InventoryDetail',
        meta: {
          title: t('inventory.detail'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/inventorymanage'
        }
      },
      {
        path: 'inventorystatement',
        component: () => import('@/views/StoreManage/InventoryStatement.vue'),
        name: 'InventoryStatement',
        meta: {
          title: t('inventory.detail_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/inventorymanage'
        }
      },
      {
        path: 'stoneoutlist',
        component: () => import('@/views/StoreManage/StoneOutList.vue'),
        name: 'StoneOutList',
        meta: {
          title: t('inventory.outlist'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddout',
        component: () => import('@/views/StoreManage/StoneAddOut.vue'),
        name: 'StoneAddOut',
        meta: {
          title: t('inventory.addoutlist'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stoneoutlist'
        }
      },
      {
        path: 'stoneinlist',
        component: () => import('@/views/StoreManage/StoneInList.vue'),
        name: 'StoneInList',
        meta: {
          title: t('inventory.inlist'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddin',
        component: () => import('@/views/StoreManage/StoneAddIn.vue'),
        name: 'StoneAddIn',
        meta: {
          title: t('inventory.addinlist'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stoneinlist'
        }
      },
      {
        path: 'stonemovelist',
        component: () => import('@/views/StoreManage/StoneMoveList.vue'),
        name: 'StoneMoveList',
        meta: {
          title: t('inventory.changelist'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddmove',
        component: () => import('@/views/StoreManage/StoneAddMove.vue'),
        name: 'StoneAddMove',
        meta: {
          title: t('inventory.addchange'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonemovelist'
        }
      },




      {
        path: 'stonesalemanage',
        component: () => import('@/views/StoreManage/StoneSaleManage.vue'),
        name: 'StoneSaleManage',
        meta: {
          title: t('sale.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddsale',
        component: () => import('@/views/StoreManage/StoneAddSale.vue'),
        name: 'StoneAddSale',
        meta: {
          title: t('sale.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonesalemanage'
        }
      },





      {
        path: 'stonesaleoutlist',
        component: () => import('@/views/StoreManage/StoneSaleOutList.vue'),
        name: 'StoneSaleOutList',
        meta: {
          title: '销售出库列表',
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddsaleout',
        component: () => import('@/views/StoreManage/StoneAddSaleOut.vue'),
        name: 'StoneAddSaleOut',
        meta: {
          title: t('sale.add_out'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonesaleoutlist'
        }
      },
      {
        path: 'stonesaleoutcheck',
        component: () => import('@/views/SaleManage/SaleOutList.vue'),
        name: 'StoneSaleOutCheck',
        meta: {
          title: t('sale.out_list_check'),
          noCache: false,
          affix: true
        }
      },


      
      {
        path: 'stonesalereturnlist',
        component: () => import('@/views/StoreManage/StoneSaleReturnList.vue'),
        name: 'StoneSaleReturnList',
        meta: {
          title: t('sale.return_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddsalereturn',
        component: () => import('@/views/StoreManage/StoneAddSaleReturn.vue'),
        name: 'StoneAddSaleReturn',
        meta: {
          title: t('sale.add_return'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonesalereturnlist'
        }
      },


      {
        path: 'stonepurchasemanage',
        component: () => import('@/views/StoreManage/StonePurchaseManage.vue'),
        name: 'StonePurchaseManage',
        meta: {
          title: t('purchase.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddpurchase',
        component: () => import('@/views/StoreManage/StoneAddPurchase.vue'),
        name: 'StoneAddPurchase',
        meta: {
          title: t('purchase.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonepurchasemanage'
        }
      },

      {
        path: 'stonereceiptlist',
        component: () => import('@/views/StoreManage/StoneReceiptList.vue'),
        name: 'StoneReceiptList',
        meta: {
          title: t('receipt.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddreceipt',
        component: () => import('@/views/StoreManage/StoneAddReceipt.vue'),
        name: 'StoneAddreceipt',
        meta: {
          title: t('receipt.add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stonereceiptlist'
        }
      },
      {
        path: 'stoneputinlist',
        component: () => import('@/views/StoreManage/StonePutinList.vue'),
        name: 'StonePutinList',
        meta: {
          title: t('putin.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneputinchecklist',
        component: () => import('@/views/StoreManage/StonePutinList.vue'),
        name: 'StonePutinCheckList',
        meta: {
          title: t('putin.check'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddputin',
        component: () => import('@/views/StoreManage/StoneAddPutin.vue'),
        name: 'StoneAddPutin',
        meta: {
          title: t('putin.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stoneputinlist'
        }
      },
      {
        path: 'stonereturnlist',
        component: () => import('@/views/StoreManage/StoneReturnList.vue'),
        name: 'StoneReturnList',
        meta: {
          title: t('return.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddreturn',
        component: () => import('@/views/StoreManage/StoneAddReturn.vue'),
        name: 'StoneAddReturn',
        meta: {
          title: t('return.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stoneaddreturn'
        }
      },
      {
        path: 'stonebooktakein',
        component: () => import('@/views/StoreManage/StoneBookTakein.vue'),
        name: 'StoneBookTakein',
        meta: {
          title: t('store.booktakein'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddbooktakein',
        component: () => import('@/views/StoreManage/StoneAddBookTakein.vue'),
        name: 'StoneAddBookTakein',
        meta: {
          title: t('store.add_booktakein'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/inventorymanage/stoneaddreturn'
        }
      },
    ]
  },
  {
    path: '/oeminventorymanage',
    component: Layout,
    redirect: '/oeminventorymanage/oeminventorymanage',
    name: 'OemInventoryManageMain',
    meta: {
      title: t('store.oeminventory'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'stoneoemtakeoutlist',
        component: () => import('@/views/StoreManage/oem/StoneOemTakeOutList.vue'),
        name: 'StoneOemTakeOutList',
        meta: {
          title: t('oem.takeout_list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddoemtakeout',
        component: () => import('@/views/StoreManage/oem/StoneAddOemTakeOut.vue'),
        name: 'StoneAddOemTakeOut',
        meta: {
          title: t('oem.add_takeout'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemorderlist'
        }
      },
      {
        path: 'stoneoemtakeinlist',
        component: () => import('@/views/StoreManage/oem/StoneOemTakeInList.vue'),
        name: 'StoneOemTakeInList',
        meta: {
          title: t('oem.takein_list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddoemtakein',
        component: () => import('@/views/StoreManage/oem/StoneAddOemTakeIn.vue'),
        name: 'StoneAddOemTakeIn',
        meta: {
          title: t('oem.add_takein'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemtakeinlist'
        }
      },
      {
        path: 'stoneoemtakechannellist',
        component: () => import('@/views/StoreManage/oem/StoneOemTakeChannelList.vue'),
        name: 'StoneOemTakeChannelList',
        meta: {
          title: t('takechannel.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddoemtakechannel',
        component: () => import('@/views/StoreManage/oem/StoneAddOemTakeChannel.vue'),
        name: 'StoneAddOemTakeChannel',
        meta: {
          title: t('takechannel.add_takechannel'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemtakechannellist'
        }
      },

      {
        path: 'stoneaddoemremain',
        component: () => import('@/views/StoreManage/oem/StoneAddOemRemain.vue'),
        name: 'StoneAddOemRemain',
        meta: {
          title: t('remain.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemremainlist'
        }
      },

      {
        path: 'stoneoemremainlist',
        component: () => import('@/views/StoreManage/oem/StoneOemRemainList.vue'),
        name: 'StoneOemRemainList',
        meta: {
          title: t('remain.oemremian'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneoemremaincheck',
        component: () => import('@/views/StoreManage/oem/StoneOemRemainList.vue'),
        name: 'StoneOemRemainCheck',
        meta: {
          title: t('remain.oemremian_check'),
          noCache: true,
          affix: true
        }
      },


      {
        path: 'stoneoemdrawinlist',
        component: () => import('@/views/StoreManage/oem/StoneOemDrawinList.vue'),
        name: 'StoneOemDrawinList',
        meta: {
          title: t('drawin.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stoneaddoemdrawin',
        component: () => import('@/views/StoreManage/oem/StoneAddOemDrawin.vue'),
        name: 'StoneAddOemDrawin',
        meta: {
          title: t('drawin.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemdrawinlist'
        }
      },
      {
        path: 'stoneoemputinlist',
        component: () => import('@/views/StoreManage/oem/StoneOemPutinList.vue'),
        name: 'StoneOemPutinList',
        meta: {
          title: t('oemputin.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddoemputin',
        component: () => import('@/views/StoreManage/oem/StoneAddOemPutin.vue'),
        name: 'StoneAddOemPutin',
        meta: {
          title: t('oemputin.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemputinlist'
        }
      },
      {
        path: 'stoneoemreturnlist',
        component: () => import('@/views/StoreManage/oem/StoneOemReturnList.vue'),
        name: 'StoneOemReturnList',
        meta: {
          title: t('oemreturn.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneoemreturncheck',
        component: () => import('@/views/StoreManage/oem/StoneOemReturnList.vue'),
        name: 'StoneOemReturnCheck',
        meta: {
          title: t('oemreturn.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddoemreturn',
        component: () => import('@/views/StoreManage/oem/StoneAddOemReturn.vue'),
        name: 'StoneAddOemReturn',
        meta: {
          title: t('oemreturn.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneoemreturnlist'
        }
      },

      {
        path: 'stoneparterstone',
        component: () => import('@/views/StoreManage/oem/StoneParterStone.vue'),
        name: 'StoneParterStone',
        meta: {
          title: t('parter.stone'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneparterstonestatement',
        component: () => import('@/views/StoreManage/oem/StoneParterStoneStatement.vue'),
        name: 'StoneParterStoneStatement',
        meta: {
          title: t('parterstone.liushui'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oeminventorymanage/stoneparterstone'
        }
      },
    ]
  },
  {
    path: '/stonecheck',
    component: Layout,
    redirect: '/stonecheck/stonechecklist',
    name: 'StoreCheckMain',
    meta: {
      title: t('store.check'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'stonechecklist',
        component: () => import('@/views/StoreManage/StoneCheckList.vue'),
        name: 'StoneCheckList',
        meta: {
          title: t('store.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'stoneaddcheck',
        component: () => import('@/views/StoreManage/StoneAddCheck.vue'),
        name: 'StoneAddCheck',
        meta: {
          title: t('store.check_add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/stonecheck/stonechecklist'
        }
      },
    ]
  },
  {
    path: '/storemanage',
    component: Layout,
    redirect: '/storemanage/storemanage',
    name: 'StoreManageMain',
    meta: {
      title: t('store.manage'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'storemanage',
        component: () => import('@/views/StoreManage/StoreManage.vue'),
        name: 'StoreManage',
        meta: {
          title: t('store.manage'),
          noCache: true,
          affix: true
        }
      }
    ]
  },
  {
    path: '/stonereport',
    component: Layout,
    redirect: '/stonereport/stonereportmonth',
    name: 'StoneReport',
    meta: {
      title: '仓库统计报表',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'stonereportmonth',
        component: () => import('@/views/StoreManage/Report/StoneReportMonth.vue'),
        name: 'StoneReportMonth',
        meta: {
          title: t('report.stone_report_month'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stonereportany',
        component: () => import('@/views/StoreManage/Report/StoneReportMonth.vue'),
        name: 'StoneReportAny',
        meta: {
          title: t('report.stone_report_any'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'stonereportdetail',
        component: () => import('@/views/StoreManage/Report/StoneReportMonthDetail.vue'),
        name: 'StoneReportDetail',
        meta: {
          title: t('report.stone_report_month_detail'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
        }
      },
    ]
  }
]

//委外管理
export const oemRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/oemmanage',
    component: Layout,
    redirect: '/oemmanage/oemorderlist',
    name: 'OemManage',
    meta: {
      title: t('oem.mgr'),
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
      {name:'委外订单明细查看'},
      {name:'委外订单新增'},
      {name:'委外订单修改'},
      {name:'委外订单价格显示'},
      {name:'委外订单删除'},
      {name:'委外显示关闭订单'},
      {name:'受托商名称显示'},
      {name:'委外图片查看'},
      {name:'委外文件查看'},
      {name:'委外订单同页面审核'},
    ],
    children: [
      {
        path: 'oemorderlist',
        component: () => import('@/views/OemManage/OemOrderList.vue'),
        name: 'OemOrderList',
        meta: {
          title: t('oem.order_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'oemordercheck',
        component: () => import('@/views/OemManage/OemOrderList.vue'),
        name: 'OemOrderCheck',
        meta: {
          title: t('oem.order_list_check'),
          noCache: false,
          affix: true
        }
      },



      {
        path: 'addoemorder',
        component: () => import('@/views/OemManage/AddOemOrder.vue'),
        name: 'AddOemOrder',
        meta: {
          title: t('oem.add_order'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'wlneedinfo',
        component: () => import('@/views/OemManage/WLNeedInfo.vue'),
        name: 'WLNeedInfo',
        meta: {
          title: t('oem.wlneed'),
          noCache: true,
          affix: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'wlprepare',
        component: () => import('@/views/OemManage/WLPrepare.vue'),
        name: 'WLPrepare',
        meta: {
          title:t('oem.wlprepare'),
          noCache: true,
          affix: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'oemtakeoutlist',
        component: () => import('@/views/OemManage/OemTakeOutList.vue'),
        name: 'OemTakeOutList',
        meta: {
          title: t('oem.takeout_list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addoemtakeout',
        component: () => import('@/views/OemManage/AddOemTakeOut.vue'),
        name: 'AddOemTakeOut',
        meta: {
          title: t('oem.add_takeout'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'oemtakeinlist',
        component: () => import('@/views/OemManage/OemTakeInList.vue'),
        name: 'OemTakeInList',
        meta: {
          title: t('oem.takein_list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addoemtakein',
        component: () => import('@/views/OemManage/AddOemTakeIn.vue'),
        name: 'AddOemTakeIn',
        meta: {
          title: t('oem.add_takein'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'oemtakechannellist',
        component: () => import('@/views/OemManage/OemTakeChannelList.vue'),
        name: 'OemTakeChannelList',
        meta: {
          title: t('takechannel.list'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'addoemtakechannel',
        component: () => import('@/views/OemManage/AddOemTakeChannel.vue'),
        name: 'AddOemTakeChannel',
        meta: {
          title: t('takechannel.add_takechannel'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },

      {
        path: 'addoemremain',
        component: () => import('@/views/OemManage/AddOemRemain.vue'),
        name: 'AddOemRemain',
        meta: {
          title: t('takechannel.add_takechannel'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },

      {
        path: 'oemremainlist',
        component: () => import('@/views/OemManage/OemRemainList.vue'),
        name: 'OemRemainList',
        meta: {
          title: t('remain.oemremian'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'oemremaincheck',
        component: () => import('@/views/OemManage/OemRemainList.vue'),
        name: 'OemRemainCheck',
        meta: {
          title: t('remain.oemremian_check'),
          noCache: true,
          affix: true
        }
      },


      {
        path: 'oemdrawinlist',
        component: () => import('@/views/OemManage/OemDrawinList.vue'),
        name: 'OemDrawinList',
        meta: {
          title: t('drawin.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addoemdrawin',
        component: () => import('@/views/OemManage/AddOemDrawin.vue'),
        name: 'AddOemDrawin',
        meta: {
          title: t('drawin.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },
      {
        path: 'oemqualitychecklist',
        component: () => import('@/views/OemManage/OemQualityCheckList.vue'),
        name: 'OemQualityCheckList',
        meta: {
          title: t('qualitycheck.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addoemqualitycheck',
        component: () => import('@/views/OemManage/AddOemQualityCheck.vue'),
        name: 'AddOemQualityCheck',
        meta: {
          title: t('qualitycheck.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemdrawinlist'
        }
      },
      {
        path: 'oemputinlist',
        component: () => import('@/views/OemManage/OemPutinList.vue'),
        name: 'OemPutinList',
        meta: {
          title: t('oemputin.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addoemputin',
        component: () => import('@/views/OemManage/AddOemPutin.vue'),
        name: 'AddOemPutin',
        meta: {
          title: t('oemputin.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemdrawinlist'
        }
      },
      {
        path: 'oemreturnlist',
        component: () => import('@/views/OemManage/OemReturnList.vue'),
        name: 'OemReturnList',
        meta: {
          title: t('oemreturn.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'oemreturncheck',
        component: () => import('@/views/OemManage/OemReturnList.vue'),
        name: 'OemReturnCheck',
        meta: {
          title: t('oemreturn.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addoemreturn',
        component: () => import('@/views/OemManage/AddOemReturn.vue'),
        name: 'AddOemReturn',
        meta: {
          title: t('oemreturn.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemdrawinlist'
        }
      },



      {
        path: 'wllock',
        component: () => import('@/views/OemManage/WLLock.vue'),
        name: 'WLLock222',
        meta: {
          title: t('oem.wl_lock'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },

      {
        path: 'parterstone',
        component: () => import('@/views/OemManage/ParterStone.vue'),
        name: 'ParterStone',
        meta: {
          title: t('parter.stone'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'parterstonestatement',
        component: () => import('@/views/OemManage/ParterStoneStatement.vue'),
        name: 'ParterStoneStatement',
        meta: {
          title: t('parterstone.liushui'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/parterstone'
        }
      },

      {
        path: 'addpurchase',
        component: () => import('@/views/PurchaseManage/AddPurchase.vue'),
        name: 'AddPurchase',
        meta: {
          title: t('purchase.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/wlneedinfo'
        }
      },



      {
        path: 'parterlist',
        component: () => import('@/views/OemManage/ParterList.vue'),
        name: 'ParterList',
        meta: {
          title: t('oem.parter'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addparter',
        component: () => import('@/views/OemManage/AddParter.vue'),
        name: 'AddParter',
        meta: {
          title: t('oem.addparter'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/parterlist'
        }
      },
      
      {
        path: 'test',
        component: () => import('@/views/OemManage/test.vue'),
        name: 'test',
        meta: {
          title: 'test',
          noCache: true,
          affix: true
        }
      },
      {
        path: 'print',
        component: () => import('@/views/PrintManage/print.vue'),
        name: 'print',
        meta: {
          title: 'print',
          noCache: true,
          affix: true
        }
      },
    ]
  },
  {
    path: '/oomonthlycheck',
    component: Layout,
    redirect: '/oomonthlycheck/oosellmonthlycheck',
    name: 'OOMonthlyCheck',
    meta: {
      title: '月结对账',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'oooemmonthlycheck',
        component: () => import('@/views/OemManage/Fin/OOOemMonthlyCheck.vue'),
        name: 'OOOemMonthlyCheck',
        meta: {
          title: t('finance.oem_montylycheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'oooemmonthlycuslist',
        component: () => import('@/views/OemManage/Fin/OOOemMontylyCusList.vue'),
        name: 'OOOemMontylyCusList',
        meta: {
          title: t('finance.parter_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oomonthlycheck/oooemmonthlycheck'
        },
      },
      {
        path: 'oooemmonthlycheckadd',
        component: () => import('@/views/OemManage/Fin/OOOemMonthlyCheckAdd.vue'),
        name: 'OOOemMonthlyCheckAdd',
        meta: {
          title: t('finance.oem_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oomonthlycheck/oooemmonthlycheck'
        }
      },
      {
        path: 'oooemmonthlycheckinfo',
        component: () => import('@/views/OemManage/Fin/OOOemMonthlyCheckInfo.vue'),
        name: 'OOOemMonthlyCheckInfo',
        meta: {
          title: t('finance.oem_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oomonthlycheck/oooemmonthlycheck'
        }
      },
      {
        path: 'oooemmonthlychecktj',
        component: () => import('@/views/OemManage/Fin/OOOemMonthlyCheckTJ.vue'),
        name: 'OOOemMonthlyCheckTJ',
        meta: {
          title: t('finance.oem_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'oempaymentapplication',
        component: () => import('@/views/OemManage/OemPaymentApplication.vue'),
        name: 'OemPaymentApplication',
        meta: {
          title: t('finance.paymentapplication'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oomonthlycheck/oooemmonthlycheck'
        }
      },
      {
        path: 'oempaymentapplicationadd',
        component: () => import('@/views/OemManage/OemPaymentApplicationAdd.vue'),
        name: 'OemPaymentApplicationAdd',
        meta: {
          title: t('finance.paymentapplication_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oomonthlycheck/oooemmonthlycheck'
        }
      },
    ]
  },
  {
    path: '/oemreport',
    component: Layout,
    redirect: '/oemreport/oemreportall',
    name: 'OemReport',
    meta: {
      title: '统计报表',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'oemreportall',
        component: () => import('@/views/OemManage/Report/OemReportAll.vue'),
        name: 'OemReportAll',
        meta: {
          title: t('report.oem_reportall'),
          noCache: true,
          affix: true
        }
      },
    ]
  }
]


//品质管理
export const qualityRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/qualitymanage',
    component: Layout,
    redirect: '/qualitymanage/qualityreceiptlist',
    name: 'QualityManage',
    meta: {
      title: '采购品质',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'qualityreceiptlist',
        component: () => import('@/views/QualityManage/QualityReceiptList.vue'),
        name: 'QualityReceiptList',
        meta: {
          title: t('receipt.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'qualityaddreceipt',
        component: () => import('@/views/QualityManage/QualityAddReceipt.vue'),
        name: 'QualityAddreceipt',
        meta: {
          title: t('receipt.add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/qualitymanage/qualityreceiptlist'
        }
      },
      {
        path: 'qualityqualitycheck',
        component: () => import('@/views/QualityManage/QualityQualityCheck.vue'),
        name: 'QualityQualityCheck',
        meta: {
          title: t('quality.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'qualityaddqualitycheck',
        component: () => import('@/views/QualityManage/QualityAddQualityCheck.vue'),
        name: 'QualityAddQualityCheck',
        meta: {
          title: t('quality.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/qualitymanage/qualityqualitycheck'
        }
      },
    ]
  },
  {
    path: '/oemqualitymanage',
    component: Layout,
    redirect: '/oemqualitymanage/qualityoemdrawinlist',
    name: 'OemQualityManage',
    meta: {
      title: '委外品质',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'qualityoemdrawinlist',
        component: () => import('@/views/QualityManage/QualityOemDrawinList.vue'),
        name: 'QualityOemDrawinList',
        meta: {
          title: t('drawin.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'qualityaddoemdrawin',
        component: () => import('@/views/QualityManage/QualityAddOemDrawin.vue'),
        name: 'QualityAddOemDrawin',
        meta: {
          title: t('drawin.add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemqualitymanage/qualityoemdrawinlist'
        }
      },
      {
        path: 'qualityoemqualitychecklist',
        component: () => import('@/views/QualityManage/QualityOemQualityCheckList.vue'),
        name: 'QualityOemQualityCheckList',
        meta: {
          title: t('qualitycheck.list'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'qualityaddoemqualitycheck',
        component: () => import('@/views/QualityManage/QualityAddOemQualityCheck.vue'),
        name: 'QualityAddOemQualityCheck',
        meta: {
          title: t('qualitycheck.add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/oemqualitymanage/qualityoemqualitychecklist'
        }
      },
    ]
  }
]

//财务管理 
export const financeRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/inoutdetail',
    component: Layout,
    redirect: '/inoutdetail/inoutdetail',
    name: 'InoutDetail',
    meta: {
      title: '收支明细',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'inoutdetail',
        component: () => import('@/views/FinanceManage/InOutDetail.vue'),
        name: 'InOutDetail',
        meta: {
          title: t('finance.inout_detail'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'bankmanage',
        component: () => import('@/views/FinanceManage/BankManage.vue'),
        name: 'BankManage',
        meta: {
          title: t('finance.bank_manage'),
          noCache: true,
          affix: true
        }
      },
    ]
  },
  {
    path: '/monthlycheck',
    component: Layout,
    redirect: '/monthlycheck/sellmonthlycheck',
    name: 'MonthlyCheck',
    meta: {
      title: '月结对账',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'sellmonthlycheck',
        component: () => import('@/views/FinanceManage/SellMonthlyCheck.vue'),
        name: 'SellMonthlyCheck',
        meta: {
          title: t('finance.sell_montylycheck'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'sellmonthlycuslist',
        component: () => import('@/views/FinanceManage/SellMontylyCusList.vue'),
        name: 'SellMontylyCusList',
        meta: {
          title: t('finance.cus_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/sellmonthlycheck'
        },
      },
      {
        path: 'sellmonthlycheckadd',
        component: () => import('@/views/FinanceManage/SellMonthlyCheckAdd.vue'),
        name: 'SellMonthlyCheckAdd',
        meta: {
          title: t('finance.sell_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/sellmonthlycheck'
        }
      },
      {
        path: 'sellmonthlycheckinfo',
        component: () => import('@/views/FinanceManage/SellMonthlyCheckInfo.vue'),
        name: 'SellMonthlyCheckInfo',
        meta: {
          title: t('finance.sell_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/sellmonthlycheck'
        }
      },


      {
        path: 'buymonthlycheck',
        component: () => import('@/views/FinanceManage/BuyMonthlyCheck.vue'),
        name: 'BuyMonthlyCheck',
        meta: {
          title: t('finance.buy_montylycheck'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'buymonthlycuslist',
        component: () => import('@/views/FinanceManage/BuyMontylyCusList.vue'),
        name: 'BuyMontylyCusList',
        meta: {
          title: t('finance.supplier_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/buymonthlycheck'
        },
      },
      {
        path: 'buymonthlycheckadd',
        component: () => import('@/views/FinanceManage/BuyMonthlyCheckAdd.vue'),
        name: 'BuyMonthlyCheckAdd',
        meta: {
          title: t('finance.buy_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/buymonthlycheck'
        }
      },
      {
        path: 'buymonthlycheckinfo',
        component: () => import('@/views/FinanceManage/BuyMonthlyCheckInfo.vue'),
        name: 'BuyMonthlyCheckInfo',
        meta: {
          title: t('finance.buy_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/buymonthlycheck'
        }
      },




      {
        path: 'oemmonthlycheck',
        component: () => import('@/views/FinanceManage/OemMonthlyCheck.vue'),
        name: 'OemMonthlyCheck',
        meta: {
          title: t('finance.oem_montylycheck'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'oemmonthlycuslist',
        component: () => import('@/views/FinanceManage/OemMontylyCusList.vue'),
        name: 'OemMontylyCusList',
        meta: {
          title: t('finance.parter_list'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/oemmonthlycheck'
        },
      },
      {
        path: 'oemmonthlycheckadd',
        component: () => import('@/views/FinanceManage/OemMonthlyCheckAdd.vue'),
        name: 'OemMonthlyCheckAdd',
        meta: {
          title: t('finance.oem_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/oemmonthlycheck'
        }
      },
      {
        path: 'oemmonthlycheckinfo',
        component: () => import('@/views/FinanceManage/OemMonthlyCheckInfo.vue'),
        name: 'OemMonthlyCheckInfo',
        meta: {
          title: t('finance.oem_info'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/monthlycheck/oemmonthlycheck'
        }
      },
      {
        path: 'sellmonthlychecktj',
        component: () => import('@/views/FinanceManage/SellMonthlyCheckTJ.vue'),
        name: 'SellMonthlyCheckTJ',
        meta: {
          title: t('finance.sell_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'buymonthlychecktj',
        component: () => import('@/views/FinanceManage/BuyMonthlyCheckTJ.vue'),
        name: 'BuyMonthlyCheckTJ',
        meta: {
          title: t('finance.buy_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'oemmonthlychecktj',
        component: () => import('@/views/FinanceManage/OemMonthlyCheckTJ.vue'),
        name: 'OemMonthlyCheckTJ',
        meta: {
          title: t('finance.oem_montylycheck_tj'),
          noCache: true,
          affix: true
        }
      },
    ]
  },
  {
    path: '/arap',
    component: Layout,
    redirect: '/arap/paymentapplication',
    name: 'arap',
    meta: {
      title: '应收应付',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
      {name:'审核付款申请'},
    ],
    children: [
      {
        path: 'paymentapplication',
        component: () => import('@/views/FinanceManage/PaymentApplication.vue'),
        name: 'PaymentApplication',
        meta: {
          title: t('finance.paymentapplication'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'paymentapplicationadd',
        component: () => import('@/views/FinanceManage/PaymentApplicationAdd.vue'),
        name: 'PaymentApplicationAdd',
        meta: {
          title: t('finance.paymentapplication_add'),
          noTagsView: false,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/arap/paymentapplication'
        }
      },
    ]
  },
]

//人力资源
export const humanResRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/humanmanage',
    component: Layout,
    redirect: '/humanmanage/usermanage',
    name: 'HumanManage',
    meta: {
      title: '员工管理',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'usermanage',
        component: () => import('@/views/UserManage/UserManage.vue'),
        name: 'HumanUserManage',
        meta: {
          title: t('userManage.userManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'usermanagedep',
        component: () => import('@/views/UserManage/UserManageDep.vue'),
        name: 'HumanUserManageDep',
        meta: {
          title: t('human.usermanage_dep'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'usermanage_zz',
        component: () => import('@/views/UserManage/UserManage.vue'),
        name: 'HumanUserManageZZ',
        meta: {
          title: t('human.zz'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'usermanage_lz',
        component: () => import('@/views/UserManage/UserManage.vue'),
        name: 'HumanUserManageLZ',
        meta: {
          title: t('human.lz'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'deptmanage',
        component: () => import('@/views/UserManage/DeptManage.vue'),
        name: 'HumanDeptManage',
        meta: {
          title: t('userManage.deptManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'adduser',
        component: () => import('@/views/UserManage/AddUser.vue'),
        name: 'HumanAddUser',
        meta: {
          title: t('userTable.addUser'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/humanmanage/usermanage'
        }
      },
      {
        path: 'rolemanage',
        component: () => import('@/views/UserManage/RoleManage.vue'),
        name: 'HumanRoleManage',
        meta: {
          title: t('userManage.roleManage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addrole',
        component: () => import('@/views/UserManage/AddRole.vue'),
        name: 'HumanAddRole',
        meta: {
          title: t('roleTable.addRole'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/humanmanage/rolemanage'
        }
      },
      {
        path: 'addroledata',
        component: () => import('@/views/UserManage/AddRoleData.vue'),
        name: 'HumanAddRoleData',
        meta: {
          title: '角色数据权限',
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/humanmanage/rolemanage'
        }
      },
      {
        path: 'jobmanage',
        component: () => import('@/views/HumanManage/JobManage.vue'),
        name: 'HumanJobManage',
        meta: {
          title: t('human.jobmanage'),
          noCache: true,
          affix: true
        }
      },
    ]
  },
  {
    path: '/kaoqingmanage',
    component: Layout,
    redirect: '/kaoqingmanage/KaoQingDeviceManage',
    name: 'KaoQingManageMain',
    meta: {
      title: '考勤管理',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
    ],
    children: [
      {
        path: 'kaoqingdevicemanage',
        component: () => import('@/views/KaoQingManage/KaoQingDeviceManage.vue'),
        name: 'KaoQingDeviceManage',
        meta: {
          title: t('kaoqing.device'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'kaoqingconfig',
        component: () => import('@/views/KaoQingManage/KaoQingConfig.vue'),
        name: 'KaoQingConfig',
        meta: {
          title: t('kaoqing.config'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'kaoqingconfigadd',
        component: () => import('@/views/KaoQingManage/KaoQingConfigAdd.vue'),
        name: 'KaoQingConfigAdd',
        meta: {
          title: t('kaoqing.addconfig'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/kaoqingmanage/kaoqingconfig'
        }
      },
      {
        path: 'kaoqingwaterlist',
        component: () => import('@/views/KaoQingManage/KaoQingWaterList.vue'),
        name: 'KaoQingWaterList',
        meta: {
          title: t('kaoqing.waterlist'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'kaoqingdaylist',
        component: () => import('@/views/KaoQingManage/KaoQingDayList.vue'),
        name: 'KaoQingDayList',
        meta: {
          title: t('kaoqing.daylist'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'kaoqingmounthlist',
        component: () => import('@/views/KaoQingManage/KaoQingMounthList.vue'),
        name: 'KaoQingMounthList',
        meta: {
          title: t('kaoqing.mounthlist'),
          noCache: true,
          affix: true
        }
      },
    ]
  },


  {
    path: '/lcmmanage',
    component: Layout,
    redirect: '/lcmmanage/lcmlist',
    name: 'LcmManage',
    meta: {
      title: '劳动合同',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'lcmlist',
        component: () => import('@/views/HumanManage/LcmList.vue'),
        name: 'LcmList',
        meta: {
          title: t('human.lcmlist'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'lcmwarning',
        component: () => import('@/views/HumanManage/LcmWarning.vue'),
        name: 'LcmWarning',
        meta: {
          title: t('human.nolcmwarning'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'nearlcmwarning',
        component: () => import('@/views/HumanManage/NoLcmWarning.vue'),
        name: 'NearLcmWarning',
        meta: {
          title: t('human.nearwarning'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'zzlcmwarning',
        component: () => import('@/views/HumanManage/ZZLcmWarning.vue'),
        name: 'ZZLcmWarning',
        meta: {
          title: t('human.zzwarning'),
          noCache: false,
          affix: true
        }
      }
    ]
  },
  {
    path: '/holidaymanage',
    component: Layout,
    redirect: '/holidaymanage/holidaymanage',
    name: 'HolidayManageMain',
    meta: {
      title: '请假管理',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
    ],
    children: [
      {
        path: 'holidaymanage',
        component: () => import('@/views/HumanManage/HolidayManage.vue'),
        name: 'HolidayManage',
        meta: {
          title: t('human.holidaymanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'holiadyadd',
        component: () => import('@/views/HumanManage/HolidayAdd.vue'),
        name: 'HolidayAdd',
        meta: {
          title: t('human.holidayadd'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/holidaymanage/holidaymanage'
        }
      },
      {
        path: 'yearholidaymanage',
        component: () => import('@/views/HumanManage/YearHolidayManage.vue'),
        name: 'YearHolidayManage',
        meta: {
          title: t('human.yearholiday'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'holidaytype',
        component: () => import('@/views/HumanManage/HolidayType.vue'),
        name: 'HolidayType',
        meta: {
          title: t('human.holidytype'),
          noCache: true,
          affix: true
        }
      },
    ]
  },
  {
    path: '/overtimemanage',
    component: Layout,
    redirect: '/overtimemanage/overtimemanage',
    name: 'OvertimeManageMain',
    meta: {
      title: '加班/调休管理',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
    ],
    children: [
      {
        path: 'overtimemanage',
        component: () => import('@/views/HumanManage/OvertimeManage.vue'),
        name: 'OvertimeManage',
        meta: {
          title: t('human.overtimemanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'overtimeadd',
        component: () => import('@/views/HumanManage/OvertimeAdd.vue'),
        name: 'OvertimeAdd',
        meta: {
          title: t('human.overtime_add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/overtimemanage/overtimemanage'
        }
      },
      {
        path: 'overtimemanagecheck',
        component: () => import('@/views/HumanManage/OvertimeManage.vue'),
        name: 'OvertimeManageCheck',
        meta: {
          title: t('human.overtimemanage_check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'tiaoxiumanage',
        component: () => import('@/views/HumanManage/TiaoxiuManage.vue'),
        name: 'TiaoxiuManage',
        meta: {
          title: t('human.tiaoxiumanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'tiaoxiumanagecheck',
        component: () => import('@/views/HumanManage/TiaoxiuManage.vue'),
        name: 'TiaoxiuManageCheck',
        meta: {
          title: t('human.tiaoxiumanage_check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'tiaoxiuadd',
        component: () => import('@/views/HumanManage/TiaoxiuAdd.vue'),
        name: 'TiaoxiuAdd',
        meta: {
          title: t('human.overtime_add'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/overtimemanage/tiaoxiumanage'
        }
      },
    ]
  },
  {
    path: '/businessmanage',
    component: Layout,
    redirect: '/businessmanage/businessmanage',
    name: 'BusinessManageMain',
    meta: {
      title: '出差管理',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles: [
    ],
    children: [
      {
        path: 'businessmanage',
        component: () => import('@/views/HumanManage/BusinessManage.vue'),
        name: 'BusinessManage',
        meta: {
          title: t('human.businessmanage'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'businessadd',
        component: () => import('@/views/HumanManage/BusinessAdd.vue'),
        name: 'BusinessAdd',
        meta: {
          title: t('human.businessadd'),
          noTagsView: true,
          noCache: true,
          hidden: true,
          canTo: true,
          activeMenu: '/businessmanage/businessmanage'
        }
      },
    ]
  },
  
]


//手机版本路由
export const mobileRouterMap: AppRouteRecordRaw[] = [
  //主页面
  {
    path: '/mobile',
    component: () => import('@/layout/LayoutMobile.vue'),
    redirect: '/mobile/main',
    name: 'MainMain',
    meta: {
      title: '主页',
      icon: 'ant-design:dashboard-filled',
      alwaysShow: true
    },
    otherroles:[
    ],
    children: [
      {
        path: 'main',
        component: () => import('@/views/mobile/main/main.vue'),
        name: 'mainmain',
        meta: {
          title: t('mobile.main'),
          noCache: true,
          affix: true
        },
      }
    ]
  },
  //销售页面
  {
    path: '/mobile_salemanage',
    component: () => import('@/layout/LayoutMobile.vue'),
    redirect: '/mobile_salemanage/salemain',
    name: 'SaleMainMain',
    meta: {
      title: '销售管理',
      noCache: true,
      affix: true
    },
    children: [
      {
        path: 'salemain',
        component: () => import('@/views/mobile/SaleManage/SaleMain.vue'),
        name: 'SaleMain',
        meta: {
          title: '销售管理',
          noCache: false,
          affix: true
        }
      },
      {
        path: 'salemanage',
        component: () => import('@/views/mobile/SaleManage/SaleList.vue'),
        name: 'SaleManage',
        meta: {
          title: t('sale.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'pmcsalecheck',
        component: () => import('@/views/mobile/SaleManage/SaleList.vue'),
        name: 'PMCSaleCheck',
        meta: {
          title: t('sale.list_PMCCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'productmgrsalecheck',
        component: () => import('@/views/mobile/SaleManage/SaleList.vue'),
        name: 'ProductMgrSaleCheck',
        meta: {
          title: t('sale.list_ProductCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'projectmgrsalecheck',
        component: () => import('@/views/mobile/SaleManage/SaleList.vue'),
        name: 'ProjectMgrSaleCheck',
        meta: {
          title: t('sale.list_ProjectCheck'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'addsale',
        component: () => import('@/views/mobile/SaleManage/AddSale.vue'),
        name: 'AddSale',
        meta: {
          title: t('sale.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/mobile_salemanage/salemanage'
        }
      },

    ]
  },
    //采购页面
  {
    path: '/mobile_purchasemanage',
    component: () => import('@/layout/LayoutMobile.vue'),
    redirect: '/mobile_purchasemanage/purchasemain',
    name: 'PurchaseMainMain',
    meta: {
      title: '采购管理',
      noCache: true,
      affix: true
    },
    children: [
      {
        path: 'purchasemain',
        component: () => import('@/views/mobile/PurchaseManage/PurchaseMain.vue'),
        name: 'PurchaseMain',
        meta: {
          title: '采购管理',
          noCache: false,
          affix: true
        }
      },
      {
        path: 'purchasemanage',
        component: () => import('@/views/mobile/PurchaseManage/PurchaseList.vue'),
        name: 'PurchaseManage',
        meta: {
          title: t('purchase.list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'purchasecheck',
        component: () => import('@/views/mobile/PurchaseManage/PurchaseList.vue'),
        name: 'PurchaseCheck',
        meta: {
          title: t('purchase.check'),
          noCache: true,
          affix: true
        }
      },
      {
        path: 'addpurchase',
        component: () => import('@/views/mobile/PurchaseManage/AddPurchase.vue'),
        name: 'AddPurchase',
        meta: {
          title: t('purchase.add'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/mobile_purchasemanage/purchasemanage'
        }
      },
    ]
  },


  //委外页面
  {
    path: '/mobile_oemmanage',
    component: () => import('@/layout/LayoutMobile.vue'),
    redirect: '/mobile_oemmanage/oemmain',
    name: 'OemMainMain',
    meta: {
      title: '委外管理',
      noCache: true,
      affix: true
    },
    children: [
      {
        path: 'oemmain',
        component: () => import('@/views/mobile/OemManage/OemMain.vue'),
        name: 'OemMain',
        meta: {
          title: '委外管理',
          noCache: false,
          affix: true
        }
      },
      {
        path: 'oemorderlist',
        component: () => import('@/views/mobile/OemManage/OemOrderList.vue'),
        name: 'OemOrderList',
        meta: {
          title: t('oem.order_list'),
          noCache: false,
          affix: true
        }
      },
      {
        path: 'oemordercheck',
        component: () => import('@/views/mobile/OemManage/OemOrderList.vue'),
        name: 'OemOrderCheck',
        meta: {
          title: t('oem.order_list_check'),
          noCache: false,
          affix: true
        }
      },



      {
        path: 'addoemorder',
        component: () => import('@/views/mobile/OemManage/AddOemOrder.vue'),
        name: 'AddOemOrder',
        meta: {
          title: t('oem.add_order'),
          noTagsView: false,
          noCache: false,
          hidden: true,
          canTo: true,
          activeMenu: '/oemmanage/oemorderlist'
        }
      },


    ]
  }
]

const isMobileDevice =()=> {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

//主功能列表路由
export const abilityTabMap: AbilityTabItem[] = [] 

export const getAbilityTabMap = ()=>{
  if(getAbilityTabMap.length == 0)
  {
    initTab()
  }
  return abilityTabMap
}

const getDefRouter = () => {
  if (isMobileDevice()) {
    constantRouterMap[0].redirect = '/mobile/main'
  }
  return constantRouterMap
}

const router = createRouter({
  history: createWebHashHistory(),
  strict: true,
  routes: getDefRouter() as RouteRecordRaw[],//constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  console.log(router)
  initTab()
  app.use(router)

}



//初始化总TAB功能列表
const initTab =()=>
{
  abilityTabMap.length = 0  
  
  if (isMobileDevice()) {
    abilityTabMap.push({"name":"mobileRouterMap","title":'手机主页',"routes":mobileRouterMap})
  }
  else {
    abilityTabMap.push({"name":"customerRouterMap","title":t("router.customerRouterMap"),"routes":customerRouterMap})
    abilityTabMap.push({"name":"saleRouterMap","title":t("router.saleRouterMap"),"routes":saleRouterMap})
    abilityTabMap.push({"name":"procureRouterMap","title":t("router.procureRouterMap"),"routes":procureRouterMap})
    abilityTabMap.push({"name":"oemRouterMap","title":t("oem.mgr"),"routes":oemRouterMap})
    abilityTabMap.push({"name":"qualityRouterMap","title":'品质管理',"routes":qualityRouterMap})
    abilityTabMap.push({"name":"storeRouterMap","title":t("router.storeRouterMap"),"routes":storeRouterMap})
    abilityTabMap.push({"name":"prjectRouterMap","title":t("router.prjectRouterMap"),"routes":prjectRouterMap})
    abilityTabMap.push({"name":"financeRouterMap","title":'财务管理',"routes":financeRouterMap})
    abilityTabMap.push({"name":"humanResRouterMap","title":'人力资源',"routes":humanResRouterMap})
    abilityTabMap.push({"name":"sysConfigRouterMap","title":t("router.sysConfigRouterMap"),"routes":sysConfigRouterMap})  
  }
    

  
}

export const getRouterTabItem = (name: string) => {
  abilityTabMap.length = 0  
  
  if (isMobileDevice()) {
    abilityTabMap.push({"name":"mobileRouterMap","title":'手机主页',"routes":mobileRouterMap})
  }
  else {
    abilityTabMap.push({"name":"customerRouterMap","title":t("router.customerRouterMap"),"routes":customerRouterMap})
    abilityTabMap.push({"name":"saleRouterMap","title":t("router.saleRouterMap"),"routes":saleRouterMap})
    abilityTabMap.push({"name":"procureRouterMap","title":t("router.procureRouterMap"),"routes":procureRouterMap})
    abilityTabMap.push({"name":"oemRouterMap","title":t("oem.mgr"),"routes":oemRouterMap})
    abilityTabMap.push({"name":"qualityRouterMap","title":'品质管理',"routes":qualityRouterMap})
    abilityTabMap.push({"name":"storeRouterMap","title":t("router.storeRouterMap"),"routes":storeRouterMap})
    abilityTabMap.push({"name":"prjectRouterMap","title":t("router.prjectRouterMap"),"routes":prjectRouterMap})
    abilityTabMap.push({ "name": "financeRouterMap", "title": '财务管理', "routes": financeRouterMap })
    abilityTabMap.push({"name":"humanResRouterMap","title":'人力资源',"routes":humanResRouterMap})
    abilityTabMap.push({"name":"sysConfigRouterMap","title":t("router.sysConfigRouterMap"),"routes":sysConfigRouterMap})  
  }


  for(const item of abilityTabMap)
  {
    if(item.name == name)
    {
      return item
    }
  }
  return {
    "name":"",
    "title":"",
    "routes":[]
  }
}
export const getRootNameByPath = (path)=>{
  for(const item of abilityTabMap)
    {
      for(const route of item.routes)
      {
        for(const one of route.children)
        {
            const parts = path.split('/');
            const lastPart = parts[parts.length - 1];
            if(lastPart == one.path)
            {
              return item.name
            }

          }
      }
    }
    return ''
}
//拿到所有不需要权限的页面
export const getAllNoQXRoute = ()=>{
  const arrayAll = []
  for(const item of abilityTabMap)
    {
      for(const route of item.routes)
      {
        for(const one of route.children)
          {
            if(one.meta.hidden == true )
              {
                one.test = route.path+'/'+one.path
                arrayAll.push(one)
              }
          }
      }
    }
  return arrayAll;
}

export const gotoRouter = (path:string)=>{
  //push(path)
}

export default router
