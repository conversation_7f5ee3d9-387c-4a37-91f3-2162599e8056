<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElUpload, ElButton, ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElTableColumn, ElInput, ElSelect, ElOption, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted, onActivated } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { getCategListApi, addCategApi, updateCategApi, delCategApi, getBomListApi, delBomApi, getBomInfoApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { computed, nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { DialogBomStruct } from '@/components/DialogBomStruct'
import { DialogBomHis } from '@/components/DialogBomHis'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { useAppStore } from '@/store/modules/app'
import { importBomApi } from '@/api/task';
import { DialogImportTask } from '@/components/DialogImportTask'
import { onBeforeUnmount } from 'vue';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { exportBomListApi } from '@/api/extra';

const { currentRoute, push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
  children: 'sub_categ',
  label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
//分类树右键菜单classname
const categMenuClassName = 'categ_menu'
//分类右键菜单
const menuCateg = reactive([
  {
    icon: "icon-park-outline:add",
    name: 'add_product',
    title: t('product_manage.add_product')
  },
  {
    icon: "icon-park-outline:add",
    name: 'categ_param_set',
    title: t('product_manage.categ_param_set')
  },
  {
    icon: '',
    name: '分割线',
    title: ''
  },
  {
    icon: "icon-park-outline:add",
    name: 'add_categ',
    title: t('product_manage.add_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'update_categ',
    title: t('product_manage.update_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'delete_categ',
    title: t('product_manage.delete_categ')
  },
])
//分类编码器设置窗口控制
const showEncoderWin = ref(false)
//编码器设置
const categEncoderSet = reactive(['关闭', '', 1, 8])

//BOM数据源
const bomData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  categ: '',
  pdt_name: '',
  pdt_nick: '',
  specs_name: '',
  产品状态: '',
  客户: '',
  备注: '',
  物料编号:'',
  物料名称:'',
  page: 1,
  count: 15
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
  const ret = await getCategListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    console.log('当前选中')

    //设置默认选中
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    console.log(categTree.value)
    console.log(lastSelCateg.id)
    currentCatgData.value = lastSelCateg
    currentCatg.value = lastSelCateg.id
    searchCondition.categ = lastSelCateg.id
    if (lastSelCateg.id != undefined) {
      nextTick(() => {
        categTree.value.setCurrentNode(lastSelCateg)
        console.log(categTree.value?.getCurrentNode())
      })
    }


    categData.splice(0, categData.length, ret.data.all_categs)
    //设置默认展开
    expandCateg.splice(0, expandCateg.length, ret.data.all_categs.id)
    if (currentCatg.value) {
      expandCateg.push(currentCatg.value)
    }
  }
}
//分类树点击左键
const leftClick = (data) => {
  currentCatgData.value = data
  searchCondition.categ = data.id
  //更新最后一次选中
  wsCache.set('last_sel_categ', data)
  getBomList()
}

//分类树点击右键菜单
const rightClick = (event, data) => {
  currentCatg.value = data.id
  currentCatgData.value = data
  //隐藏之前的菜单
  hideRightMenu()

  //如果是未分组人员行不允许弹出菜单
  if (data.name == '未分组人员') {
    event.preventDefault();
    return
  }


  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  if (!menu) return;

  event.preventDefault();
  menu.style.left = `${event.pageX - 200}px`;
  menu.style.top = `${event.pageY - 200}px`;
  menu.style.display = "block";
  menu.style.zIndex = "1000";

  document.addEventListener("click", hideRightMenu);
}
//隐藏右键菜单
const hideRightMenu = () => {
  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  menu!.style.display = "none";
  document.removeEventListener("click", hideRightMenu);
}
//处理分类树菜单消息
const handleMenuEvent = (item) => {
  console.log(item)
  hideRightMenu()
  //添加/修改分类
  if (item === 'add_categ' || item === 'update_categ') {
    ElMessageBox.prompt(t('msg.inputCategName'), t('title.notify'),
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        inputErrorMessage: t('msg.inputCategName'),
        inputValidator: (val) => {
          if (val === null || val.length < 1) {
            return false;
          }
        }
      })
      .then(async ({ value }) => {
        if (item === 'add_categ') {
          const ret = await addCategApi({
            parent_id: currentCatg.value,
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }
        else {
          const ret = await updateCategApi({
            ids: [currentCatg.value],
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }


      })
  }
  //删除分类
  else if (item === 'delete_categ') {
    ElMessageBox.confirm(
      t('msg.delCateg'),
      'Warning',
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        type: 'warning',
      }
    )
      .then(async () => {
        const ret = await delCategApi({
          ids: [currentCatg.value],
        })
        if (ret) {
          getCategList()
          ElMessage({
            type: 'success',
            message: t('msg.success'),
          })
        }



      })
  }
  //设置分类参数
  else if (item === 'categ_param_set') {
    showEncoderWin.value = true
    categEncoderSet.splice(0, categEncoderSet.length, ...currentCatgData.value.pdt_num_role)
    console.log(categEncoderSet)
  }
  else if (item === 'add_product') {
    onAddBom()
  }
}
//计算编码展示样例
const categEncodeDemo = computed(() => {
  return categEncoderSet[1] + '0'.repeat(categEncoderSet[3])
})

//修改分类编码器格式
const onUpdateCategEncode = async () => {
  showEncoderWin.value = false
  const ret = await updateCategApi({
    ids: [currentCatgData.value.id],
    pdt_num_role: [...categEncoderSet],
    pdt_def_types: [...currentCatgData.value.pdt_def_types],
    pdt_def_digits: [...currentCatgData.value.pdt_def_digits],
  })
  if (ret) {
    ElMessage({
      type: 'success',
      message: t('msg.success'),
    })
    getCategList()
  }
}


//开始查询
const onSearch = () => {
  getBomList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (tableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 270
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getBomList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getBomList(val)
}
//处理表格对象操作
const handleOper = async(type, item) => {
  //编辑产品
  if (type === 'edit') {
    //缓存查询条件
    localStorage.setItem('bom_search',JSON.stringify(searchCondition))
    push({
      path: '/bommanage/addbom',
      query: {
        id: item.id,
      }
    })
  }
  else if (type === 'del') //删除
  {
    console.log(item)
    ElMessageBox.confirm(t('msg.confirm_del') + '--> ' + item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delBomApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getCategList()
        getBomList()
      }
    }
    ).catch(() => { })
  }
  else if (type === 'copybom') //复制BOM
  {
    push({
      path: '/bommanage/addbom',
      query: {
        id: '',
        copy_id: item.id,  //复制主要信息来源
      },

    })
  }
  else if (type === 'his') //历史版本
  {

  }
  else if (type === 'fullpath') //完整结构图
  {
    onShowBomStruct(item.id)
  }
  else if (type === 'fullpath_new') //完整结构图
  {
    // onShowBomStruct(item.id)
    const ret = await getBomInfoApi({
      id: item.id,
      is_all_level: 1
    })
    if (ret) {
      console.log(ret)
      const msg = JSON.stringify(ret.data)
      localStorage.setItem('bom', msg)
      const url = `${window.location.origin}/bom.html`;
      console.log(url)
      window.open(url, '_blank');
      // tableData.value = [ret.data]

      // console.log(tableData.value)
    }
  }
  else if (type === 'fullpath_print') { //打印完整结构图
    const ret = await getBomInfoApi({
      id: item.id,
      is_all_level: 1
    })
    if (ret) {
      console.log(ret)
      toPrintPage(ret.data)
      // const msg = JSON.stringify(ret.data)
      // localStorage.setItem('bom', msg)
      // const url = `${window.location.origin}/bom.html`;
      // console.log(url)
      // window.open(url, '_blank');
      // // tableData.value = [ret.data]

      // // console.log(tableData.value)
    }
  }
}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: 'BOM完整结构图' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}



//进入新增产品界面
const onAddBom = () => {
  console.log(currentCatgData.value)
  push({
    path: '/bommanage/addbom',
    query: {
      id: '',
      categ: currentCatgData.value.id,
      categ_name: currentCatgData.value.name
    },

  })
}

//查询BOM列表
const loadingList = ref(false)
const getBomList = async (page = 1) => {
  const lastSelCateg = wsCache.get('last_sel_categ') || {}
  searchCondition.categ = lastSelCateg.id == undefined ? '0' : lastSelCateg.id
  searchCondition.page = page
  loadingList.value = true
  const ret = await getBomListApi(searchCondition)
  if (ret) {
    bomData.splice(0, bomData.length, ...ret.data)
    console.log(bomData)
    totleCount.value = parseInt(ret.count)
  }
  loadingList.value = false
}

onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);


  if (currentRoute.value.query.pdt_name != undefined) {
    searchCondition.pdt_name = currentRoute.value.query.pdt_name
  }
  if (currentRoute.value.query.wl_name != undefined) {
    searchCondition.物料编号 = currentRoute.value.query.wl_name
  }

  //如果是编辑状态则显示默认查询条件
  if(currentRoute.value.query.type == 'info')
  {
    const tj = localStorage.getItem('bom_search') || ''
    console.log(tj)
    if(tj?.indexOf('{')>=0)
    {
      const jj = JSON.parse(tj)
      searchCondition.pdt_nick = jj.pdt_nick
      searchCondition.pdt_name = jj.pdt_name
      searchCondition.specs_name = jj.specs_name
      searchCondition.产品状态 = jj.产品状态
      searchCondition.客户 = jj.客户
      searchCondition.备注 = jj.备注
      getBomList()
    }
  }

  //更新分类树
  getCategList()

  //更新产品列表
  getBomList()

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
});


//--------------中间拖拽调整大小-------------
const isResizing = ref(false);
const startX = ref(0);
const leftWidth = ref(15);
const rightWidth = ref(85);

const startResize = (event: MouseEvent) => {
  event.preventDefault(); // 阻止默认行为
  isResizing.value = true;
  startX.value = event.clientX;

  document.addEventListener('mousemove', resize);
  document.addEventListener('mouseup', stopResize);
};

const resize = (event: MouseEvent) => {
  if (isResizing.value) {
    event.preventDefault(); // 阻止默认行为
    const deltaX = event.clientX - startX.value;
    leftWidth.value += (deltaX / window.innerWidth) * 100;
    rightWidth.value -= (deltaX / window.innerWidth) * 100;
    startX.value = event.clientX;
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('mouseup', stopResize);
};


//显示BOM结构图
const showBomStruct = ref(false)
const showBomID = ref('')
const onShowBomStruct = (bom_id) => {
  console.log(bomData)
  showBomID.value = bom_id
  showBomStruct.value = true
}

//显示BOM历史
const showBomHis = ref(false)
const selBom = ref(null)
const selBomId = ref('')
const onShowBomHis = (bom) => {
  console.log(bom)
  selBom.value = bom
  selBomId.value = bom.id
  showBomHis.value = true
}

const tableRowClassName = (row, index) => {
  if (row.row.status == '草稿') {
    return 'error-row'
  }
}

//正在上传
const bShowTaskDlg = ref(false)
const onShowTaskDlg = async () => {
  bShowTaskDlg.value = true
}
const loading = ref(false)
const uploadImg = async (file) => {
  const info = wsCache.get(appStore.getUserInfo)
  console.log(info)

  let path = 'import/' + info.username + '/bom/'

  const ret = await getOssSignApi({ upload_dir: path })
  if (ret) {
    loading.value = true
    const end = await ossUpload(ret.data.token, file.file, path, (pro) => {
      console.log('pppp', pro)

    })
    loading.value = false
    // fileData.push({
    //     name:file.file.name,
    //     url:end.url
    // })
    //上传完成调用导入
    const imp = await importBomApi({
      url: end.url
    })
    if (imp) {
      ElMessage.success("上传文件成功，等待系统导入！")
      console.log('上传完成', end.url)
      onShowTaskDlg()
    }
    else {
      ElMessage.error("创建导入任务失败，请重试！")
    }

  }
}
//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scrollY.value = document.getElementById('mainscroll')?.scrollTop
  console.log('离开了',scrollY)
  next()
})
onActivated(()=>{
  document.getElementById('mainscroll')?.scrollTo(0,scrollY.value)
})



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    searchCondition.categ = lastSelCateg.id == undefined ? '0' : lastSelCateg.id
    const ret = await exportBomListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
  <div ref="rootRef" class="absolute top-5 right-5 left-5 bottom-5 flex">
    <!-- 左侧分类栏 -->
    <div class="bg-white p-2 overflow-y-auto" :style="{ width: leftWidth + '%' }">
      <el-tree ref="categTree" :data="categData" :props="defaultProps" :default-expanded-keys="expandCateg"
        node-key="id" @node-contextmenu="rightClick" @node-click="leftClick" highlight-current
        :current-node-key="currentCatg" :expand-on-click-node="false" :render-after-expand="true">
        <template #default="{ node }">
          <Icon icon="bx:category" />
          <div class="pl-2">{{ node.data.name + (node.data.total_bom_count > 0 ? (" " + node.data.total_bom_count) : '')
            }}
          </div>
        </template>
      </el-tree>
      <RightMenu :id="categMenuClassName" :menuDate="menuCateg" @on-menu-event="handleMenuEvent" />
    </div>
    <div class="drag w-2  bg-gray-100 " style="cursor: col-resize;" @mousedown="startResize"></div>
    <!-- 右侧产品列表 -->
    <div class="relative w-[100%] !bg-white flex-grow overflow-y-auto" :style="{ width: rightWidth + '%' }">
      <div class="absolute top-5 left-10 flex">
        <ElButton type="success" class="mr-3" @click="onAddBom">
          <Icon icon="carbon:document-add" />
          <div class="pl-2">{{ t('button.add') }}</div>
        </ElButton>
        <el-upload class='mr-3' :http-request="(file) => uploadImg(file)" :auto-upload="true" :show-file-list='false'>
          <template #trigger>
            <el-button color="#409EFF" plain :loading="loading" :disabled="loading">
              <Icon icon="clarity:import-line" />
              {{ loading ? '上传中..' : '导入' }}
            </el-button>
          </template>
        </el-upload>
        <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
      </div>
      <div class="w-[100%] h-[100%] bg-white p-7">
        <div class="text-center mb-5 font-bold">{{ t('project_manage.bom_manage') +(currentRoute.query.type == 'info'?'2':'')}}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small" v-model="searchCondition.pdt_name" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small" v-model="searchCondition.pdt_nick" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input size="small" v-model="searchCondition.specs_name" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">产品状态</div>
            <el-select size="small" class="searchItem" v-model="searchCondition.产品状态" placeholder="">
              <el-option v-for="item in ['正常', '停用']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">客户</div>
            <el-input size="small" v-model="searchCondition.客户" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">备注</div>
            <el-input size="small" v-model="searchCondition.备注" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">物料编号</div>
            <el-input size="small" v-model="searchCondition.物料编号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1">
            <div class="searchTitle">物料名称</div>
            <el-input size="small" v-model="searchCondition.物料名称" placeholder="" class="searchItem" />
          </div>


          <div class="mt-5 mb-2 flex items-center justify-end">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
            <!-- <el-checkbox class="ml-5" :label="t('customer.senior')" v-model="senior"/> -->
          </div>
        </div>
        <!-- BOM列表 -->
        <el-table v-loading.lock="loadingList" ref="tableRef" :row-class-name="tableRowClassName" header-cell-class-name="tableHeader"
          :data="bomData" style="width: 100%" :height="tableHeight" border>
          <el-table-column show-overflow-tooltip prop="pdt_name" :label="t('product_manage.id')" width="150" />
          <el-table-column show-overflow-tooltip prop="pdt_nick" :label="t('product_manage.name')" width="200">
            <template #default="scope">
              <div style="white-space: normal;" class11="nameStyle" @click="handleOper('edit', scope.row)">{{
                scope.row.pdt_nick }}</div>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="status" :label="'状态'" width="70" />
          <el-table-column prop="规格" :label="t('product_manage.specify_info')" width="100">
            <template #default="scope">
              <el-tooltip class="box-item" effect="dark" :content="scope.row.specs_text" placement="bottom">
                {{ scope.row.specs_name == '自定义规格' ? scope.row.specs_text : scope.row.specs_name }}
              </el-tooltip>

            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="工序数" :label="t('bom.process_count')" width="70" />
          <el-table-column show-overflow-tooltip prop="物料数" :label="t('bom.part_count')" width="70" />
          <el-table-column show-overflow-tooltip prop="物料费" :label="t('bom.part_price')" width="100" />
          <el-table-column show-overflow-tooltip prop="人工费" :label="t('bom.job_price')" width="100" />
          <el-table-column show-overflow-tooltip prop="成本价" :label="t('bom.cost_price')" width="100" />
          <el-table-column show-overflow-tooltip prop="buyer_name" :label="t('customer.name')" width="200" />
          <el-table-column show-overflow-tooltip prop="mainer_name" :label="t('process.manager')" width="130" />
          <el-table-column fixed="right" show-overflow-tooltip prop="remark" :label="t('customer.remark')"
            min-width="130" />
          <el-table-column show-overflow-tooltip prop="modify_date" :label="t('bom.last_edit_time')" width="180" />
          <el-table-column fixed="right" show-overflow-tooltip :label="t('userTable.operate')" width="80">
            <template #default="scope">
              <el-dropdown trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <div class="flex flex-wrap w-[200px]">
                    <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('copybom', scope.row)">复制BOM</el-dropdown-item>
                    <el-dropdown-item @click="onShowBomHis(scope.row)">历史版本</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('fullpath', scope.row)">完整结构图</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('fullpath_new', scope.row)">完整结构图(新页面)</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('fullpath_print', scope.row)">【打印】完整结构图</el-dropdown-item>
                  </div>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="flex justify-end mt-4" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[15, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>



    </div>
    <DialogBomStruct v-model:show="showBomStruct" :bomid="showBomID" />
    <DialogBomHis v-model:show="showBomHis" :bom="selBom" @on-submit="getBomList" :old_id="selBomId" />
    <DialogImportTask v-model:show="bShowTaskDlg" :mod="'bom'" :cmd="'import_list'" @on-submit="getBomList" />

    <PrintModal v-model:show="dialogVisible" />
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e1f3d8 !important;
}

.searchItem {
  width: 150px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}

.headerBk {
  background-color: #6d92b4 !important;
}

.content {
  &:extend(.header);
  font-size: 14px;
}

.header>div,
.content>div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}

.header>div:last-child,
.content>div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

//解决tree没有横向滚动条问题
:deep(.el-tree>.el-tree-node) {
  display: inline-block;
  min-width: 100%;
}


:deep(.error-row) {
  // background-color: rgb(252, 196, 196);
  // color:#F56C6C;
  background-color: #cccccc;
}
</style>
