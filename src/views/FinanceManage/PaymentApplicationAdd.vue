<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElPopconfirm,ElDropdown, ElDropdownItem, ElDropdownMenu, ElTag, ElDatePicker, ElSelect, ElOption, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, nextTick } from 'vue'
import { getStoreListApi, getOemPutinNewnumApi, getOemDrawinInfoApi, getOemPutinInfoApi, addOemPutinApi, updateOemPutinApi, getPaymentApplicationNewnumApi, getPaymentApplicationInfoApi, addPaymentApplicationApi, updatePaymentApplicationApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount, watch } from 'vue'
import { checkFormRule, toUperNumber } from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogSelParter } from '@/components/DialogSelParter'
import { DialogPaymentListSel } from '@/components/DialogPaymentListSel'
import { getParterInfoApi, getParterListApi, getSupplierListApi } from '@/api/customer'

const { currentRoute, back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({    
    payee_nick: [{ required: true, message: '请输入收款单位名字', trigger: 'blur' }],
    payment_date: [{ required: true, message: '请输入付款日期', trigger: 'blur' }],
    corp_account: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
    corp_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
    money_use: [{ required: true, message: '请输入款项用途', trigger: 'blur' }],
    monemoney_amounty_use: [{ required: true, message: '请输入金额', trigger: 'blur' }],
})


//入库单数据
const paymentData = reactive(
    {
        book_payment_num:'',
        payment_man_id: '',
        payment_man_name:'',
        payment_date:'',
        payee_nick: '',
        order_nick:'', //类型
        order_list:[],
        supplier_id: '',
        supplier_nick: '',
        parter_id:'',
        parter_nick:'',
        corp_bank:'',
        corp_account :'',
        money_use:'', 
        money_type:'人民币', 
        money_amount:'', 
        
        fsm_can_trig_data: {
            审核触发: [],
            操作触发: ['提交审核']
        }, //审批决策
        fsm_cur_state: '',    //当前节点状态
        fsm_exe_man_name: '',
        fsm_exe_log: '',
        fsm_exe_trig: '',//决策内容
        fsm_log_list: []
    }
)


//获取最新ID
const onChangeID = async () => {
    const ret = await getPaymentApplicationNewnumApi()
    if (ret) {
        console.log(ret)
        paymentData.book_payment_num = ret.data.new_id
    }
}



onMounted(async () => {
    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        title.value = '新建付款申请'

        await onChangeID()

        //处理传递数据
        if (currentRoute.value.query.type != undefined)
        {
            paymentData.order_nick = currentRoute.value.query.type as string
        }
        //如果传递了供应商则需要更新
        if(currentRoute.value.query.supplier_id != undefined)
        {

        }
        //如果传递了委外商则需要更新
        if(currentRoute.value.query.parter_id != undefined)
        {
            const ret = await getParterListApi({
                ids:[currentRoute.value.query.parter_id],
                page:1,
                count:100
            })
            if(ret)
            {
                paymentData.parter_id = ret.data[0].id
                paymentData.parter_nick = ret.data[0].parter_nick
                paymentData.payee_nick = ret.data[0].parter_nick
                paymentData.order_nick = '委外'
                paymentData.corp_account = ret.data[0].corp_account
                paymentData.corp_bank = ret.data[0].corp_bank
            }
        }
        //更新关联单号
        if(currentRoute.value.query.param != undefined)
        {
            let param = JSON.parse(currentRoute.value.query.param as string)
            paymentData.order_list.push(param)
            updateNote()
        }
        //如果传递了供应商则需要更新
        if(currentRoute.value.query.supplier_id != undefined)
        {
            const ret = await getSupplierListApi({
                ids:[currentRoute.value.query.supplier_id],
                page:1,
                count:100
            })
            if(ret)
            {
                paymentData.supplier_id = ret.data[0].id
                paymentData.supplier_nick = ret.data[0].supplier_nick
                paymentData.payee_nick = ret.data[0].supplier_nick
                paymentData.order_nick = '采购'
                paymentData.corp_account = ret.data[0].corp_account
                paymentData.corp_bank = ret.data[0].corp_bank
            }
        }

        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        paymentData.payment_man_id = info.id
        paymentData.payment_man_name = info.resident_name
        //默认日期为今天
        paymentData.payment_date = getTodayDate()

    }
    else {
        if (currentRoute.value.query.mode == 'info') {
            title.value = '查看付款申请单'
        }
        else {
            title.value = '修改付款申请单'
        }


        //查询产品信息 
        const ret = await getPaymentApplicationInfoApi({
            id: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(paymentData, ret.data)
        }
        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }

})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示员窗口变量
const showSelPutinUserDlg = ref(false)
//显示选择员弹窗
const onSelPutinUser = () => {
    showSelPutinUserDlg.value = true
}
//选择入库员回调
const onSelPutinCallback = (id, name) => {
    console.log(id, name)
    paymentData.payment_man_id = id
    paymentData.payment_man_name = name
}


//重新校验输入
const recomputeNum = (pdt) => {

}

//保存
const handleCheck = async (btn) => {
    console.log(paymentData)

    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        console.log('1111',rule)
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    if(paymentData.supplier_nick == '' && paymentData.parter_nick == '')
    {
        ElMessage.warning('请选择供应商或委外商！')
        return
    }

    if (paymentData.order_nick == '采购' && paymentData.supplier_nick == '')
    {
        ElMessage.warning('请选择供应商！')
        return
    }
    if (paymentData.order_nick == '委外' && paymentData.parter_nick == '') {
        ElMessage.warning('请选择委外商！')
        return
    }

    if (paymentData.order_list.length == 0)
    {
        ElMessage.warning('请添加关联单号！')
        return
    }

    if(paymentData.payee_nick == '')
    {
        ElMessage.warning('请选择收款单位！')
        return
    }

    if(paymentData.payment_date == '')
    {
        ElMessage.warning('请选择付款日期！')
        return
    }
    if(paymentData.corp_account == '')
    {
        ElMessage.warning('请选择付款账户！')
        return
    }
    if(paymentData.corp_bank == '')
    {
        ElMessage.warning('请选择开户行！')
        return
    }
    if(paymentData.money_amount == '')
    {
        ElMessage.warning('请输入付款金额！')
        return
    }
    
    
    


    //删除paymentData.pdt_list中入库数量为0的行
    // paymentData.pdt_list = paymentData.pdt_list.filter(pdt=>pdt.入库数量!=0)
    const info = wsCache.get(appStore.getUserInfo)
    paymentData.fsm_exe_man_name = info.resident_name
    paymentData.fsm_exe_trig = btn

    const tmp = cloneDeep(paymentData)

    if (tmp.id == undefined) {
        const ret = await addPaymentApplicationApi(tmp)
        if (ret) {
            ElMessage.success('付款申请单创建成功！')
            back()
        }
    }
    else //修改
    {
        const ret = await updatePaymentApplicationApi(tmp)
        if (ret) {
            ElMessage.success('付款申请单修改成功！')
            back()
        }
    }


}

//显示隐藏选择供应商弹窗
const showSelSupplierDlg = ref(false)
const onSelSupplier = ()=>{
    showSelSupplierDlg.value = true
}
//选择供应商回调
const onSelSupplierCallback = (id,name,nick,tax_type,tax_rate:string,data)=>{
    console.log(id,name,nick,tax_type,tax_rate)
    paymentData.supplier_id = id
    paymentData.supplier_nick = nick
    paymentData.payee_nick = nick
    paymentData.order_nick = '采购'
    paymentData.corp_account = data.corp_account
    paymentData.corp_bank = data.corp_bank
}

//显示隐藏选择委外弹窗
const showSelParterDlg = ref(false)
const onSelParter = ()=>{
    showSelParterDlg.value = true
}
//选择回调
const onSelParterCallback = (id,name,customer)=>{
    console.log(id,name,customer)
    paymentData.parter_id = id
    paymentData.parter_nick = customer.parter_nick
    paymentData.payee_nick = customer.parter_nick
    paymentData.order_nick = '委外'
    paymentData.corp_account = customer.corp_account
    paymentData.corp_bank = customer.corp_bank
}

//显示隐藏选择合并付款单
const showNumListSelDlg = ref(false)
const onSelNumList = ()=>{
    showNumListSelDlg.value = true
}
//选择dingd回调
const onSelNumListCallback = (list)=>{
    console.log(list)
    paymentData.order_list.splice(0, paymentData.order_list.length, ...list)
    updateNote()
}

const updateNote = ()=>{
    //更新备注金额
    let nCount = 0
    let name1 = ''
    let name2 = ''
    let arr1 = []
    let arr2 = []
    for(let one of paymentData.order_list)
    {
        nCount += Number(one.申请金额)
        if(one.关联类型.indexOf('月结')>=0)
        {
            name2 = one.关联类型
            arr2.push(one.月份)
        }
        else
        {
            name1 = one.关联类型
            arr1.push(one.关联单号)
        }
    }
    paymentData.money_amount = nCount
    paymentData.money_use = ''
    if(arr1.length>0)
    {
        paymentData.money_use += name1 + '('+arr1.join(',')+')' 
    }
    if(arr2.length>0)
    {
        paymentData.money_use += name2 + '('+arr2.join(',')+')' 
    }
    paymentData.money_use += '支出'
}

const bitmoney = computed(()=>{  
    return toUperNumber(Number( paymentData.money_amount))
})

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton type="primary" @click="handleCheck('提交审核')"
                v-show="currentRoute.query.type != 'info' && paymentData.fsm_can_trig_data.操作触发.includes('提交审核')">
                提交审核
            </ElButton>
            <ElButton type="primary" @click="handleCheck('保存')"
                v-show="currentRoute.query.type != 'info' && paymentData.fsm_can_trig_data.操作触发.includes('保存')">
                保存
            </ElButton>
        </template>

        <el-card id="check" v-if="paymentData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核' && currentRoute.query.type == 'info'" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ paymentData.fsm_cur_state }}</span>                                
                <ElButton v-show="paymentData.fsm_can_trig_data.审核触发.includes('同意')" type="primary" @click="handleCheck('同意')" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="paymentData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="paymentData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

            
            </div>
            </template>
            <el-input v-model="paymentData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>


        <!-- <div class="flex justify-center"> -->
        <el-form :rules="rules" class="flex justify-center" :model="paymentData" ref="ruleFormRef" >
            <div class="text-center flex-col justify-center items-center w-[75%]">
                <p class="title" style="letter-spacing: 20px;font-size: 28px;">付款申请单</p>
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <div class='flex'>
                    <el-form-item label="供货商:" class="mr-5">
                        <div class="rounded mr-2 border pl-2 pr-1" style="color: #606266;">{{ paymentData.order_nick=='采购'? paymentData.supplier_nick:paymentData.parter_nick }}</div> 
                        <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                                <ElButton size="small" v-if="currentRoute.query.type != 'info'" >
                                    <Icon  icon="iconamoon:search-bold" />
                                </ElButton>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item  @click="onSelSupplier">供应商</el-dropdown-item>
                                    <el-dropdown-item  @click="onSelParter">委外商</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </el-form-item>
                    <el-form-item label="关联单号:" class='mr-5'>
                        <div v-if="paymentData.order_list.length == 1" class="mr-2">{{ paymentData.order_list.map(item => item.关联单号).join(',') }}</div> 
                        <el-tooltip
                                v-if="paymentData.order_list.length>1"
                                class="box-item"
                                effect="dark"
                                :content="paymentData.order_list.map(item => item.关联单号).join(',')"
                                placement="bottom"
                            >
                            <el-tag>组合</el-tag>
                        </el-tooltip>
                        <ElButton size="small" v-if="currentRoute.query.type != 'info' && paymentData.order_nick != ''" @click="onSelNumList">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                    <el-form-item label="申请人:" class="mr-5">
                        <div class="mr-2">{{ paymentData.payment_man_name }}</div> 
                        <ElButton size="small" v-if="currentRoute.query.type != 'info'" @click="onSelPutinUser">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                    <el-form-item label="No:" class="ml-auto">
                        <el-input size="small" :disabled="paymentData.id != undefined" v-model="paymentData.book_payment_num" placeholder="" class="searchItem !w-[100px]" />
                        <ElButton size="small" v-if="currentRoute.query.type != 'info'" @click="onChangeID">
                            <Icon  icon="material-symbols:refresh" />
                        </ElButton>
                    </el-form-item> 
                </div>

                <div>
                    <table class="main" style="border:2px solid #2F4056;color:#2F4056;width: 100%; font-size: 13px;">
                        <tbody>
                            <tr style="height:35px;">
                                <td style="width:20%;">收款单位名称</td>
                                <td style="width:30%;">
                                    <!-- <el-input  v-model="paymentData.payee_nick" /> -->
                                    <div>{{ paymentData.order_nick=='采购'? paymentData.supplier_nick:paymentData.parter_nick }}</div>
                                </td>
                                <td style="width:20%;">付款日期</td>
                                <td>
                                    <!-- <el-input  v-model="paymentData.payment_date" /> -->
                                    <el-date-picker v-model="paymentData.payment_date" type="date" placeholder="" format="YYYY/MM/DD"
                                        value-format="YYYY-MM-DD"  :clearable="false"/>
                                </td>
                            </tr>
                            <tr style="height:35px;">
                                <td style="width:20%;">收款账号</td>
                                <td style="width:30%;">
                                    <el-input  v-model="paymentData.corp_account" />
                                </td>
                                <td style="width:20%;">开户行</td>
                                <td>
                                    <el-input  v-model="paymentData.corp_bank" />
                                </td>
                            </tr>
                            <tr style="height:60px;">
                                <td>款项用途说明</td>
                                <td colspan="3">
                                    <el-input v-model="paymentData.money_use" clearable :autosize="{ minRows: 3, maxRows: 3 }" type="textarea" />
                                </td>
                            </tr>
                            <tr style="height:35px;">
                                <td>金额(小写)</td>
                                <td style="padding: 9px 8px;" class="flex">
                                    <el-select v-model="paymentData.money_type" placeholder="Select" class="w-[100%]">                                        
                                        <el-option value="人民币">人民币</el-option>
                                        <el-option value="港币">港币</el-option>
                                        <el-option value="美元">美元</el-option>
                                        <el-option value="欧元">欧元</el-option>
                                        <el-option value="英镑">英镑</el-option>
                                        <el-option value="日元">日元</el-option>
                                        <el-option value="新台币">新台币</el-option>
                                        <el-option value="泰国铢">泰国铢</el-option>
                                        <el-option value="越南盾">越南盾</el-option>
                                        <el-option value="新加坡元">新加坡元</el-option>
                                        <el-option value="澳大利亚元">澳大利亚元</el-option>
                                        <el-option value="加拿大元">加拿大元</el-option>
                                        <el-option value="印尼卢比">印尼卢比</el-option>
                                        <el-option value="林吉特">林吉特</el-option>
                                        <el-option value="菲律宾比索">菲律宾比索</el-option>
                                        <el-option value="韩国元">韩国元</el-option>
                                        <el-option value="印度卢比">印度卢比</el-option>
                                        <el-option value="埃及镑">埃及镑</el-option>
                                        <el-option value="塔卡">塔卡</el-option>
                                        <el-option value="斯里兰卡卢比">斯里兰卡卢比</el-option>
                                        <el-option value="阿联酋迪拉姆">阿联酋迪拉姆</el-option>
                                        <el-option value="澳门元">澳门元</el-option>
                                        <el-option value="卢布">卢布</el-option>
                                        <el-option value="新土耳其里拉">新土耳其里拉</el-option>
                                        <el-option value="巴基斯坦卢比">巴基斯坦卢比</el-option>
                                        <el-option value="墨西哥比索">墨西哥比索</el-option>
                                        <el-option value="瑞士法郎">瑞士法郎</el-option>
                                        <el-option value="刚果法郎">刚果法郎</el-option>
                                        <el-option value="缅元">缅元</el-option>
                                        <el-option value="秘鲁新索尔">秘鲁新索尔</el-option>

                                    </el-select>
                                    <el-input  v-model="paymentData.money_amount" type="Number" />
                                </td>
                                <td>金额(大写)</td>
                                <td style="text-align:left;"><span id="bigrmb">{{bitmoney}}</span></td>
                            </tr>
                            <tr style="height:75px;">
                                <td>领&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;审<br>导&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;批</td>
                                <td></td>
                                <td>会&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;审<br>计&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;批</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <table style="width:75%;margin:0 auto;">
                        <tbody>
                            <tr>
                                <td></td>
                                <td style="font-size:14px;">领导:
                                </td>
                                <td></td>
                                <td style="font-size:14px;">会计:
                                </td>
                                <td></td>
                                <td style="font-size:14px;">出纳:
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </el-form>


        <!-- 选择质检员 -->
        <DialogUser :param="''" v-model:show="showSelPutinUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelPutinCallback" />

        <!-- 选择供应商弹窗 -->
        <DialogSelSupplier v-model:show="showSelSupplierDlg" :title="t('purchase.sel_supplier')" @on-submit="onSelSupplierCallback"/>
        <!-- 选择客户弹窗 -->
        <DialogSelParter v-model:show="showSelParterDlg" :title="t('parter.sel_parter')" @on-submit="onSelParterCallback"/>
        <!-- 选择供应商弹窗 -->
        <DialogPaymentListSel v-model:show="showNumListSelDlg" :type="paymentData.order_nick" :dir_id="paymentData.order_nick=='采购'?paymentData.supplier_id:paymentData.parter_id" :order_list="paymentData.order_list" :title="t('purchase.sel_supplier')" @on-submit="onSelNumListCallback"/>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

//下半部分表格标题
.table_self_title {
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner) {
    text-align: center;
    font-size: 18px;
}

//扩展文字
.ex_text {
    font-size: 11px;
    color: #646464;
}
:deep(.searchItem .el-input__inner){
    color: red;
}

.main {
    border-collapse: collapse; /* 合并边框，避免双线 */
    width: 100%;
    font-size: 13px;
    border: 2px solid #d3d3d3; /* 表格外框设置为浅灰色 */
    color: #2F4056;
}
.main td {
    border: 1px solid #d3d3d3; /* 单元格边框设置为浅灰色 */
    padding: 0px;
}
.main input, .main select, .main textarea {
    width: 100%;
    box-sizing: border-box; /* 包含内边距和边框的宽度 */
}
</style>