<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi, updatePayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, closeOneTagByPath, downloadFile } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { DialogGetMoney } from '@/components/DialogGetMoney'
import { exportSellReportMonthApi, exportSellReportMonthInfoApi } from '@/api/extra';

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  客户编号:'',
  客户名称:'',
  月份:'',
  收款状态:'',
  对账状态:'',
  已收金额0:'',
  已收金额1:'',
  已开发票0:'',
  已开发票1:'',
  开票状态:'',
  收款日期:['',''],
  业务日期:['',''],
  page: 1,
  count: 20,
  销售人员:'',
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//对账单数据源
const checkData = reactive([])
//当前选中行
const currentRow = ref(null)

const loading = ref(false)
//查询客户数据
const getSellMonthlyCheckListData = async () => {
  let tmp = cloneDeep(searchCondition)
  delete tmp.已收金额0
  delete tmp.已收金额1
  delete tmp.已开发票0
  delete tmp.已开发票1

  tmp.已收金额 = searchCondition.已收金额0+','+searchCondition.已收金额1
  tmp.已开发票 = searchCondition.已开发票0+','+searchCondition.已开发票1
  tmp.收款日期 = searchCondition.收款日期[0]+','+searchCondition.收款日期[1]
  tmp.业务日期 = searchCondition.业务日期[0] + ',' + searchCondition.业务日期[1]
  loading.value = true
  const ret = await getPayBillSellListApi(tmp)
  if(ret)
  {
    checkData.splice(0,checkData.length,...ret.data)
    totleCount.value =  parseInt(ret.count)
  }
  loading.value = false
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getSellMonthlyCheckListData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getSellMonthlyCheckListData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getSellMonthlyCheckListData()
}
//创建新客户月结
const onAddCheck = () => {
  const tmp = currentRoute.value.name?.indexOf('SSS')>=0?'/ssmonthlycheck/sssellmonthlycuslist':'/monthlycheck/sellmonthlycuslist'
  console.log(currentRoute.value.name,tmp) 
  push({
      path: tmp,
      query:{
        id:''
      }
    })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info') {
    const tmp = currentRoute.value.name?.indexOf('SSS')>=0?'/ssmonthlycheck/sssellmonthlycheckinfo':'/monthlycheck/sellmonthlycheckinfo'
  
    closeOneTagByPath(tmp)
    push({
      path: tmp,
      query: {
        id: row.id,
        type: type
      }
    })
  }
  else if (type === 'del') {

    if(row.已收金额>0)
    {
      ElMessage.error('该对账单已收款，不能删除！')
      return
    }

    ElMessageBox.confirm(
      '确定是否删除该对账单？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delPayBillSellApi({ 
          "ids": [row.id] ,
          fsm_exe_trig : '删除'
        })
        if (ret) {
            getSellMonthlyCheckListData()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
  else if(type === '修改备注')
  {
    ElMessageBox.prompt(
      '请输入备注',
      '修改备注',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    )
      .then(async({ value }) => {
        console.log('111111')
        //修改备注
        row.fsm_exe_trig = '保存'
        row.note = value
        const ret = await updatePayBillSellApi(row)
        if (ret) {
          getSellMonthlyCheckListData()
          ElMessage({
            type: 'success',
            message: '更新成功',
          })
        }

      })
      .catch(() => {

      })
  }
  else if(type === '更改发票金额')
  {
    ElMessageBox.prompt(
      '请输入金额,原始金额:'+row.应开发票,
      '修改金额',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: '请输入有效的数字',
      }
    )
      .then(async ({ value }) => {
        if(value == null || value == '')
        {
          ElMessage.error('请输入有效的数字')
          return
        }
        
        //修改发票金额
        row.fsm_exe_trig = '保存'
        row.need_fp_fee = value
        const ret = await updatePayBillSellApi(row)
        if (ret) {
          getSellMonthlyCheckListData()
          ElMessage({
            type: 'success',
            message: '更新成功',
          })
        }

      })
      .catch(() => {

      })
  }
  else if(type == '账户收款')
  {

  }
  else if(type == '导出excel')
  {
     //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前月结单？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true

    const ret = await exportSellReportMonthInfoApi({
        id: row.id
    })
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

  }
}
//设置当前选中行
const setCurrentRow = (value) => {
  currentRow.value = value
}


onMounted(() => {

  if(currentRoute.value.query.month != undefined)
  {
    searchCondition.月份 = currentRoute.value.query.month as string
  }
  

  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  //刷新表格
  getSellMonthlyCheckListData()
})

//显示合计
/*
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['应收金额','已收金额', '应开发票','已开发票'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}*/
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['应收金额', '已收金额', '应开发票', '已开发票', '未收金额'].includes(column.property)) {
            // 针对不同列进行不同的合计计算
            if (column.property === '未收金额') {
                const values = data.map(item => item.应收金额 - item.已收金额);
                if (!values.every(value => isNaN(value))) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return parseFloat((prev + curr).toFixed(2));
                        } else {
                            return prev;
                        }
                    }, 0);
                } else {
                    sums[index] = '/';
                }
            } else {
                const values = data.map(item => Number(item[column.property]));
                if (!values.every(value => isNaN(value))) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return parseFloat((prev + curr).toFixed(2));
                        } else {
                            return prev;
                        }
                    }, 0);
                } else {
                    sums[index] = '/';
                }
            }
        }
    });
    return sums;
};

const curCusID = ref('')
const curCusNick = ref('')
const curMoneyType = ref('')
const showGetMoney = ref(false)
const onShowGetMoney = (row)=>{
  showGetMoney.value = true
  curCusID.value = row.buyer_id
  curCusNick.value = row.buyer_nick+'('+row.币种+')'
  curMoneyType.value = row.币种
}





const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.已收金额0
    delete tmp.已收金额1
    delete tmp.已开发票0
    delete tmp.已开发票1

    tmp.已收金额 = searchCondition.已收金额0+','+searchCondition.已收金额1
    tmp.已开发票 = searchCondition.已开发票0+','+searchCondition.已开发票1
    tmp.收款日期 = searchCondition.收款日期[0]+','+searchCondition.收款日期[1]
    tmp.业务日期 = searchCondition.业务日期[0]+','+searchCondition.业务日期[1]
    const ret = await exportSellReportMonthApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
  <div ref="rootRef">
      <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
        <div class="absolute top-5 left-8">
          <ElButton type="success" @click="onAddCheck">
            <Icon icon="fluent-mdl2:people-add" />
            <div class="pl-2">新增对账单</div>
          </ElButton>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>
        <div class="text-center mb-5 font-bold" style="color:#333">销售月结对账单</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-2 pl-12 bg-light-200">
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">客户编号</div>
            <el-input size="small" v-model="searchCondition.客户编号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">客户名称</div>
            <el-input size="small" v-model="searchCondition.客户名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">销售人员</div>
            <el-input size="small" v-model="searchCondition.销售人员" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">月份</div>
            <!-- <el-date-picker size="small" v-model="searchCondition.月份" type="month" placeholder="选择月份" class="searchItem" value-format="YYYY-MM"/> -->
            <el-input size="small" v-model="searchCondition.月份" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">收款状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.收款状态" placeholder="" >
              <el-option v-for="item in ['未收款','部分收款','完全收款','未收款+部分收款']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">对账状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.对账状态" placeholder="" >
              <el-option v-for="item in ['账单生成中','对账中','已对账']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">已收金额</div>
            <el-input size="small"  v-model="searchCondition.已收金额0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.已收金额1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">已开发票</div>
            <el-input size="small"  v-model="searchCondition.已开发票0" placeholder="" class="!w-[60px]" type="number"/>
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small"  v-model="searchCondition.已开发票1" placeholder="" class="!w-[60px]" type="number" />
          </div>
          <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">开票状态</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.开票状态" placeholder="" >
              <el-option v-for="item in ['未开票','部分开票','完全开票','未开票+部分开票']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">收款日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.收款日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">业务日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.业务日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>


          <div class="flex justify-end items-center mr-6 mt-4">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
            <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
              <Icon icon="tabler:search" />
              <div class="ml-1">查询</div>
            </ElButton>
            <ElButton class="ml-4" type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="ml-1">清除</div>
            </ElButton>
          </div>
        </div>

        <el-table v-loading.lock="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="checkData"
          style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries" @current-change="setCurrentRow" border stripe>
          <el-table-column align="center" show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" >
            <template #default="scope">
              {{ scope.$index+1 }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="buyer_name" :label="'客户编号'" />
          <el-table-column align="center" show-overflow-tooltip prop="buyer_nick" :label="'客户名称'" />
          <el-table-column align="center" show-overflow-tooltip prop="all_sell_man_name" :label="'销售人员'" />
          <el-table-column align="center" show-overflow-tooltip prop="paybill_date" :label="'月份'" />
          <el-table-column align="center" show-overflow-tooltip prop="币种" :label="'币种'" />
          <el-table-column align="center"  show-overflow-tooltip prop="应收金额" :label="'应收金额'" />
          <!-- <el-table-column align="center" show-overflow-tooltip prop="预存款金额" :label="'预存款'" /> -->
          <el-table-column align="center" show-overflow-tooltip prop="已收金额" :label="'已收金额'" />
          <el-table-column align="center" show-overflow-tooltip prop="未收金额" :label="'未收金额'" >
            <template #default="scope">
              {{ scope.row.应收金额-scope.row.已收金额 }}
            </template>              
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="应开发票" :label="'应开发票'" />
          <el-table-column align="center" show-overflow-tooltip prop="已开发票" :label="'已开发票'" />
          <el-table-column align="center" show-overflow-tooltip prop="fsm_cur_state" :label="'状态'" />
          <el-table-column align="center" show-overflow-tooltip prop="create_man_name" :label="'创建人'" />
          <el-table-column align="center" show-overflow-tooltip prop="prepaid_date" :label="'预收款日期'" />
          <el-table-column align="center" show-overflow-tooltip prop="note" :label="'备注'" />
          
          <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
            <template #default="scope">
              <el-dropdown trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <div class="flex flex-wrap w-[200px]">
                    <el-dropdown-item @click="handleOper('info', scope.row)">查看</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('导出excel', scope.row)">导出excel</el-dropdown-item>
                    <!-- <el-dropdown-item @click="handleOper('发票', scope.row)">发票({{ 0 }})</el-dropdown-item> -->
                    <el-dropdown-item @click="handleOper('更改发票金额', scope.row)">更改发票金额</el-dropdown-item>
                    <el-dropdown-item @click="handleOper('修改备注', scope.row)">修改备注</el-dropdown-item>
                    <el-dropdown-item @click="onShowGetMoney(scope.row)">账户收款</el-dropdown-item>
                    <!-- <el-dropdown-item @click="handleOper('get_mo预存款抵扣ney_from_pre', scope.row)">预存款抵扣</el-dropdown-item> -->
                    <el-dropdown-item v-if='scope.row.已收金额==0' @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                  </div>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>

    <DialogGetMoney v-model:show="showGetMoney" :cus_id="curCusID" :cus_nick="curCusNick" :money_type="curMoneyType" @on-submit="getSellMonthlyCheckListData"/>
  </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper)
{
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}

</style>
