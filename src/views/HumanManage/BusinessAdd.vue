<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElPopconfirm,ElRadio, ElRadioGroup, ElDropdown, ElDropdownItem, ElDropdownMenu, ElTag, ElDatePicker, ElSelect, ElOption, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, nextTick } from 'vue'
import { getStoreListApi, getOemPutinNewnumApi, getOemDrawinInfoApi, getOemPutinInfoApi, addOemPutinApi, updateOemPutinApi, getPaymentApplicationNewnumApi, getPaymentApplicationInfoApi, addPaymentApplicationApi, updatePaymentApplicationApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount, watch } from 'vue'
import { checkFormRule, toUperNumber } from '@/api/tool'
import { DialogUserEx } from '@/components/DialogUserEx'
import { addOvertimeApi, delOvertimeApi, getDepartmentListApi, getOvertimeInfoApi, getOvertimeNewnumApi, getOvertimeListApi, updateOvertimeApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogSelParter } from '@/components/DialogSelParter'
import { DialogPaymentListSel } from '@/components/DialogPaymentListSel'
import { getParterInfoApi, getParterListApi, getSupplierListApi } from '@/api/customer'

const { currentRoute, back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    payee_nick: [{ required: true, message: '请输入收款单位名字', trigger: 'blur' }],
    payment_date: [{ required: true, message: '请输入付款日期', trigger: 'blur' }],
    corp_account: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
    corp_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
    money_use: [{ required: true, message: '请输入款项用途', trigger: 'blur' }],
    monemoney_amounty_use: [{ required: true, message: '请输入金额', trigger: 'blur' }],
})

//加班数据
const overtimeData = reactive(
    {
        "user_overtime_num": '',
        "overtime_man_id": '',
        "type": '工作日加班',
        "result": '发薪',
        "source": '',
        "start_time": '',
        "end_time": '',
        "overtime_unit": '',
        "overtime_length": '',
        "note": '',
        start_end: [],
        fsm_can_trig_data:{
            审核触发:[],
            操作触发:['保存']
        }, //审核决策
        fsm_cur_state:'保存',    //当前节点状态
        fsm_exe_man_name:'',
        fsm_exe_log:'',
        fsm_exe_trig:'',//决策内容
        fsm_log_list:[]
    }
)



//获取最新ID
const onChangeID = async () => {
    const ret = await getOvertimeNewnumApi()
    if (ret) {
        console.log(ret)
        overtimeData.user_overtime_num = ret.data.new_id
    }
}



onMounted(async () => {
    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        title.value = '新建加班单'

        await onChangeID()


        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        overtimeData.user_name = info.username
        overtimeData.user_nick = info.resident_name
        //默认日期为今天
        overtimeData.start_time = getTodayDate()
        overtimeData.end_time = getTodayDate()

    }
    else {
        if (currentRoute.value.query.mode == 'info') {
            title.value = '查看加班单'
        }
        else {
            title.value = '修改加班单'
        }


        //查询产品信息 
        const ret = await getOvertimeInfoApi({
            user_overtime_num: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(overtimeData, ret.data)
            overtimeData.user_nick = overtimeData.overtime_man_name
            arrayAllUser.push({
                username: overtimeData.overtime_man_username,
                resident_name: overtimeData.overtime_man_name,
            })
            overtimeData.start_end = [overtimeData.start_time, overtimeData.end_time]
            console.log('---',overtimeData)
        }
        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }

})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示员窗口变量
const showSelUserDlg = ref(false)
//显示选择员弹窗
const onSelUser = () => {
    showSelUserDlg.value = true
}

const arrayAllUser = reactive([]) //所有加班员工
//选择入库员回调
const onSelCallback = (array) => {
    console.log('laile', array)
    arrayAllUser.splice(0, arrayAllUser.length)
    arrayAllUser.push(...array)
    return
}


//保存
const handleCheck = async (btn) => {
    console.log(overtimeData)

    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        console.log('1111', rule)
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    if (arrayAllUser.length <= 0) {
        ElMessage.warning('请选择申请人')
        return
    }

    if (overtimeData.holiday_type == '') {
        ElMessage.warning('请选择假期类别')
        return
    }
    if (overtimeData.start_end[0] == '') {
        ElMessage.warning('请选择加班日期')
        return
    }

    console.log('111111111111111')
    //批量创建加班
    for (let i = 0; i < arrayAllUser.length; i++) {
        const tmp = cloneDeep(overtimeData)
        tmp.overtime_man_id = arrayAllUser[i].id
        tmp.user_name = arrayAllUser[i].username
        tmp.user_nick = arrayAllUser[i].resident_name
        tmp.start_time = tmp.start_end[0]
        tmp.end_time = tmp.start_end[1]
        delete tmp.start_end
        if (i > 0) { //修改修改编号
            tmp.user_overtime_num = tmp.user_overtime_num + '_' + i
        }
        const info = wsCache.get(appStore.getUserInfo)
        tmp.fsm_exe_man_name = info.resident_name
        tmp.fsm_exe_trig = btn

        if (tmp.id == undefined) {
            const ret = await addOvertimeApi(tmp)
            if (ret) {
                ElNotification({
                    title: t('msg.notify'),
                    message: tmp.user_nick + '的加班申请成功！',
                    type: 'success',
                    duration: 3000
                })
            }
        }
        else //修改
        {
            const ret = await updateOvertimeApi(tmp)
            if (ret) {
                ElNotification({
                    title: t('msg.notify'),
                    message: tmp.user_nick + '的加班修改成功！',
                    type: 'success',
                    duration: 3000
                })
            }
        }

    }
    back()

}

const delOvertime = async () => {

    ElMessageBox.confirm(
        '确定是否删除该加班？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
        .then(async () => {
            const ret = await delOvertimeApi({ "ids": [overtimeData.id] });
            if (ret) {
                ElMessage.success('删除成功')
                back()
            }
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
}


const nOvertimeDayCount = ref(0)

const onDateChange = (value) => {
    console.log(value)
    //计算相差天数
    const start = new Date(value[0])
    const end = new Date(value[1])
    const days = (end - start) / (1000 * 60 * 60 * 24) + 1
    nOvertimeDayCount.value = Math.ceil(days)
}


</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && overtimeData.fsm_can_trig_data.操作触发.includes('保存')" type="primary" @click="handleCheck('保存')" >
                保存
            </ElButton>
            <ElButton v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && overtimeData.fsm_can_trig_data.操作触发.includes('保存')" type="danger" @click="delOvertime" >
                删除
            </ElButton>
        </template>
        <el-card id="check" shadow="never" v-if="overtimeData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核'" class="w-[100%] h-[100%] mt-4">
            审核原因：
            <el-input v-model="overtimeData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
            <div class="mt-4 flex justify-end">
                <div class="flex items-center text-12px">
                    <span>当前节点:</span>
                    <span class="text-red-500 mr-3">{{ overtimeData.fsm_cur_state }}</span>
                </div>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in purchaseData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="overtimeData.fsm_can_trig_data.审核触发.includes('确认')" type="success" @click="handleCheck('确认')" >确认</ElButton>
                <el-popconfirm  title="是否驳回该申请?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="overtimeData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该申请?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="overtimeData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>
            </div>
        </el-card>


        <!-- <div class="flex justify-center"> -->
        <el-form :rules="rules" class="flex justify-center" :model="overtimeData" ref="ruleFormRef">
            <div class="text-center flex-col justify-center items-center w-[75%]">
                <p class="title" style="letter-spacing: 20px;font-size: 28px;">加班单</p>
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <div class='flex'>

                    <el-form-item label="No:" class="ml-auto">
                        <el-input size="small" :disabled="overtimeData.id != undefined"
                            v-model="overtimeData.user_overtime_num" placeholder="" class="searchItem !w-[100px]" />
                        <ElButton size="small" v-if="currentRoute.query.type != 'info'" @click="onChangeID">
                            <Icon icon="material-symbols:refresh" />
                        </ElButton>
                    </el-form-item>
                </div>


                <div>
                    <table class="main" style="border:2px solid #2F4056;color:#2F4056;width: 100%; font-size: 13px;">
                        <tbody>
                            <tr>
                                <th style="height:45px;width:18%">加班员工</th>
                                <td style="width:82%">
                                    <div class="flex justify-center items-center">
                                        <div class="mr-2 break-words">{{ arrayAllUser.map(user => user.username).join(',')
                                            }}
                                        </div>
                                        <ElButton size="small"
                                            v-if="currentRoute.query.type != 'info' && currentRoute.query.type != 'edit'"
                                            @click="onSelUser">
                                            <Icon icon="iconamoon:search-bold" />
                                        </ElButton>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">加班类型</th>
                                <td>
                                    <el-select class="searchItem" v-model="overtimeData.type" placeholder="">
                                        <el-option v-for="item in ['工作日加班', '休息日加班', '节假日加班', '居家值班']" :key="item"
                                            :label="item" :value="item" />
                                    </el-select>
                                    <div id="vacationtypediv" style="display:inline-block;"></div>
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">加班起止</th>
                                <td  style="text-align:left;">
                                    <div class="flex justify-center items-center">


                                        <el-date-picker v-model="overtimeData.start_end" type="datetimerange"
                                            range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
                                            value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss"
                                            @change="onDateChange" />
                                    </div>
         
                                </td>
                                <!-- <td>
                                    共<span class="text-red-500 font-black">{{ nOvertimeDayCount }}</span>天
                                </td> -->
                            </tr>
                            <tr>
                                <th style="height:45px;">加班处理</th>
                                <td>
                                    <el-radio-group v-model="overtimeData.result">
                                        <el-radio v-for="item in ['发薪', '调休', '无']" :key="item" :value="item"
                                            :label="item" />
                                    </el-radio-group>
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">备注</th>
                                <td colspan="3" style="height:100%;">
                                    <el-input class="p-3" v-model="overtimeData.note" clearable
                                        :autosize="{ minRows: 6, maxRows: 6 }" type="textarea" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>


            </div>
        </el-form>


        <!-- 选择质检员 -->
        <DialogUserEx :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelCallback" />

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

//下半部分表格标题
.table_self_title {
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner) {
    text-align: center;
    font-size: 18px;
}

//扩展文字
.ex_text {
    font-size: 11px;
    color: #646464;
}

:deep(.searchItem .el-input__inner) {
    color: red;
}

.main {
    border-collapse: collapse;
    /* 合并边框，避免双线 */
    width: 50%;
    font-size: 13px;
    border: 2px solid #d3d3d3;
    /* 表格外框设置为浅灰色 */
    color: #2F4056;
}

.main td {
    border: 1px solid #d3d3d3;
    /* 单元格边框设置为浅灰色 */
    padding: 0px;
}

.main input,
.main select,
.main textarea {
    width: 100%;
    box-sizing: border-box;
    /* 包含内边距和边框的宽度 */
}
</style>