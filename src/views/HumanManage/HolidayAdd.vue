<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElPopconfirm,ElDropdown, ElDropdownItem, ElDropdownMenu, ElTag, ElDatePicker, ElSelect, ElOption, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, nextTick } from 'vue'
import { getStoreListApi, getOemPutinNewnumApi, getOemDrawinInfoApi, getOemPutinInfoApi, addOemPutinApi, updateOemPutinApi, getPaymentApplicationNewnumApi, getPaymentApplicationInfoApi, addPaymentApplicationApi, updatePaymentApplicationApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount, watch } from 'vue'
import { checkFormRule, toUperNumber } from '@/api/tool'
import { DialogUserEx } from '@/components/DialogUserEx'
import { addHolidayApi, delHolidayApi, getDepartmentListApi, getHolidayInfoApi, getHolidayNewnumApi, getHolidayTypeListApi, updateHolidayApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { DialogSelSupplier } from '@/components/DialogSelSupplier'
import { DialogSelParter } from '@/components/DialogSelParter'
import { DialogPaymentListSel } from '@/components/DialogPaymentListSel'
import { getParterInfoApi, getParterListApi, getSupplierListApi } from '@/api/customer'

const { currentRoute, back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({    
    payee_nick: [{ required: true, message: '请输入收款单位名字', trigger: 'blur' }],
    payment_date: [{ required: true, message: '请输入付款日期', trigger: 'blur' }],
    corp_account: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
    corp_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
    money_use: [{ required: true, message: '请输入款项用途', trigger: 'blur' }],
    monemoney_amounty_use: [{ required: true, message: '请输入金额', trigger: 'blur' }],
})

//请假数据
const holidayData = reactive(
    {
        user_holiday_num:'',
        user_name: '',
        user_nick:'',
        holiday_type:'',
        money_type:'不带薪',
        time_type:'全天',
        start_date:'',
        end_date:'',
        note:'',
        pic_list:'',
        file_list: '',
        start_end: [,],
        user_depts_name:'',
    }
)



//获取最新ID
const onChangeID = async () => {
    const ret = await getHolidayNewnumApi()
    if (ret) {
        console.log(ret)
        holidayData.user_holiday_num = ret.data.new_id
    }
}



onMounted(async () => {
    getHolidayTypeList()
    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        title.value = '新建请假单'

        await onChangeID()


        //设置入库人员
        const info = wsCache.get(appStore.getUserInfo)
        holidayData.user_name = info.username
        holidayData.user_nick = info.resident_name
        //默认日期为今天
        holidayData.start_date = getTodayDate()
        holidayData.end_date = getTodayDate()

    }
    else {
        if (currentRoute.value.query.mode == 'info') {
            title.value = '查看请假单'
        }
        else {
            title.value = '修改请假单'
        }


        //查询产品信息 
        const ret = await getHolidayInfoApi({
            user_holiday_num: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(holidayData, ret.data)
            holidayData.user_nick = holidayData.user_resident_name
            arrayAllUser.push({
                username: holidayData.user_name,
                resident_name:holidayData.user_resident_name,
            })
            holidayData.start_end = [holidayData.start_date, holidayData.end_date]

        }
        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }

})

//unmounted的时候移除监听
onBeforeUnmount(() => {

})



//显示员窗口变量
const showSelUserDlg = ref(false)
//显示选择员弹窗
const onSelUser = () => {
    showSelUserDlg.value = true
}

const arrayAllUser = reactive([]) //所有请假员工
//选择入库员回调
const onSelCallback = (array) => {
    console.log('laile', array)
    arrayAllUser.splice(0, arrayAllUser.length)
    arrayAllUser.push(...array)
    return
}


//保存
const handleCheck = async (btn) => {
    console.log(holidayData)

    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        console.log('1111',rule)
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    // if(holidayData.user_name == '' )
    // {
    //     ElMessage.warning('请选择申请人')
    //     return
    // }
    if (arrayAllUser.length <= 0)
    {
        ElMessage.warning('请选择申请人')
        return
    }

    if (holidayData.holiday_type == '' )
    {
        ElMessage.warning('请选择假期类别')
        return
    }
    if (holidayData.start_end[0] == '' )
    {
        ElMessage.warning('请选择请假日期')
        return
    }
    // if (holidayData.start_date == '' )
    // {
    //     ElMessage.warning('请选择开始日期')
    //     return
    // }
    // if (holidayData.end_date == '' )
    // {
    //     ElMessage.warning('请选择结束日期')
    //     return
    // }


    // const info = wsCache.get(appStore.getUserInfo)
    // holidayData.fsm_exe_man_name = info.resident_name
    // holidayData.fsm_exe_trig = btn

    //批量创建请假
    for (let i = 0; i < arrayAllUser.length; i++) {
        const tmp = cloneDeep(holidayData)
        tmp.user_name = arrayAllUser[i].username
        tmp.user_nick = arrayAllUser[i].resident_name
        tmp.start_date = tmp.start_end[0]
        tmp.end_date = tmp.start_end[1]
        if (i>0) { //修改修改编号
            tmp.user_holiday_num = tmp.user_holiday_num+'_' + i
        }   
        if (tmp.id == undefined) {
            const ret = await addHolidayApi(tmp)
            if (ret) {
                ElNotification({
                    title: t('msg.notify'),
                    message: tmp.user_nick+'的请假申请成功！',
                    type: 'success',
                    duration: 3000
                })
            }
        }
        else //修改
        {
            const ret = await updateHolidayApi(tmp)
            if (ret) {
                ElNotification({
                    title: t('msg.notify'),
                    message: tmp.user_nick+'的请假修改成功！',
                    type: 'success',
                    duration: 3000
                })
            }
        }

    }
    back()

    // const tmp = cloneDeep(holidayData)
    // tmp.start_date = tmp.start_end[0]
    // tmp.end_date = tmp.start_end[1]

    // if (tmp.id == undefined) {
    //     const ret = await addHolidayApi(tmp)
    //     if (ret) {
    //         ElMessage.success('创建申请成功！')
    //         back()
    //     }
    // }
    // else //修改
    // {
    //     const ret = await updateHolidayApi(tmp)
    //     if (ret) {
    //         ElMessage.success('修改成功')
    //         back()
    //     }
    // }


}

const delHoliday = async () => {

    ElMessageBox.confirm(
        '确定是否删除该假期？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
    .then(async () => {
        const ret = await delHolidayApi({ "ids": [holidayData.id] });
        if (ret) {
            ElMessage.success('删除成功')
            back()
        }
    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: t('msg.delChannel'),
        })
    })
}

const holidayTypeList = reactive([])
//获取假期数据
const getHolidayTypeList = async () => {
  const res = await getHolidayTypeListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    holidayTypeList.splice(0,holidayTypeList.length,...res.data)
  }

}

const nHolidayDayCount = ref(0)

const onDateChange = (value) => {
    console.log(value)
    //计算相差天数
    const start = new Date(value[0])
    const end = new Date(value[1])
    const days = (end - start) / (1000 * 60 * 60 * 24)
    nHolidayDayCount.value = Math.ceil(days)
}


</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton type="primary" @click="handleCheck('保存')"
                v-show="currentRoute.query.type != 'info' ">
                保存
            </ElButton>
            <ElButton type="danger" @click="delHoliday"
                v-show="currentRoute.query.type == 'edit' ">
                删除
            </ElButton>
        </template>


        <!-- <div class="flex justify-center"> -->
        <el-form :rules="rules" class="flex justify-center" :model="holidayData" ref="ruleFormRef" >
            <div class="text-center flex-col justify-center items-center w-[75%]">
                <p class="title" style="letter-spacing: 20px;font-size: 28px;">请假单</p>
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <hr style="width:350px;border: 0.2px solid #999;background-color:#999;height:0.5px;margin:2px auto;">
                <div class='flex'>
                    
                    <el-form-item label="No:" class="ml-auto">
                        <el-input size="small" :disabled="holidayData.id != undefined" v-model="holidayData.user_holiday_num" placeholder="" class="searchItem !w-[100px]" />
                        <ElButton size="small" v-if="currentRoute.query.type != 'info'" @click="onChangeID">
                            <Icon  icon="material-symbols:refresh" />
                        </ElButton>
                    </el-form-item> 
                </div>


                <div>
                    <table class="main" style="border:2px solid #2F4056;color:#2F4056;width: 100%; font-size: 13px;">
                        <tbody>
                            <tr>
                                <th style="height:45px;width:18%">申请人</th>
                                <td style="width:35%" >
                                    <div class="flex justify-center items-center">
                                        <div class="mr-2 break-words">{{ arrayAllUser.map(user=>user.username).join(',') }}</div> 
                                        <ElButton size="small" v-if="currentRoute.query.type != 'info' &&  currentRoute.query.type != 'edit'" @click="onSelUser">
                                            <Icon  icon="iconamoon:search-bold" />
                                        </ElButton>    
                                    </div>
                                </td>
                                <th style="width:15%">部门</th>
                                <td style="width:32%;">
                                    <div class="mr-2">{{ holidayData.user_depts_name }}</div> 
                                    
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">假期类别</th>
                                <td>
                                    <el-select  class="searchItem" v-model="holidayData.holiday_type" placeholder="">
                                        <el-option v-for="item in holidayTypeList" :key="item.nick" :label="item.nick"
                                            :value="item.nick" />
                                    </el-select>         
                                    <div id="vacationtypediv" style="display:inline-block;"></div>
                                </td>

                                <th>是否带薪</th>
                                <td>
                                    <el-select  class="searchItem" v-model="holidayData.money_type" placeholder="">
                                        <el-option v-for="item in ['带薪','不带薪']" :key="item" :label="item"
                                            :value="item" />
                                    </el-select>  
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">请假起止</th>
                                <td colspan="2" style="text-align:left;">
                                    <div class="flex justify-center items-center">
                                        <!-- <el-select  class="searchItem" v-model="holidayData.time_type" placeholder="请选择请假类型">
                                            <el-option v-for="item in ['全天','上午','下午']" :key="item" :label="item"  :value="item" />
                                        </el-select>  
                                        <span class="ml-2">从</span>
                                        <el-date-picker   v-model="holidayData.start_date"  range-separator="到"
                                        start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                                        <span class="mr-1">到</span>
                                        <el-date-picker   v-model="holidayData.end_date"  range-separator="到"
                                        start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" /> -->
                                        <!-- <div class="flex">
                                            <span>共</span>
                                            <span  style="color:red;font-weight:bold;"> ? </span>
                                            <span>天</span>
                                        </div> -->
                                        <el-date-picker
                                            v-model="holidayData.start_end"
                                            type="datetimerange"
                                            range-separator="至"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                            format="YYYY-MM-DD HH:mm:ss"
                                            @change="onDateChange"
                                        />
                                    </div>
                                </td>
                                <td>
                                    共<span class="text-red-500 font-black">{{ nHolidayDayCount }}</span>天
                                </td>
                            </tr>
                            <tr>
                                <th style="height:45px;">备注</th>
                                <td colspan="3" style="height:100%;">
                                    <el-input class="p-3" v-model="holidayData.note" clearable :autosize="{ minRows: 6, maxRows: 6 }" type="textarea" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>


            </div>
        </el-form>


        <!-- 选择质检员 -->
        <DialogUserEx :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelCallback" />

     </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 15% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

//下半部分表格标题
.table_self_title {
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner) {
    text-align: center;
    font-size: 18px;
}

//扩展文字
.ex_text {
    font-size: 11px;
    color: #646464;
}
:deep(.searchItem .el-input__inner){
    color: red;
}

.main {
    border-collapse: collapse; /* 合并边框，避免双线 */
    width: 50%;
    font-size: 13px;
    border: 2px solid #d3d3d3; /* 表格外框设置为浅灰色 */
    color: #2F4056;
}
.main td {
    border: 1px solid #d3d3d3; /* 单元格边框设置为浅灰色 */
    padding: 0px;
}
.main input, .main select, .main textarea {
    width: 100%;
    box-sizing: border-box; /* 包含内边距和边框的宽度 */
}
</style>