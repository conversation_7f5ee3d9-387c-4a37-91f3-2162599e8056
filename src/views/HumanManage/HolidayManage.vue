<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTooltip,ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delHolidayApi, getHolidayListApi, getHolidayUserListApi } from '@/api/usermanage';

const { push,currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    年份: '2024',
    月份: '01月',
    员工编号: '',
    员工姓名: '',
    部门:'',
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})


//对账单数据源
const holidayData = reactive([])

//获取明细
const getHolidayList = async (page = 1) => {
    Object.assign(dayData, generateDaysForCurrentMonth(Number(searchCondition.年份), Number(searchCondition.月份.replace('月', ''))));
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  tmp.月份 = tmp.月份.replace('月', '')
  const ret = await getHolidayUserListApi(tmp)
  if (ret) {
      holidayData.splice(0, holidayData.length, ...ret.data);
      
    totleCount.value = ret.count
  }
}


//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getHolidayList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getHolidayList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getHolidayList(val)
}
//创建
const onAddHoliday = () => {
    push({
      path: '/holidaymanage/holiadyadd',
      query: {
        id: '',
      }
    })
}

//处理表格对象操作
const handleOper = (type, row) => {
  if (type === 'edit' || type == 'info') {
    push({
      path: '/holidaymanage/holiadyadd',
      query: {
        id: row[0].split(',')[0],
        type: type
      }
    })
  }
  else if (type === '删除') {

    ElMessageBox.confirm(
        '确定是否删除该假期？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
    .then(async () => {

        const ret = await delHolidayApi({ "ids": [row.id] })
        if (ret) {
            getHolidayList()

            ElMessage({
                type: 'success',
                message: t('msg.delOK'),
            })
        }


    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: t('msg.delChannel'),
        })
    })
  }
  
  else if(type === '修改')
  {
    ElMessageBox.confirm(
        '确定是否修改该流水？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
    .then(async () => {

      showDetail.value = true
      curDetailData.value = row
      showMode.value = 'edit'

    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: '取消修改',
        })
    })



  }
}
const dayData = reactive([])

onMounted(async () => {

  const currentDate = new Date();
  const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
  searchCondition.月份 = `${currentMonth}月`;
  
  await getHolidayList()
  //监听键盘事件
  const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });


})


    // 生成当前月的日期和星期几数组
const generateDaysForCurrentMonth = (year: number, monthNumber: number) => {
  const firstDay = new Date(year, monthNumber - 1, 1);
  const lastDay = new Date(year, monthNumber, 0);
  const days = [];
  let current = firstDay;
  while (current <= lastDay) {
    const weekday = ['日', '一', '二', '三', '四', '五', '六'][current.getDay()];
    days.push({ month: monthNumber, date: current.getDate(), weekday });
    current.setDate(current.getDate() + 1);
  }
  return days;
};

const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    console.log('======', row, column, rowIndex, columnIndex)
    return { backgroundColor: 'yellow' }; 
}
</script>

<template>
  <div ref="rootRef" class="">
    <div class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
      <div class="absolute top-10 left-8">
        <ElButton type="success" @click="onAddHoliday">
          <Icon icon="fluent-mdl2:people-add" />
          <div class="pl-2">新增请假单</div>
        </ElButton>
      </div>
      <div class="text-center mb-5 font-bold pt-5" style="color:#333">{{ searchCondition.年份 + '年' + searchCondition.月份}}的请假记录
      </div>
      <div class="flex flex-col justify-center items-center">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">年份</div>
          <el-date-picker @change="getHolidayList" @calendar-change="getHolidayList" :clearable="false" v-model="searchCondition.年份"
            type="year" placeholder="Pick a year" format="YYYY" value-format="YYYY" />
        </div>

        <el-radio-group @change="(value) => { getHolidayList() }" v-model="searchCondition.月份">
          <el-radio-button v-for="month in 12" :key="month" :label="`${String(month).padStart(2, '0')}月`"
            :value="month" />
        </el-radio-group>
      </div>

      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-1 pl-5 bg-light-200">
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">员工账号</div>
          <el-input size="small" v-model="searchCondition.员工编号" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">员工姓名</div>
          <el-input size="small" v-model="searchCondition.员工姓名" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">部门</div>
          <el-input size="small" v-model="searchCondition.部门" placeholder="" class="searchItem" />
        </div>
        

        <div class="flex justify-end items-center mr-6">
          <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="ml-1">查询</div>
          </ElButton>
          <ElButton class="ml-4" type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="ml-1">清除</div>
          </ElButton>
        </div>
      </div>

      <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="holidayData"
        style="width: 100%;color: #666666;" border stripe>
        <el-table-column align="center" show-overflow-tooltip fixed prop="员工姓名" :label="'姓名'" :min-width="120"/>
        <el-table-column align="center" show-overflow-tooltip fixed prop="部门" :label="'部门'" :min-width="200"/>
        
        <!-- 动态生成日期和星期几的表头列 -->
        <el-table-column  v-for="day,index in dayData" :key="day.date" align="center" :label="'星期'+day.weekday" >
            <el-table-column  align="center" :label="day.month+'.'+day.date" :cell-style="cellStyle"   >
                <template #default="scope">
                    <el-tooltip
                        class="box-item"
                        effect="dark"
                        :content="scope.row.day_list[index][3]"
                        placement="top"
                    >
                        <div v-if="scope.row.day_list[index][1]!=''" class="cursor-pointer" :style="{ backgroundColor: 'rgb(255, 140, 105)'}" @click="handleOper('edit',scope.row.day_list[index])">
                            {{ scope.row.day_list[index][1]}}
                        </div>
                    </el-tooltip>

                </template>
            </el-table-column>
        </el-table-column>

    
        <!-- <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="bottom">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <div class="flex flex-wrap w-[200px]">
                  <el-dropdown-item @click="handleOper('流水明细', scope.row)">流水明细</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('修改', scope.row)">修改</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('删除', scope.row)">删除</el-dropdown-item>
                </div>
              </template>
            </el-dropdown>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
        layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
 
    </div>
    <DialogMoneyDetail v-model:show="showDetail" :data="curDetailData" :type="showMode" @on-submit="getFinanceWaterList"/>
  </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #f6f6f6 !important;
  color: #333;
  font-weight: 600;
}

.nameStyle {
  color: rgb(60, 60, 255);
  color: #00BA80;
  cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
  background-color: #EAF8F2 !important;
  --el-table-current-row-bg-color: #EAF8F2 !important;
  --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.25rem;
  /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}

.searchTitle::after {
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.searchItem {
  width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
  font-size: 12px;
  white-space: normal;
  color: black;
  padding-left: 1px;
  padding-right: 1px;
}
</style>
