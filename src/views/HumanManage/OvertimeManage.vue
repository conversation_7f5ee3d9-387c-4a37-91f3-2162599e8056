<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTooltip, ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delOvertimeApi, getOvertimeListApi } from '@/api/usermanage';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    年份: '2024',
    月份: '01月',
    确认情况:'',
    overtime_man_name: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})


//数据源
const overtimeData = reactive([])

//获取明细
const getOvertimeList = async (page = 1) => {
    // Object.assign(dayData, generateDaysForCurrentMonth(Number(searchCondition.年份), Number(searchCondition.月份.replace('月', ''))));
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)
    tmp.月份 = tmp.月份.replace('月', '')
    const ret = await getOvertimeListApi(tmp)
    if (ret) {
        overtimeData.splice(0, overtimeData.length, ...ret.data);
        totleCount.value = ret.count
    }
}


//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getOvertimeList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getOvertimeList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getOvertimeList(val)
}
//创建
const onAddOvertime = () => {
    push({
        path: '/overtimemanage/overtimeadd',
        query: {
            id: '',
        }
    })
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type === 'edit' || type == 'info' || type == 'check') {
        push({
            path: '/overtimemanage/overtimeadd',
            query: {
                id: row.user_overtime_num,
                type: type,
                cmd:bCheckMode.value?'审核':''
            }
        })
    }
    else if (type === '删除') {

        ElMessageBox.confirm(
            '确定是否删除该加班？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delOvertimeApi({ "ids": [row.id] })
                if (ret) {
                    getOvertimeList()

                    ElMessage({
                        type: 'success',
                        message: t('msg.delOK'),
                    })
                }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}
const dayData = reactive([])

//审核模式
const bCheckMode = ref(false)
onMounted(async () => {

    console.log(currentRoute.value.name)
    if (currentRoute.value.name == 'OvertimeManageCheck') {
        searchCondition.确认情况 = '未确认'
        bCheckMode.value = true
    }

    const currentDate = new Date();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    searchCondition.月份 = `${currentMonth}月`;

    await getOvertimeList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });


})


// // 生成当前月的日期和星期几数组
// const generateDaysForCurrentMonth = (year: number, monthNumber: number) => {
//     const firstDay = new Date(year, monthNumber - 1, 1);
//     const lastDay = new Date(year, monthNumber, 0);
//     const days = [];
//     let current = firstDay;
//     while (current <= lastDay) {
//         const weekday = ['日', '一', '二', '三', '四', '五', '六'][current.getDay()];
//         days.push({ month: monthNumber, date: current.getDate(), weekday });
//         current.setDate(current.getDate() + 1);
//     }
//     return days;
// };

// const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
//     console.log('======', row, column, rowIndex, columnIndex)
//     return { backgroundColor: 'yellow' };
// }
</script>

<template>
    <div ref="rootRef" class="">
        <div class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-10 left-8">
                <ElButton type="success" @click="onAddOvertime">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">新增加班</div>
                </ElButton>
            </div>
            <div class="text-center mb-5 font-bold pt-5" style="color:#333">{{ searchCondition.年份 + '年' +
                searchCondition.月份}}的加班记录
            </div>
            <div class="flex flex-col justify-center items-center">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">年份</div>
                    <el-date-picker @change="getOvertimeList" @calendar-change="getOvertimeList" :clearable="false"
                        v-model="searchCondition.年份" type="year" placeholder="Pick a year" format="YYYY"
                        value-format="YYYY" />
                </div>

                <el-radio-group @change="(value) => { getOvertimeList() }" v-model="searchCondition.月份">
                    <el-radio-button v-for="month in 12" :key="month" :label="`${String(month).padStart(2, '0')}月`"
                        :value="month" />
                </el-radio-group>
            </div>

            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pb-5 mb-1 pl-5 bg-light-200">
                <div  class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">状态</div>
                    <el-select size="small"  class="searchItem" v-model="searchCondition.确认情况" placeholder="" >
                    <el-option v-for="item in ['未确认','已确认']" :key="item" :label="item" :value="item" />
                    </el-select>
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">员工姓名</div>
                    <el-input size="small" v-model="searchCondition.overtime_man_name" placeholder="" class="searchItem" />
                </div>


                <div class="flex justify-end items-center mr-6">
                    <ElButton class="ml-4" color="#00BA80" type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="ml-1">查询</div>
                    </ElButton>
                    <ElButton class="ml-4" type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="ml-1">清除</div>
                    </ElButton>
                </div>
            </div>

            <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="overtimeData"
                style="width: 100%;color: #666666;" border stripe>
                <el-table-column align="center" show-overflow-tooltip fixed prop="overtime_man_name" :label="'姓名'"
                    :min-width="120" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="type" :label="'类型'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="start_time" :label="'开始时间'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="end_time" :label="'结束时间'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="overtime_length" :label="'时长'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="create_man_name" :label="'操作员工'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="result" :label="'处理类型'"  />
                <el-table-column align="center" show-overflow-tooltip fixed prop="fsm_cur_state" :label="'确认情况'"  />

                <!-- 动态生成日期和星期几的表头列 -->



        <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <ElButton v-if="currentRoute.name == 'OvertimeManageCheck'" type="primary" size="small" @click="handleOper('check', scope.row)">审核</ElButton>
            <el-dropdown v-if="currentRoute.name != 'OvertimeManageCheck'" trigger="click" placement="bottom">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <div class="flex flex-wrap w-[200px]">
                  <el-dropdown-item @click="handleOper('edit', scope.row)">修改</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('del', scope.row)">删除</el-dropdown-item>
                </div>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
            </el-table>
            <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />

        </div>
    </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
