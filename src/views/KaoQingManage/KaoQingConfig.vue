<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElCheckbox, ElDialog, ElDropdown, ElDropdownItem, ElDropdownMenu, ElButton, ElTable, ElTableColumn, ElTag, ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getDepartmentListApi, getUserListApi, updateUserApi } from '@/api/usermanage'
import { DialogUserEx } from '@/components/DialogUserEx'
import { delKaoqingConfigApi, getKaoqingConfigListApi, updateKaoqingConfigApi } from '@/api/kaoqing';
import { cloneDeep } from 'lodash-es';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//权限数据源
const configData = reactive([])


//新增方案
const onAddKaoQingConfig = () => {
  push({ path: '/kaoqingmanage/kaoqingconfigadd' })
}

//获取方案数据
const getKaoQingConfigList = async () => {
  const res = await getKaoqingConfigListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    configData.splice(0, configData.length, ...res.data)
  }

}

//编辑方案
const onEditKaoQingConfig = (row) => {
  //进入编辑页面
  push({
    path: '/kaoqingmanage/kaoqingconfigadd',
    query: {
      id: row.id
    }
  })
}


//删除方案
const onDelKaoQingConfig = async (row) => {
  //删除用户
  ElMessageBox.confirm('确实是否删除方案:' + row.name, t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning'
  }).then(async () => {
    const ret = await delKaoqingConfigApi({ "ids": [row.id] })
    if (ret) {
      //提示删除成功
      ElMessage.success(t('msg.delOK'))
      getKaoQingConfigList()
    }
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: t('msg.delChannel'),
    })
  })


}

onMounted(() => {
  getKaoQingConfigList()
  getDepartmentTreeInfo()
})


const lastKaoQingConfig = ref({})
//显示隐藏选窗口变量
const showAddUser = ref(false)
//显示选择员弹窗
const onAddUser = (row) => {
  showAddUser.value = true
  lastKaoQingConfig.value = row
  console.log('1111', depData)
  setUserCheckInfo(depData[0])
}

//部门树
const depData = reactive([])
//当前需要展开的部门树节点
const arrExpandRowKeys = reactive([])
//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {
    let modifyData = cloneDeep(ret.data.all_depts)

    //需要展开的列表先清空
    arrExpandRowKeys.splice(0, arrExpandRowKeys.length)

    for (let one of modifyData) {
      await modifyDepData(one)
      arrExpandRowKeys.push(one.guuid)
    }

    depData.splice(0, depData.length, ...modifyData);
    console.log(depData)
    //当前节点展开
    // if (currentRow.value) {
    //   arrExpandRowKeys.push(currentRow.value.guuid)
    // }
  }
}

//根据服务器数据构造界面数据
const modifyDepData = async (data) => {
  data.check = false
  data.indeterminate = false;
  if (data.hasOwnProperty('login_time')) {
    data.type = 'user';
    return;
  }
  data.type = 'dept';
  data.children = [...data.sub_dept, ...data.sub_user];
  delete data.sub_dept;
  delete data.sub_user;
  for (let item of data.children) {
    await modifyDepData(item);
  }
}

//设置表头class
const setHeadClassName = (row, rowindex) => {
  return "tableHeader"
}
//勾选发生变化
const onSelChange = (value, item) => {
  if (item.row.type === 'dept') //如果是部门节点则需要自动选中他下面所有人员
  {
    modifyCheckAuto(item.row, value)
  }

}
//批量勾选处理部门节点下所有用户
const modifyCheckAuto = (row, value) => {
  if (!value) {
    row.check = value
  }
  else {
    if (row.total_user_count > 0 || row.type == 'user') {
      row.check = value
    }
  }

  //遍历子节点children递归调用自己来修改value
  if (row.children) {
    row.children.forEach(item => {
      modifyCheckAuto(item, value)
    })
  }
}

const onSaveUsers = async () => {
  showAddUser.value = false
  lastKaoQingConfig.value.user_list.splice(0, lastKaoQingConfig.value.user_list.length)
  getUserCheckinfo(depData[0])
  console.log(lastKaoQingConfig.value)
  // lastKaoQingConfig
  const ret = await updateKaoqingConfigApi(lastKaoQingConfig.value)
  if(ret) {
    ElMessage.success('操作成功')
    getKaoQingConfigList()
  }
}

//遍历depData树设置user_list中存在的id的check为true,depData层级不固定， 需要递归
const setUserCheckInfo = (row) => {

  if (row.type == 'user') {
    //判断是否存在于user_list中
    // if (lastKaoQingConfig.value.user_list.includes(row.id)) {
    //   row.check = true
    // }
    for (let item of lastKaoQingConfig.value.user_list) {
      if (item.id == row.id) {
        row.check = true
        break
      }
    }
  }

  //遍历子节点children递归调用自己来修改value
  if (row.children) {
    row.children.forEach(item => {
      setUserCheckInfo(item)
    })
    //如果自己是dept节点，判断下面是否有选中的user节点，如果有则自己也要选中
    let checkCount = 0
    for (let item of row.children) {
      if (item.check) {
        checkCount++
      }
    }
    if (checkCount > 0) {
      row.check = true
    }
  }
}

const getUserCheckinfo = (row) => {
  //递归拿到row下面所有类型是user并且check为true的id
  if (row.type == 'user') {
    if (row.check) {
      lastKaoQingConfig.value.user_list.push({
        id: row.id,
        name: row.resident_name
      })
    }
  }

  //遍历子节点children递归调用自己来修改value
  if (row.children) {
    row.children.forEach(item => {
      getUserCheckinfo(item)
    })
  }
}

</script>

<template>
  <ContentWrap title="考勤方案配置">
    <div class="mb-2">
      <ElButton type="success" @click="onAddKaoQingConfig">
        <Icon icon="fluent-mdl2:people-add" />
        <div class="pl-2">新增</div>
      </ElButton>
    </div>
    <el-table ref="tableRef" :data="configData" style="width: 100%;min-height: 60vh;" row-key="id" border stripe
      header-cell-class-name="tableHeader">
      <el-table-column label="编号" prop="id" width="100px" />
      <el-table-column align="center" width="150px" prop="name" label="方案名称" />
      <el-table-column align="center" min-width="10%" prop="users_name" label="对应员工">
        <template #default="scope">
          <el-tag class="mr-1 mb-2" effect="dark" color="#409EFF" v-for="item in scope.row.user_list" :key="item">{{
            item.name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" width="100px" prop="name" label="操作">
        <template #default="scope">
          <el-dropdown trigger="click" placement="bottom">
            <span class="el-dropdown-link">
              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="onAddUser(scope.row)">添加员工到方案</el-dropdown-item>
                <el-dropdown-item @click="onEditKaoQingConfig(scope.row)">编辑方案</el-dropdown-item>
                <el-dropdown-item @click="onDelKaoQingConfig(scope.row)">删除方案</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>



    <el-dialog title="选择员工" v-model="showAddUser" width="800" align-center destroy-on-close>
      <div style="height: 800px; overflow: auto;">
        <el-table ref="tableRef" :data="depData" style="width: 100%; margin-bottom: 20px" row-key="guuid" border stripe
          :expand-row-keys="arrExpandRowKeys" highlight-current-row :header-cell-class-name="setHeadClassName">
          <el-table-column label="部门/员工" min-width="60%">
            <template #default="scope">
              <el-checkbox :indeterminate="scope.row.indeterminate" v-model="scope.row.check" size="small" class="!mr-2"
                @change="onSelChange($event, scope)" />
              <Icon :icon="scope.row.type == 'dept' ? 'octicon:organization-16' : 'teenyicons:user-outline'"
                :class="scope.row.name ? 'iconDept' : 'iconUser'" />
              <span style="margin-left: 10px">{{ scope.row.name ? (scope.row.name +
                " (" + scope.row.total_user_count + "人)") : "(" + scope.row.username + ") " + scope.row.resident_name
                }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" min-width="10%" prop="roles_name" label="角色" />
          <el-table-column align="center" min-width="10%" prop="emp_status" label="在职状态" />
        </el-table>

      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddUser = false">取消</el-button>
          <el-button type="danger" @click="onSaveUsers">确定</el-button>
        </div>
      </template>
    </el-dialog>


  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
  background-color: #73b0e8 !important;
  color: #fff;
  // font-weight: 400;
}
</style>
