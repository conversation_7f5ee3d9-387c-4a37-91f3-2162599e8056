<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElSwitch,ElTimeSelect,ElCard,ElCheckboxGroup,ElTable,ElTableColumn,ElPopconfirm,ElForm,ElFormItem,ElDescriptions, ElDescriptionsItem, ElInput, ElCheckbox,ElButton,ElMessage, FormInstance, FormRules, ElInputNumber } from 'element-plus';
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { onMounted } from 'vue';
import { addRoleApi,getRoleListApi,updateRoleApi } from '@/api/usermanage'
import { nextTick } from 'process';
import {getAbilityTabMap} from '@/router'
import {checkFormRule} from '@/api/tool'
import { addKaoqingConfigApi, getKaoqingConfigInfoApi, updateKaoqingConfigApi } from '@/api/kaoqing';

const { push,currentRoute,back } = useRouter()
const { t } = useI18n()
const title = ref('')

//界面数据配置
const uiData = reactive([])
//数据源
const configData = reactive({
    name:'',
    user_list:[],
    rule_list:[],
    sort_num:''
})

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }] ,   
})

//保存
const onSave = async ()=>{
    console.log(configData)

    //校验每条时间段打卡范围是否为空
    for (let i = 0; i < configData.rule_list.length; i++) {
        const rule = configData.rule_list[i];
        if (!rule.上班时间 || !rule.下班时间 || !rule.打卡范围开始 || !rule.打卡范围结束) {
            ElMessage.warning('请填写时间段的打卡范围');
            return;
        }
    }
    for (let i = 0; i < configData.rule_list.length; i++) {
        const rule = configData.rule_list[i];
        if (rule.打卡误差 == undefined || rule.打卡误差 == null) {
            ElMessage.warning('请填写打卡误差');
            return;
        }
    }

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }
    //jiaoyan

    //新增
    if (currentRoute.value.query.id == undefined)
    {
        const ret = await addKaoqingConfigApi(configData)
        if(ret)
        {
            ElMessage.success('新增成功')
            back()
        }
    }
    else //修改角色
    {
        const ret = await updateKaoqingConfigApi(configData)
        if(ret)
        {
            ElMessage.success('修改成功')
            back()
        }
    }
}



onMounted(async()=>{
    if (currentRoute.value.query.id == undefined) {
        title.value = '新增考勤方案'
    }
    else
    {
        title.value = '修改考勤方案'
        let ret = await getKaoqingConfigInfoApi({
            id:currentRoute.value.query.id as number,
        })
        if(ret)
        {
            Object.assign(configData,ret.data)
        }
    }
})

//新增时间规则
const onAddTime = () => {
    configData.rule_list.push({
        上班时间:'',
        下班时间: '',
        打卡范围开始: '',
        打卡范围结束: '',
        必须签到: true,
        必须签退: true,
        加班: false,
        打卡误差:'15'
    })
}
//删除时间规则
const onDelRule = (index) => {
    configData.rule_list.splice(index, 1)
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="back()">
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;"  @click="onSave">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="configData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="1" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="方案名称"
                    class="flex">
                    <el-form-item prop="name" >
                            <el-input  v-model="configData.name" :disabled="configData.id!=undefined" />
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>

        <el-card class="w-[100%] mt-5">
            <template #header>
            <div class="flex">
                <div class="mr w-[130px]">工作时间段:</div>
                <ElButton type="primary" @click="onAddTime" >新增</ElButton>
            </div>
            </template>
            <el-table header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="configData.rule_list" style="width: 100%" border stripe>
                <el-table-column align="center"  :label="t('process.opt')" width="60" >
                    <template #default="scope">
                        <div  type="primary">
                            <el-popconfirm v-if="currentRoute.query.type != 'info'" title="是否确认删除?" @confirm="onDelRule(scope.$index)">
                                <template #reference>
                                    <Icon  icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                                </template>
                            </el-popconfirm>                    
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" >
                    <template #default="scope">
                        {{ scope.$index+1 }}
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip  prop="id" label="时间段名称" >
                    <template #default="scope">
                        <ElInput v-model="scope.row.时间段名称" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="上班时间" min-width="180px">
                    <template #default="scope">
                        <el-time-select
                            v-model="scope.row.上班时间"
                            start="00:00"
                            step="00:05"
                            end="23:59"
                            placeholder="上班"
                        />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="下班时间" min-width="180px">
                    <template #default="scope">
                        <el-time-select
                            v-model="scope.row.下班时间"
                            start="00:00"
                            step="00:05"
                            end="23:59"
                            placeholder="下班"
                        />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="打卡时间范围" min-width="280px">
                    <template #default="scope">
                        <div>
                            <el-time-select
                            v-model="scope.row.打卡范围开始"
                            style="width: 120px"
                            start="00:00"
                            step="00:05"
                            end="23:59"
                            placeholder="开始"
                        />
                        <span class="ml-2 mr-2">到</span>
                        <el-time-select
                            v-model="scope.row.打卡范围结束"
                            style="width: 120px"
                            start="00:00"
                            step="00:05"
                            end="23:59"
                            placeholder="结束"
                        />
                        </div>
 
                    </template>
                </el-table-column>
                <el-table-column align="center"   prop="id" label="允许误差(分钟)" >
                    <template #default="scope">
                        <el-input-number :controls="false" :min="0" v-model="scope.row.打卡误差"  class="!w-[100%]"/>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="必须签到" width="100">
                    <template #default="scope">
                        <el-switch v-model="scope.row.必须签到" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="必须签退" width="100">
                    <template #default="scope">
                        <el-switch v-model="scope.row.必须签退" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="加班" width="100">
                    <template #default="scope">
                        <el-switch v-model="scope.row.加班" />
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip   label="生效时间" width="370">
                    <template #default="scope">
                        <el-checkbox-group class="w-[100%]" :disabled="currentRoute.query.type == 'info'" v-model="scope.row.生效时间">
                            <div class="flex flex-col justify-center">
                                <!-- 第一行 -->
                                <div class="w-1/2">
                                    <el-checkbox label="星期一" />
                                    <el-checkbox label="星期二" />
                                    <el-checkbox label="星期三" />
                                    <el-checkbox label="星期四" />
                                </div>
                                <!-- 第二行 -->
                                <div class="w-1/2">                                    
                                    <el-checkbox label="星期五" />
                                    <el-checkbox label="星期六" />
                                    <el-checkbox label="星期日" />
                                </div>
                            </div>
                        </el-checkbox-group>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>


    <div class="mb-20"></div>

    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 160px !important;
    white-space: nowrap;
}

:deep(.conentStyle11) {
    width: 85% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>