<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElTimeSelect, ElTag, ElDialog, ElForm, ElFormItem, ElTooltip, ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, ElNotification } from 'element-plus';
import { reactive, ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, downloadFile, getTodayDate } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delOvertimeApi, getOvertimeListApi } from '@/api/usermanage';
import { addKaoqingDeviceApi, addKaoqingWaterApi, delKaoqingDeviceApi, delKaoqingWaterApi, delUserFaceInfoApi, delUserFingerInfoApi, exportKaoqingDayListApi, getKaoqingDayListApi, getKaoqingDeviceListApi, getKaoqingUserListApi, getKaoqingWaterListApi, updateKaoqingDeviceApi, updateKaoqingDeviceUserApi, updateKaoqingWaterApi } from '@/api/kaoqing';
import { DialogUser } from '@/components/DialogUser'

import { VxeTable, VxeColumn } from 'vxe-table';
import 'vxe-table/lib/style.css';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    员工姓名: '',
    部门: '',
    日期: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})


//开始查询
const onSearch = () => {
    getKaoQingDayList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
    searchCondition.日期 = getTodayDate()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getKaoQingDayList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getKaoQingDayList(val)
}
//创建
const onAddWater = () => {
    showEdit.value = true
    selItem.reset()
}

//处理表格对象操作
const handleOper = async (type, row) => {
    if (type === '修改') {
        showEdit.value = true
        Object.assign(selItem, row)
    }

}

onMounted(async () => {
    searchCondition.日期 = getTodayDate()
    await getKaoQingDayList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize)
})

const showEdit = ref(false)
const selItemdef = {
    员工编号: '',
    员工姓名: '',
    打卡日期: '',
    打卡时间: '',
    序号: '',
    index: 0,
    修改备注: '',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})


const waterData = reactive([])
const getKaoQingDayList = async (page = 1) => {
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)

    const ret = await getKaoqingDayListApi(tmp)
    if (ret) {
        waterData.splice(0, waterData.length, ...ret.data);
        totleCount.value = ret.count

        const $table = tableRef.value
        if ($table) {
            $table.loadData(waterData).then(() => {
                console.log('完成')
            })
        }

    }
}

//显示隐藏选择销售员窗口变量
const showSelUserDlg = ref(false)
//显示选择销售员弹窗
const onSelUser = () => {
    showSelUserDlg.value = true
}
//选择销售员回调
const onSelCallback = (id, name) => {
    console.log(id, name)
    selItem.员工编号 = id
    selItem.员工姓名 = name
}

const onEditWater = (index, row) => {
    console.log(index, row)
    selItem.reset()
    selItem.员工编号 = row.员工编号
    selItem.员工姓名 = row.员工姓名
    selItem.打卡日期 = row.打卡日期
    selItem.打卡时间 = row.打卡时间[index]
    selItem.index = index
    showEdit.value = true
    selItem.序号 = row.序号[index]
    console.log('----', selItem)

}

const getColor = (type) => {
    if (type == '打卡') {
        return '#409EFF'
    }
    else if (type == '补卡') {
        return '#e1a04d'
    }
    else if (type == '修改') {
        return '#5fe66f'
    }
}


const getTagProps = (status) => {
    let color;
    switch (status) {
        case '正常':
            color = '#409EFF';
            break;
        case '正常(补录)':
            color = '#FF7F00';
            break;
        case '迟到':
            color = '#FF4949';
            break;
        case '早退':
            color = '#FF4949';
            break;
        case '未打卡':
            color = '#ccc';
            break;
        // case '正标迟/早':
        //     color = '#F5A623';
        //     break;
        // case '迟/早标正':
        //     color = '#F5A623';
        //     break;
        // case '未标记迟/早':
        //     color = '#F5A623';
        //     break;
        case '旷工':
            color = '#FF4949';
            break;
        default:
            color = '#409EFF';
    }
    return {
        class: "mr-1 mb-2 w-[70px]",
        effect: 'dark',
        color: color,
        style: {
            color: '#fff'
        }
    };
};

const onShowDetail = (row) => {
    push({
        path: '/kaoqingmanage/kaoqingwaterlist',
        query: {
            员工姓名: row.员工姓名,
            开始时间: searchCondition.日期,
            结束时间: searchCondition.日期
        }
    })
}

const tableHeight = ref(600)

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleWindowResize)
})
//更新表高度
const updateTableHeight = () => {
    if (rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 230
        console.log(tableHeight.value)
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}

const convertSeconds = (seconds)=> {
    let hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  let minutes = Math.floor(seconds / 60);
  if (hours === 0 && minutes === 0) {
    return '--';
  } else if (hours!== 0 && minutes === 0) {
    return hours + '小时';
  } else if (hours === 0) {
    return `${String(minutes).padStart(2, '0')}分钟`;
  } else {
    return `${hours}小时${String(minutes).padStart(2, '0')}分钟`;
  }
}
const tableRef = ref()



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
      loadingExport.value = true
      searchCondition.count = 10000
    let tmp = cloneDeep(searchCondition)
    
    const ret = await exportKaoqingDayListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

</script>

<template>
    <div class="flex absolute top-5 right-5 left-5 bottom-0">

        <div ref="rootRef" class="relative !bg-white flex-grow overflow-y-auto">
            <div class="w-[100%] !bg-white flex-grow " style="color:#666666">
                <div class="absolute top-10 left-8 w-[90%]">
                    <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                        <Icon icon="carbon:export" />
                        <div class="pl-2">{{ t('button.export') }}</div>
                    </ElButton>
                </div>
                <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                    考勤日报
                </div>
                <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-2 pl-5 pr-5 pb-1 mb-2 bg-light-200">
                    <div class="inline-flex items-center  mr-5">
                        <div class="searchTitle">日报</div>
                        <el-date-picker v-model="searchCondition.日期" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </div>
                    <div class="inline-flex items-center mr-5">
                        <div class="searchTitle">用户姓名</div>
                        <el-input size="small" class="searchItem" v-model="searchCondition.员工姓名" placeholder="" />
                    </div>
                    <div class="inline-flex items-center  mr-5">
                        <div class="searchTitle">部门</div>
                        <el-input size="small" class="searchItem" v-model="searchCondition.部门" placeholder="" />
                    </div>

                    <ElButton type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>

                </div>
                <!-- 图例 -->
                <div class="flex mb-1">
                    <div class="flex text-sm items-center mr-3">
                        <div class="bg-[#409EFF] rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                        正常
                    </div>
                    <div class="flex text-sm items-center mr-3">
                        <div class="bg-[#FF7F00]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                        正常(补录)
                    </div>
                    <div class="flex text-sm items-center mr-3">
                        <div class="bg-[#FF4949]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                        迟到
                    </div>
                    <div class="flex text-sm items-center mr-3">
                        <div class="bg-[#FF4949]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                        早退
                    </div>
                    <div class="flex text-sm items-center mr-3">
                        <div class="bg-[#ccc]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                        未打卡
                    </div>
                </div>
                <vxe-table ref="tableRef" :height="tableHeight" id="myTable" size="mini" :data="waterData" :border="true" stripe
                    :column-config="{ resizable: true, useKey: true }">
                    <vxe-column align="center" type="seq" width="70"></vxe-column>
                    <vxe-column align="center" field="员工编号" title="员工编号"></vxe-column>
                    <vxe-column align="center" field="员工姓名" title="员工姓名"></vxe-column>
                    <vxe-column align="center" field="班次" title="班次" :min-width="250"></vxe-column>
                    <vxe-column align="center" field="上班1/下班1" title="上班1/下班1" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班1下班1 != undefined">
                                <el-tag v-bind="getTagProps(row.上班1下班1[0])">{{ row.上班1下班1[0] === '未打卡' ? '--'
                                    : row.上班1下班1[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班1下班1[2])">{{ row.上班1下班1[2] === '未打卡' ? '--'
                                    : row.上班1下班1[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="上班2/下班2" title="上班2/下班2" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班2下班2 != undefined">
                                <el-tag v-bind="getTagProps(row.上班2下班2[0])">{{ row.上班2下班2[0] === '未打卡' ? '--'
                                    : row.上班2下班2[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班2下班2[2])">{{ row.上班2下班2[2] === '未打卡' ? '--'
                                    : row.上班2下班2[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="上班3/下班3" title="上班3/下班3" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班3下班3 != undefined">
                                <el-tag v-bind="getTagProps(row.上班3下班3[0])">{{ row.上班3下班3[0] === '未打卡' ? '--'
                                    : row.上班3下班3[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班3下班3[2])">{{ row.上班3下班3[2] === '未打卡' ? '--'
                                    : row.上班3下班3[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="上班4/下班4" title="上班4/下班4" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班4下班4 != undefined">
                                <el-tag v-bind="getTagProps(row.上班4下班4[0])">{{ row.上班4下班4[0] === '未打卡' ? '--'
                                    : row.上班4下班4[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班4下班4[2])">{{ row.上班4下班4[2] === '未打卡' ? '--'
                                    : row.上班4下班4[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="上班5/下班5" title="上班5/下班5" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班5下班5 != undefined">
                                <el-tag v-bind="getTagProps(row.上班5下班5[0])">{{ row.上班5下班5[0] === '未打卡' ? '--'
                                    : row.上班5下班5[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班5下班5[2])">{{ row.上班5下班5[2] === '未打卡' ? '--'
                                    : row.上班5下班5[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="上班6/下班6" title="上班6/下班6" width="140">
                        <template #default="{ row }">
                            <div class="flex items-center justify-center" v-if="row.上班6下班6 != undefined">
                                <el-tag v-bind="getTagProps(row.上班6下班6[0])">{{ row.上班6下班6[0] === '未打卡' ? '--'
                                    : row.上班6下班6[1] }}</el-tag>
                                <el-tag v-bind="getTagProps(row.上班6下班6[2])">{{ row.上班6下班6[2] === '未打卡' ? '--'
                                    : row.上班6下班6[3] }}</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="工作时长" title="工作时长">
                        <template #default="{ row }">
                            {{ convertSeconds(row.工作时长) }}
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="加班时长" title="加班时长">
                        <template #default="{ row }">
                            {{ convertSeconds(row.加班时长) }}
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="总时长" title="总时长">
                        <template #default="{ row }">
                            {{ convertSeconds(row.总时长) }}
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="打卡记录" title="打卡记录">
                        <template #default="{ row }">
                            <ElButton size="small" type="primary" @click="onShowDetail(row)">查看</ElButton>
                        </template>
                    </vxe-column>
                </vxe-table>

                <el-pagination class="justify-end mt-8 " v-model:current-page="searchCondition.page"
                    v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                    layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>

            <el-dialog :title="selItem.序号 == '' ? '手动补卡' : '修改打卡'" v-model="showEdit" width="700" align-center
                destroy-on-close>
                <el-form>
                    <el-form-item v-if="selItem.序号 != ''" class="titleShow" label="打卡序号:">
                        {{ selItem.序号 }}
                    </el-form-item>
                    <el-form-item class="titleShow" label="员工编号:">
                        {{ selItem.员工编号 }}
                    </el-form-item>
                    <el-form-item class="titleShow" label="员工姓名:">
                        {{ selItem.员工姓名 }}
                        <ElButton v-if="selItem.序号 == ''" size="small" @click="onSelUser" class="ml-10 w-[50px]">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                    <el-form-item class="titleShow" label="打卡日期:">
                        <el-date-picker v-model="selItem.打卡日期" type="date" placeholder="选择日期"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                    <el-form-item class="titleShow" label="打卡时间:">
                        <el-time-select v-model="selItem.打卡时间" start="00:00" step="00:05" end="23:59"
                            placeholder="打卡时间" />
                    </el-form-item>
                    <el-form-item class="titleShow" label="修改备注:">
                        <el-input v-model="selItem.修改备注" placeholder="" />
                    </el-form-item>

                </el-form>
                <template #footer>
                    <div class="dialog-footer1">
                        <el-button type="warning" @click="showEdit = false">取消</el-button>
                        <el-button type="primary" @click="handleOper('确定修改', selItem)">{{ selItem.序号 == '' ? '手动补卡' :
                            '修改打卡'
                            }}</el-button>

                    </div>
                </template>
                <template #header>
                    <div class="dialog-header flex justify-start items-center">
                        <div class="dialog-title mr-10">
                            {{ selItem.序号 == '' ? '手动补卡' : '修改打卡' }}
                        </div>
                        <el-button v-if="selItem.序号 != ''" type="danger"
                            @click="handleOper('确定删除', selItem)">删除打卡</el-button>
                    </div>
                </template>
            </el-dialog>
            <DialogUser :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')"
                @on-submit="onSelCallback" />
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
