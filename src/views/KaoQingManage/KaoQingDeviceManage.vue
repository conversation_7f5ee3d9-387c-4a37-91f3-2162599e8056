<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDialog, ElForm, ElFormItem, ElTooltip, ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, ElNotification } from 'element-plus';
import { reactive, ref, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delOvertimeApi, getOvertimeListApi } from '@/api/usermanage';
import { addKaoqingDeviceApi, delKaoqingDeviceApi, delUserFaceInfoApi, delUserFingerInfoApi, getKaoqingDeviceListApi, getKaoqingUserListApi, updateKaoqingDeviceApi, updateKaoqingDeviceUserApi, updateKaoqingDeviceUserKaoqingApi } from '@/api/kaoqing';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/lib/style.css';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
const tableRef = ref()
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    员工姓名: '',
    部门:"",
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})


//考勤机数据源
const deviceData = reactive([])

//获取明细
const getKaoqingDeviceList = async (page = 1) => {
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)
    const ret = await getKaoqingDeviceListApi(tmp)
    if (ret) {
        deviceData.splice(0, deviceData.length, ...ret.data);
        arrayMulCondition.value.splice(0, arrayMulCondition.value.length);
        for(let one of ret.data) {
            arrayMulCondition.value.push(one.device_sn)
        }
    }
}


//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getKaoQingUserList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getKaoQingUserList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getKaoQingUserList(val)
}
//创建
const onAddDevice = () => {
    showEdit.value = true
    selItem.reset()
}

//处理表格对象操作
const handleOper = async (type, row) => {
    if (type === '修改') {
        showEdit.value = true
        Object.assign(selItem, row)
    }
    else if (type == '确定修改') {
        showEdit.value = false
        if (selItem.id == undefined) {
            const ret = await addKaoqingDeviceApi(row)
            if (ret) {
                getKaoqingDeviceList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }
        else {
            const ret = await updateKaoqingDeviceApi(row)
            if (ret) {
                getKaoqingDeviceList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }


    }
    else if (type === '删除') {

        ElMessageBox.confirm(
            '确定是否删除该设备？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {

                const ret = await delKaoqingDeviceApi({ "ids": [row.id] })
                if (ret) {
                    getKaoqingDeviceList()

                    ElMessage({
                        type: 'success',
                        message: t('msg.delOK'),
                    })
                }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}

//批量写入考勤机
const updateUserMul = () => {
    const $table = tableRef.value
    if ($table) {
        let tmp = []
        for (let one of $table.getCheckboxRecords(true)) {
            tmp.push(one.员工ID)
        }
        console.log(tmp)
        console.log(arrayMulCondition.value)
        if(arrayMulCondition.value.length<=0) {
            ElMessage({
                type:'warning',
                message: '请选择考勤机',
            });
            return
        }
        if(tmp.length<=0) {
            ElMessage({
                type:'warning',
                message: '请选择人员',
            });
            return
        }

        ElMessageBox.confirm(
            '确定是否同步所有用户到指定设备？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {

            for (let one of arrayMulCondition.value) {
                const ret = await updateKaoqingDeviceUserApi({
                    device_sn: one,
                    ids: [...tmp]
                })
                if (ret) {
                    ElNotification({
                        title: '成功',
                        message: one+'指令发送成功',
                        type: 'success',
                        position: 'top-right',
                        duration:2000,
                    })
                }

            }

        })
        .catch(() => {
        })

    }
}

const handleUser = async (type, row) => {
    if (type === '写入考勤机') {
        console.log(arrayMulCondition.value)
        if(arrayMulCondition.value.length<=0) {
            ElMessage({
                type:'warning',
                message: '请选择考勤机',
            });
            return
        }

        ElMessageBox.confirm(
            '确定是否同步该用户到指定设备？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {

            for (let one of arrayMulCondition.value) {
                const ret = await updateKaoqingDeviceUserApi({
                    device_sn: one,
                    ids: [row.员工ID]
                })
                if (ret) {
                    ElNotification({
                        title: '成功',
                        message: one+'指令发送成功',
                        type: 'success',
                        position: 'top-right',
                        duration:2000,
                    })
                }

            }

        })
        .catch(() => {
        })
    }
    else if (type === '删除指纹') {
        ElMessageBox.confirm(
            '确定是否删除该用户的指纹？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {

            const ret = await delUserFingerInfoApi({
            ids: [row.员工ID]
            })
            if (ret) {
                getKaoQingUserList()
                ElMessage({
                    type:'success',
                    message: '删除成功',
                });
            }
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
    }
    else if (type === '删除人脸') {
        ElMessageBox.confirm(
            '确定是否删除该用户的人脸？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
        .then(async () => {

            const ret = await delUserFaceInfoApi({
            ids: [row.员工ID]
            })
            if (ret) {
                getKaoQingUserList()
                ElMessage({
                    type:'success',
                    message: '删除成功',
                });
            }
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
    }
}

onMounted(async () => {

    await getKaoqingDeviceList()
    await getKaoQingUserList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

})

const showEdit = ref(false)
const selItemdef = {
    device_sn: '',
    device_name: '',
    device_type: '',
    cover_area: '',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const updateDeviceUser = async (row) => {
    ElMessageBox.confirm(
        '确定是否所有人员到该设备？',
        t('msg.warn'),
        {
            confirmButtonText: t('msg.ok'),
            cancelButtonText: t('msg.channel'),
            type: 'warning',
        }
    )
        .then(async () => {

            const ret = await updateKaoqingDeviceUserApi({
                device_sn: row.device_sn,
                ids: []
            })
            if (ret) {
                getKaoqingDeviceList()
                ElMessage({
                    type: 'success',
                    message: '指令发送成功',
                });
            }


        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: t('msg.delChannel'),
            })
        })
}
const checked = ref<any>([]);
const updateDeviceUserKaoqing = async (row) => {
    // 用于存储选择的日期范围，这里类型为数组，包含开始日期和结束日期两个元素（具体格式根据valueFormat等配置来定）
    ElMessageBox({
        title: '同步考勤时间范围',
        message: () =>
            h(ElDatePicker, {
                type: 'daterange',
                // 设置日期格式，这里以常见的 'yyyy-MM-dd' 为例，你可按需调整
                valueFormat: 'YYYY-MM-DD',
                // 绑定日期范围值，将选择的范围赋值给checked.value
                modelValue: checked.value,
                'onUpdate:modelValue': (val: [string, string] | null) => {
                    checked.value = val;
                },
                // 可以按需配置更多属性，比如限制可选日期范围等，以下是简单示例，设置最早可选日期为2024-01-01，最晚可选日期为2025-12-31
                pickerOptions: {
                    disabledDate: (time: Date) => {
                        const startDate = new Date('2024-01-01');
                        const endDate = new Date('2025-12-31');
                        return time.getTime() < startDate.getTime() || time.getTime() > endDate.getTime();
                    },
                },
            }),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton:true,
        type: 'success',
    }).then(async() => {
        // 用户点击了确定按钮，执行的操作
        console.log('用户选择的日期范围:', checked.value);
        // 在这里可以将选择的日期范围发送给后端进行处理
        if(checked.value.length==0){
            ElMessage({
                type:'warning',
                message: '请选择考勤时间范围',
            });
            return
        }
        const ret = await updateKaoqingDeviceUserKaoqingApi({
            device_sn: row.device_sn,
            start_date: checked.value[0],
            end_date: checked.value[1],
            rec_type : '考勤记录'
        })
        if(ret) {
            ElMessage({
                type:'success',
                message: '指令发送成功',
            });
        }
        // ...
    }).catch(() => {
        // 用户点击了取消按钮，执行的操作
    });
};

const onFresh = async () => {
    await getKaoqingDeviceList();
    await getKaoQingUserList();

    ElMessage({
        type: 'success',
        message: '刷新成功',
    });
}

const userData = reactive([])
const getKaoQingUserList = async (page = 1) => {
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)
    loading.value = true
    const ret = await getKaoqingUserListApi(tmp)
    if (ret) {
        userData.splice(0, userData.length, ...ret.data);
        totleCount.value = ret.count
    }
    const $table = tableRef.value
        if ($table) {
            $table.loadData(userData).then(() => {
                console.log('完成')
            })
        }

    loading.value = false
}

const columnDragConfig: VxeTablePropTypes.ColumnDragConfig = {
      visibleMethod ({ column }) {
        if (column.field === '员工ID') {
          return false
        }
        return true
      }
}

const arrayMulCondition = ref([])
const sortChangeEvent = ({ sortList }) => {
  console.info(sortList.map((item) => `${item.field},${item.order}`).join('; '))
}

const loading =ref(false)
</script>

<template>
    <div ref="rootRef" class="">
        <div v-loading="loading" class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-10 left-8 w-[90%]">
                <ElButton type="success" @click="onAddDevice">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">新增考勤设备</div>
                </ElButton>
                <ElButton type="warning" @click="onFresh">
                    <Icon icon="mynaui:refresh-solid" />
                    <div class="pl-2">刷新</div>
                </ElButton>
            </div>
            <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                考勤设备列表
            </div>

            <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="deviceData"
                style="width: 100%;color: #666666;" border stripe>
                <el-table-column align="center" show-overflow-tooltip fixed prop="device_sn" :label="'序列号'"
                    :min-width="120" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="device_name" :label="'设备名称'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="device_type" :label="'型号'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="cover_area" :label="'考勤区域'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="ip_address" :label="'IP地址'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="status" :label="'状态'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="last_time" :label="'最后链接时间'" />

                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="290">
                    <template #default="scope">
                        <ElButton type="warning" size="small" @click="updateDeviceUser(scope.row)">同步人员
                        </ElButton>
                        <ElButton type="success" size="small" class="mr-2" @click="updateDeviceUserKaoqing(scope.row)">同步考勤
                        </ElButton>
                        <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                            </span>
                            <template #dropdown>
                                <ElDropdownMenu>
                                    <el-dropdown-item @click="handleOper('修改', scope.row)">修改</el-dropdown-item>
                                    <el-dropdown-item @click="handleOper('删除', scope.row)">删除</el-dropdown-item>
                                </ElDropdownMenu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>


            <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                考勤设备人员信息
            </div>
            <div  style1="border: 1px solid rgb(143, 143, 143);" class="pt-2 pl-5 pr-5 pb-1 mb-2 bg-light-200">
                    <div class="inline-flex items-center mr-5">
                        <div class="searchTitle">用户姓名</div>
                        <el-input size="small" class="searchItem" v-model="searchCondition.员工姓名" placeholder="" />
                    </div>
                    <div class="inline-flex items-center  mr-5">
                        <div class="searchTitle">部门</div>
                        <el-input size="small" class="searchItem" v-model="searchCondition.部门" placeholder="" />
                    </div>

                    <ElButton type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>

                </div>
                <div class="w-[100%] flex justify-end mb-2">
                    <div class="flex justify-start items-center mr-2">
                        <div class="searchTitle">选择设备</div>
                        <el-select
                            v-model="arrayMulCondition"
                            multiple
                            placeholder="选择操作设备"
                            style="width: 240px"
                            >
                            <el-option
                                v-for="item in deviceData"
                                :key="item.device_sn"
                                :label="item.device_name"
                                :value="item.device_sn"
                            />
                        </el-select>
                    </div>
                    <ElButton class="mr-5" type="success" @click="updateUserMul">
                        <Icon icon="fluent-mdl2:people-add" />
                        <div class="pl-2">批量写入考勤机</div>
                    </ElButton>
                </div>
                                    <!-- :scroll-y="{enabled: true, gt: 0}"
                    :row-config="{height:38}" -->
                <vxe-table :max-height="1000"  ref="tableRef"  id="myTable" size="mini" :data="userData" :border="true" stripe
                    :column-config="{ resizable: true, useKey: true ,drag: true}"
                    :checkbox-config="{labelField: '员工ID', highlight: true}"
                    :column-drag-config="columnDragConfig"
                    :sort-config="{ multiple: true}"
                    @sort-change="sortChangeEvent"

                    >
                    <vxe-column type="checkbox" align="center" field="员工ID" title="员工ID" :width="120"></vxe-column>
                    <vxe-column align="center" field="员工编号" title="员工编号" sortable></vxe-column>
                    <vxe-column align="center" field="员工姓名" title="员工姓名" sortable></vxe-column>
                    <vxe-column align="center" field="公司部门" title="公司部门" sortable></vxe-column>
                    <vxe-column align="center" field="入职时间" title="入职时间" sortable></vxe-column>
                    <vxe-column align="center" field="考勤编号" title="考勤编号" sortable></vxe-column>
                    <vxe-column align="center" field="考勤区域" title="考勤区域" sortable></vxe-column>
                    <vxe-column align="center" field="管理员标识" title="管理员标识" sortable></vxe-column>
                    <vxe-column align="center" field="指纹数据" title="指纹数据" sortable>
                        <template #default="scope">
                            <div class="flex justify-center items-center">
                                <template v-for="index in scope.row.指纹数据" :key="index">
                                    <Icon icon="fluent:fingerprint-20-filled" />
                                </template>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="人脸数据" title="人脸数据" sortable>
                        <template #default="scope">
                            <div>
                                <template v-for="index in scope.row.人脸数据" :key="index">
                                    <Icon icon="fa6-solid:face-grin" />
                                </template>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column align="center" field="操作" title="操作">
                        <template #default="scope">
                            <el-dropdown trigger="click" placement="bottom">
                                <span class="el-dropdown-link">
                                    <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                                </span>
                                <template #dropdown>
                                    <ElDropdownMenu>
                                        <el-dropdown-item @click="handleUser('写入考勤机', scope.row)">写入考勤机</el-dropdown-item>
                                        <el-dropdown-item @click="handleUser('删除指纹', scope.row)">删除指纹</el-dropdown-item>
                                        <el-dropdown-item @click="handleUser('删除人脸', scope.row)">删除人脸</el-dropdown-item>
                                    </ElDropdownMenu>
                                </template>
                            </el-dropdown>
                        </template>
                    </vxe-column>
                </vxe-table>

            <!-- <el-table ref="userTableRef22" header-cell-class-name="tableHeader" :data="userData"
                style="width: 100%;color: #666666;" border stripe>
                <el-table-column align="center" show-overflow-tooltip fixed prop="序号" :label="'序号'" :min-width="120" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="员工编号" :label="'员工编号'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="员工姓名" :label="'员工姓名'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="公司部门" :label="'公司部门'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="考勤编号" :label="'考勤编号'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="考勤区域" :label="'考勤区域'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="管理员标识" :label="'管理员标识'" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="指纹数据" :label="'指纹数据'">
                    <template #default="scope">
                        <div class="flex justify-center items-center">
                            <template v-for="index in scope.row.指纹数据" :key="index">
                                <Icon icon="fluent:fingerprint-20-filled" />
                            </template>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip fixed prop="人脸数据" :label="'人脸数据'">
                    <template #default="scope">
                        <div>
                            <template v-for="index in scope.row.人脸数据" :key="index">
                                <Icon icon="fa6-solid:face-grin" />
                            </template>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column align="center" fixed="right" :label="t('userTable.operate')" width="190">
                    <template #default="scope">
                        <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                            </span>
                            <template #dropdown>
                                <ElDropdownMenu>
                                    <el-dropdown-item @click="handleUser('写入考勤机', scope.row)">写入考勤机</el-dropdown-item>
                                    <el-dropdown-item @click="handleUser('删除指纹', scope.row)">删除指纹</el-dropdown-item>
                                    <el-dropdown-item @click="handleUser('删除人脸', scope.row)">删除人脸</el-dropdown-item>
                                </ElDropdownMenu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table> -->
            <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

        <el-dialog title="新增/修改" v-model="showEdit" width="700" align-center destroy-on-close>
            <el-form>
                <el-form-item class="titleShow" label="序列号:">
                    <el-input v-model="selItem.device_sn" placeholder="请输入序列号"></el-input>
                </el-form-item>
                <el-form-item class="titleShow" label="名称:">
                    <el-input v-model="selItem.device_name" placeholder="请输入名称"></el-input>
                </el-form-item>
                <el-form-item class="titleShow" label="型号:">
                    <el-input v-model="selItem.device_type" placeholder="请输入型号"></el-input>
                </el-form-item>
                <el-form-item class="titleShow" label="考勤区域:">
                    <el-input v-model="selItem.cover_area" placeholder="请输入考勤区域"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="showEdit = false">取消</el-button>
                    <el-button type="danger" @click="handleOper('确定修改', selItem)">确定</el-button>
                </div>
            </template>
        </el-dialog>

    </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
