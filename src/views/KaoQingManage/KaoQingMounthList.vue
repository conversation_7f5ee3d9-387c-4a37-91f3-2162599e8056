<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElTimeSelect, ElTag, ElDialog, ElForm, ElFormItem, ElTooltip, ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, ElNotification } from 'element-plus';
import { reactive, ref, onMounted, computed, watch, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi, downloadFile, getTodayDate, getTodayMounth } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delOvertimeApi, getOvertimeListApi } from '@/api/usermanage';
import { addKaoqingDeviceApi, addKaoqingWaterApi, delKaoqingDeviceApi, delKaoqingWaterApi, delUserFaceInfoApi, delUserFingerInfoApi, exportKaoqingMounthListApi, getKaoqingDayListApi, getKaoqingDeviceListApi, getKaoqingMounthListApi, getKaoqingUserListApi, getKaoqingWaterListApi, updateKaoqingDeviceApi, updateKaoqingDeviceUserApi, updateKaoqingWaterApi } from '@/api/kaoqing';
import { DialogUser } from '@/components/DialogUser'

import { VxeTable, VxeColumn, VxeColgroup,VxeTooltip } from 'vxe-table';
import 'vxe-table/lib/style.css';

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref()

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    员工姓名: '',
    部门: '',
    月份: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})


//开始查询
const onSearch = () => {
    getKaoQingMounthList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
    searchCondition.月份 = getTodayMounth()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getKaoQingMounthList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getKaoQingMounthList(val)
}
//创建
const onAddWater = () => {
    showEdit.value = true
    selItem.reset()
}

//处理表格对象操作
const handleOper = async (type, row) => {
    if (type === '修改') {
        showEdit.value = true
        Object.assign(selItem, row)
    }

}

onMounted(async () => {
    //拿到当前年月格式 YYYY-MM
    searchCondition.月份 = getTodayMounth()

    // 初始化时生成当月数据
    generateMonthDays(`${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}`);

    await getKaoQingMounthList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });

    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize)
})

const showEdit = ref(false)
const selItemdef = {
    员工编号: '',
    员工姓名: '',
    打卡日期: '',
    打卡时间: '',
    序号: '',
    index: 0,
    修改备注: '',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const waterData = reactive([])
const getKaoQingMounthList = async (page = 1) => {
    generateMonthDays(searchCondition.月份)

    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)

    const ret = await getKaoqingMounthListApi(tmp)
    if (ret) {
        waterData.splice(0, waterData.length, ...ret.data);
        totleCount.value = ret.count

        const $table = tableRef.value
        if ($table) {
            $table.loadData(waterData).then(() => {
                console.log('完成')
            })
        }
    }
}

//显示隐藏选择销售员窗口变量
const showSelUserDlg = ref(false)
//显示选择销售员弹窗
const onSelUser = () => {
    showSelUserDlg.value = true
}
//选择销售员回调
const onSelCallback = (id, name) => {
    console.log(id, name)
    selItem.员工编号 = id
    selItem.员工姓名 = name
}

const onEditWater = (index, row) => {
    console.log(index, row)
    selItem.reset()
    selItem.员工编号 = row.员工编号
    selItem.员工姓名 = row.员工姓名
    selItem.打卡日期 = row.打卡日期
    selItem.打卡时间 = row.打卡时间[index]
    selItem.index = index
    showEdit.value = true
    selItem.序号 = row.序号[index]
    console.log('----', selItem)

}

const getColor = (type) => {
    if (type == '打卡') {
        return '#409EFF'
    }
    else if (type == '补卡') {
        return '#e1a04d'
    }
    else if (type == '修改') {
        return '#5fe66f'
    }
}


const getTagProps = (status) => {
    let color;
    switch (status) {
        case '正常':
            color = '#409EFF';
            break;
        case '正常(补录)':
            color = '#FF7F00';
            break;
        case '迟到':
            color = '#FF4949';
            break;
        case '早退':
            color = '#FF4949';
            break;
        case '未打卡':
            color = '#ccc';
            break;
        // case '正标迟/早':
        //     color = '#F5A623';
        //     break;
        // case '迟/早标正':
        //     color = '#F5A623';
        //     break;
        // case '未标记迟/早':
        //     color = '#F5A623';
        //     break;
        case '旷工':
            color = '#FF4949';
            break;
        default:
            color = '#409EFF';
    }
    return "mr-2 pl-1 pr-1 bg-["+color+"]";
};

const onShowDetail = (row) => {
    push({
        path: '/kaoqingmanage/kaoqingwaterlist',
        query: {
            员工姓名: row.员工姓名,
            开始时间: searchCondition.月份+'-01',
            结束时间: getLastDayOfMonth(searchCondition.月份),
        }
    })
}
const getLastDayOfMonth = (dateStr)=> {
  // 将传入的字符串拆分成年和月
  let [year, month] = dateStr.split('-').map(Number);
  // 创建一个Date对象
  let date = new Date(year, month, 0);
  // 获取日期
  let day = date.getDate();
  // 返回格式化后的字符串
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
}

// 动态生成本月日期
const today = new Date();
const currentYear = today.getFullYear();
const currentMonth = today.getMonth() + 1;

// 存储生成的日期数据
const weeks = ref<any[]>([]);

// 监听月份变化
// watch(
//     () => searchCondition.月份,
//     (newMonth) => {
//         if (newMonth) {
//             generateMonthDays(newMonth);
//         }
//     }
// );

// 动态生成指定月份的日期
const generateMonthDays = (monthStr: string) => {
    // 从选择的字符串中解析年份和月份（格式：YYYY-MM）
    const [year, month] = monthStr.split("-").map(Number);

    // 获取该月份的第一天和最后一天
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0); // 当前月最后一天

    // 重置 weeks 数组
    weeks.value = [];

    let currentWeek: any[] = [];

    // 循环生成每天数据
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(year, month - 1, day);
        const weekday = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][date.getDay()];
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;

        currentWeek.push({
            date: day, // 日期号
            weekday, // 星期几
            isWeekend // 是否为周末
        });

        // 如果是周六或者是最后一天，结束当前周
        if (date.getDay() === 6 || day === lastDay.getDate()) {
            weeks.value.push(currentWeek);
            currentWeek = [];
        }
    }
};



// 设置单元格类名（数据行）
const cellClassName = (params: any) => {
    const field = params.column.field;
    if (field && field.startsWith("day")) {
        const dayNumber = parseInt(field.replace("day", ""), 10);
        const targetDay = weeks.value.flat().find((day) => day.date === dayNumber);
        if (targetDay?.isWeekend) {
            return "bg-gray-100"; // 周末单元格类名
        }
    }
    return "";
};

// 设置表头单元格类名
const headerCellClassName = (params: any) => {
    let title = params.column.title;
    if (title == undefined) {
        return ""
    }
    title = String(title)
    //title 如果是星期开头 或者title是数字
    if (title.startsWith('星期') || /^\d+$/.test(title)) {
        let targetDay = weeks.value.flat().find((day) => day.date == title);
        if (targetDay == undefined) {
            targetDay = weeks.value.flat().find((day) => day.weekday == title);
        }
        if (targetDay?.isWeekend) {
            return "bg-gray-100"; // 周末表头单元格类名
        }
    }

    if (title.indexOf('周') > 0) {
        let tmp = title.replace('周', '').replace('第', '')
        if (Number(tmp) % 2 == 0)
        {
            return "bg-blue-100"; // 周末表头单元格类名
        }
        else
            return "bg-blue-200"; // 周末表头单元格类名
    }

    return "";
};

const tableHeight = ref(600)

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize)
})
//更新表高度
const updateTableHeight = () => {
    if (rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight-230
        console.log(tableHeight.value)
    }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}

const convertSeconds = (seconds)=> {
    let hours = Math.floor(seconds / 3600);
  seconds %= 3600;
  let minutes = Math.floor(seconds / 60);
  if (hours === 0 && minutes === 0) {
    return '--';
  } else if (hours!== 0 && minutes === 0) {
    return hours + '小时';
  } else if (hours === 0) {
    return `${String(minutes).padStart(2, '0')}分钟`;
  } else {
    return `${hours}小时${String(minutes).padStart(2, '0')}分钟`;
  }
}
const checkKuangGong = (array) => {
    if (array == undefined || array.length == 0)
        return false
    let bKuang = true
    for (let i = 0; i < array.length; i++) {
        if (array[i][0] != '未打卡' || array[i][2]!= '未打卡') {
            bKuang = false
            return bKuang
        }
    }
    return bKuang
}


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
      loadingExport.value = true
      searchCondition.count = 10000
    let tmp = cloneDeep(searchCondition)
    
    const ret = await exportKaoqingMounthListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
    <div class="flex absolute top-5 right-5 left-5 bottom-0">    
    <div ref="rootRef" class="relative !bg-white flex-grow overflow-y-auto">
        <div class=" w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-10 left-8 w-[90%]">
                <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                        <Icon icon="carbon:export" />
                        <div class="pl-2">{{ t('button.export') }}</div>
                    </ElButton>
            </div>
            <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                考勤月报
            </div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-2 pl-5 pr-5 pb-1 mb-2 bg-light-200">
                <div class="inline-flex items-center  mr-5">
                    <div class="searchTitle ">月份</div>
                    <el-date-picker v-model="searchCondition.月份" type="month" placeholder="" format="YYYY/MM"
                        value-format="YYYY-MM" />
                </div>
                <div class="inline-flex items-center mr-5">
                    <div class="searchTitle">用户姓名</div>
                    <el-input size="small" class="searchItem" v-model="searchCondition.员工姓名" placeholder="" />
                </div>
                <div class="inline-flex items-center  mr-5">
                    <div class="searchTitle">部门</div>
                    <el-input size="small" class="searchItem" v-model="searchCondition.部门" placeholder="" />
                </div>

                <ElButton type="primary" @click="onSearch">
                    <Icon icon="tabler:search" />
                    <div class="pl-2">查询</div>
                </ElButton>
                <ElButton type="warning" @click="onClear">
                    <Icon icon="ant-design:clear-outlined" />
                    <div class="pl-2">清除</div>
                </ElButton>

            </div>
            <!-- 图例 -->
            <div class="flex mb-1">
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#409EFF] rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    正常
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#FF7F00]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    正常(补录)
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#FF4949]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    迟到
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#FF4949]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    早退
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#ccc]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    未打卡
                </div>
            </div>
            <vxe-table ref="tableRef" :height="tableHeight" id="myTable" size="mini" :data="waterData" :border="true" stripe 
                 :column-config="{ resizable: true }" :cell-class-name="cellClassName" 
                :scroll-x="{ enabled: true, gt: 0 }" :scroll-y="{enabled: true, gt: 0}" :header-cell-class-name="headerCellClassName">
                <vxe-column align="center" field="员工编号" title="员工编号" width="120" ></vxe-column>
                <vxe-column align="center" field="员工姓名" title="员工姓名" width="120" fixed="left"></vxe-column>
                <vxe-column align="center" field="员工部门" title="员工部门" width="120"></vxe-column>

                <template v-for="(week, weekIndex) in weeks" :key="weekIndex">
                    <vxe-colgroup :title="`第${weekIndex + 1}周`" align="center">
                        <template v-for="(day, dayIndex) in week" :key="dayIndex">
                            <vxe-colgroup :title="day.weekday" align="center">
                                <vxe-column width="150" :field="`day${day.date}`" :title="day.date" align="center">
                                    <template #default="{ row }">
                                        <div class="flex justify-center">
                                            <div v-if="!checkKuangGong(row.全月情况[day.date-1])">
                                                <div  v-for="item,index of row.全月情况[day.date-1]" :key="index" class="flex items-center justify-center text-light-200 mb-1">
                                                <div :class="getTagProps(item[0])">{{ item[1] }}</div>
                                                <div :class="getTagProps(item[2])">{{ item[3] }}</div>
                                            </div>
                                            </div>      
                                            <div v-else class="bg-red-500 text-white w-[80px]" >矿工</div>
                                        </div>
                    
                                    </template>
                                </vxe-column>
                            </vxe-colgroup>
                        </template>
                    </vxe-colgroup>
                </template>


                <vxe-column fixed="right" align="center" field="工作时长" title="工作时长" width="80">
                    <template #default="{ row }">
                        {{ convertSeconds(row.工作时长) }}
                    </template>
                </vxe-column>
                <vxe-column fixed="right" align="center" field="加班时长" title="加班时长" width="80">
                    <template #default="{ row }">
                        {{ convertSeconds(row.加班时长) }}
                    </template>
                </vxe-column>
                <vxe-column fixed="right" align="center" field="总时长" title="总时长" width="80">
                    <template #default="{ row }">
                        {{ convertSeconds(row.总时长) }}
                    </template>
                </vxe-column>
                <vxe-column fixed="right" align="center" field="出勤汇总" title="出勤汇总" width="80">
                    <template #default="{ row }">
                        <div>
                            <div>出勤:{{ row.出勤 }}天</div>
                            <div>请假:{{ row.请假 }}</div>
                            <div>旷工:{{ row.旷工 }}</div>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column fixed="right" align="center" field="打卡记录" title="打卡记录" width="80">
                    <template #default="{ row }">
                        <ElButton size="small" type="primary" @click="onShowDetail(row)">查看</ElButton>
                    </template>
                </vxe-column>
            </vxe-table>

            <el-pagination class="justify-end mt-8 " v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

        <el-dialog :title="selItem.序号 == '' ? '手动补卡' : '修改打卡'" v-model="showEdit" width="700" align-center
            destroy-on-close>
            <el-form>
                <el-form-item v-if="selItem.序号 != ''" class="titleShow" label="打卡序号:">
                    {{ selItem.序号 }}
                </el-form-item>
                <el-form-item class="titleShow" label="员工编号:">
                    {{ selItem.员工编号 }}
                </el-form-item>
                <el-form-item class="titleShow" label="员工姓名:">
                    {{ selItem.员工姓名 }}
                    <ElButton v-if="selItem.序号 == ''" size="small" @click="onSelUser" class="ml-10 w-[50px]">
                        <Icon icon="iconamoon:search-bold" />
                    </ElButton>
                </el-form-item>
                <el-form-item class="titleShow" label="打卡日期:">
                    <el-date-picker v-model="selItem.打卡日期" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item class="titleShow" label="打卡时间:">
                    <el-time-select v-model="selItem.打卡时间" start="00:00" step="00:05" end="23:59" placeholder="打卡时间" />
                </el-form-item>
                <el-form-item class="titleShow" label="修改备注:">
                    <el-input v-model="selItem.修改备注" placeholder="" />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer1">
                    <el-button type="warning" @click="showEdit = false">取消</el-button>
                    <el-button type="primary" @click="handleOper('确定修改', selItem)">{{ selItem.序号 == '' ? '手动补卡' : '修改打卡'
                        }}</el-button>

                </div>
            </template>
            <template #header>
                <div class="dialog-header flex justify-start items-center">
                    <div class="dialog-title mr-10">
                        {{ selItem.序号 == '' ? '手动补卡' : '修改打卡' }}
                    </div>
                    <el-button v-if="selItem.序号 != ''" type="danger"
                        @click="handleOper('确定删除', selItem)">删除打卡</el-button>
                </div>
            </template>
        </el-dialog>
        <DialogUser :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')" @on-submit="onSelCallback" />
    </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
