<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElPopconfirm, ElTimeSelect, ElTag, ElDialog, ElForm, ElFormItem, ElTooltip, ElRadioGroup, ElRadioButton, ElTabs, ElTabPane, ElMessageBox, ElMessage, ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination, ElNotification } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPayBillSellListApi, delBuyerApi, delPayBillSellApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { checkPermissionApi } from '@/api/tool';
import { cloneDeep } from 'lodash-es';
import { delFinanceWaterApi, getFinanceAccountListApi, getFinanceWaterListApi } from '@/api/finance';
import type { TabsInstance } from 'element-plus'
import { DialogMoneyDetail } from '@/components/DialogMoneyDetail'
import { delOvertimeApi, getOvertimeListApi } from '@/api/usermanage';
import { addKaoqingDeviceApi, addKaoqingWaterApi, delKaoqingDeviceApi, delKaoqingWaterApi, delUserFaceInfoApi, delUserFingerInfoApi, getKaoqingDeviceListApi, getKaoqingUserListApi, getKaoqingWaterListApi, updateKaoqingDeviceApi, updateKaoqingDeviceUserApi, updateKaoqingWaterApi } from '@/api/kaoqing';
import { DialogUser } from '@/components/DialogUser'

const { push, currentRoute } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    员工姓名: '',
    部门: '',
    时间: ['', ''],
    开始时间: '',
    结束时间: '',
    page: 1,
    count: 20
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})


//开始查询
const onSearch = () => {
    getKaoQingWaterList()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getKaoQingWaterList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getKaoQingWaterList(val)
}
//创建
const onAddWater = () => {
    showEdit.value = true
    selItem.reset()
}

//处理表格对象操作
const handleOper = async (type, row) => {
    if (type === '修改') {
        showEdit.value = true
        Object.assign(selItem, row)
    }
    else if (type == '确定修改') {


        if (row.员工编号 == '') {
            ElMessage({
                type: 'error',
                message: '请选择用户',
            });
            return;
        }
        if (row.打卡日期 == '') {
            ElMessage({
                type: 'error',
                message: '请选择打卡日期',
            });
            return;
        }
        if (row.打卡时间 == '') {
            ElMessage({
                type: 'error',
                message: '请选择打卡时间',
            });
            return;
        }
        showEdit.value = false

        if (selItem.序号 == '') {
            const ret = await addKaoqingWaterApi({
                user_id: row.员工编号,
                user_date: row.打卡日期,
                user_time: row.打卡时间 + ':00',
            })
            if (ret) {
                getKaoQingWaterList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }
        else {
            const ret = await updateKaoqingWaterApi({
                ids: [row.序号],
                user_date: row.打卡日期,
                user_time: row.打卡时间.replace(/^(\d{1,2}:\d{1,2})(?<!:)$/, "$1:00"),//row.打卡时间 + ':00',
                checkin_type: '修改',
                note: row.修改备注,
            })
            if (ret) {
                getKaoQingWaterList();

                ElMessage({
                    type: 'success',
                    message: '操作成功',
                });
            }
        }


    }
    else if (type === '确定删除') {

        ElMessageBox.confirm(
            '确定是否删除该打卡？',
            t('msg.warn'),
            {
                confirmButtonText: t('msg.ok'),
                cancelButtonText: t('msg.channel'),
                type: 'warning',
            }
        )
            .then(async () => {
                showEdit.value = false
                const ret = await delKaoqingWaterApi({ "ids": [row.序号] })
                if (ret) {
                    getKaoQingWaterList()

                    ElMessage({
                        type: 'success',
                        message: t('msg.delOK'),
                    })
                }


            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: t('msg.delChannel'),
                })
            })
    }
}

onMounted(async () => {
    console.log('----', currentRoute.value.query)
    if(currentRoute.value.query.开始时间 != '' && currentRoute.value.query.结束时间!= '') {
        searchCondition.时间.splice(0, 2, currentRoute.value.query.开始时间, currentRoute.value.query.结束时间)
    }
    if(currentRoute.value.query.员工姓名!= '') {
        searchCondition.员工姓名 = currentRoute.value.query.员工姓名 as string
    }

    await getKaoQingWaterList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });


})

const showEdit = ref(false)
const selItemdef = {
    员工编号: '',
    员工姓名: '',
    打卡日期: '',
    打卡时间: '',
    序号: '',
    index: 0,
    修改备注: '',
}
const selItem = reactive({
    ...selItemdef,
    reset() {
        for (let key in this) {
            if (this.hasOwnProperty(key) && !(key in selItemdef) && key != 'reset') {
                delete this[key];
            }
        }
        Object.assign(this, selItemdef)
    }
})

const onFresh = async () => {
    await getKaoQingWaterList();

    ElMessage({
        type: 'success',
        message: '刷新成功',
    });
}

const waterData = reactive([])
const getKaoQingWaterList = async (page = 1) => {
    searchCondition.page = page
    let tmp = cloneDeep(searchCondition)
    tmp.开始时间 = searchCondition.时间[0]
    tmp.结束时间 = searchCondition.时间[1]

    const ret = await getKaoqingWaterListApi(tmp)
    if (ret) {
        waterData.splice(0, waterData.length, ...ret.data);
        totleCount.value = ret.count
    }
}

//显示隐藏选择销售员窗口变量
const showSelUserDlg = ref(false)
//显示选择销售员弹窗
const onSelUser = () => {
    showSelUserDlg.value = true
}
//选择销售员回调
const onSelCallback = (id, name) => {
    console.log(id, name)
    selItem.员工编号 = id
    selItem.员工姓名 = name
}

const onEditWater = (index, row) => {
    console.log(index, row)
    selItem.reset()
    selItem.员工编号 = row.员工编号
    selItem.员工姓名 = row.员工姓名
    selItem.打卡日期 = row.打卡日期
    selItem.打卡时间 = row.打卡时间[index]
    selItem.index = index
    showEdit.value = true
    selItem.序号 = row.序号[index]
    console.log('----', selItem)

}

const getColor = (type) => {
    if (type == '打卡') {
        return '#409EFF'
    }
    else if (type == '补卡') {
        return '#e1a04d'
    }
    else if (type == '修改') {
        return '#5fe66f'
    }
}
</script>

<template>
    <div ref="rootRef" class="">
        <div class="pb-[100px] w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-10 left-8 w-[90%]">
                <ElButton type="success" @click="onAddWater">
                    <Icon icon="fluent-mdl2:people-add" />
                    <div class="pl-2">手动补卡</div>
                </ElButton>
                <ElButton type="warning" @click="onFresh">
                    <Icon icon="mynaui:refresh-solid" />
                    <div class="pl-2">刷新</div>
                </ElButton>
            </div>
            <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                考勤设备人员信息
            </div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-2 pl-5 pr-5 pb-1 mb-2 bg-light-200">

                <div class="inline-flex items-center mr-5">
                    <div class="searchTitle">用户姓名</div>
                    <el-input size="small" class="searchItem" v-model="searchCondition.员工姓名" placeholder="" />
                </div>
                <div class="inline-flex items-center  mr-5">
                    <div class="searchTitle">部门</div>
                    <el-input size="small" class="searchItem" v-model="searchCondition.部门" placeholder="" />
                </div>
                <div class="inline-flex items-center mr-5 mb-2">
                    <div class="searchTitle">打卡时间</div>
                    <el-date-picker size="small" class="searchItem" v-model="searchCondition.时间" type="daterange"
                        range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="YYYY-MM-DD" />
                </div>

                <div class="flex justify-end items-center mr-6 mt-2">
                    <ElButton type="primary" @click="onSearch">
                        <Icon icon="tabler:search" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>
                </div>
            </div>
            <div class="text-center mb-5 font-bold pt-5" style="color:#333">
                考勤设备人员信息
            </div>
            <!-- 图例 -->
            <div class="flex mb-1">
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#409EFF] rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    正常打卡
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#e1a04d]  rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    手动补卡
                </div>
                <div class="flex text-sm items-center mr-3">
                    <div class="bg-[#5fe66f] rounded-[50%] w-[14px] h-[14px] mr-1"></div>
                    修改打卡
                </div>
            </div>
            <el-table ref="userTableRef22" header-cell-class-name="tableHeader" :data="waterData"
                style="width: 100%;color: #666666;" border stripe>
                <el-table-column align="center" show-overflow-tooltip fixed prop="员工编号" :label="'员工编号'" width="100" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="员工姓名" :label="'员工姓名'" width="100" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="员工部门" :label="'员工部门'" width="200" />
                <el-table-column align="center" show-overflow-tooltip fixed prop="打卡日期" :label="'打卡日期'" width="100" />

                <el-table-column align="center" show-overflow-tooltip fixed prop="打卡时间" :label="'打卡时间'">
                    <template #default="scope">
                        <div class="flex justify-start items-center ml-5 flex-wrap">
                            <el-tag class="mr-1 mb-2 cursor-pointer" effect="dark"
                                :color="getColor(scope.row.打卡类型[index]) || '#409EFF'"
                                v-for="item, index in scope.row.打卡时间" :key="item"
                                @click="onEditWater(index, scope.row)">{{ item }}</el-tag>

                        </div>
                    </template>
                </el-table-column>

            </el-table>
            <el-pagination class="justify-end mt-8 mb-[200px]" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

        <el-dialog :title="selItem.序号 == '' ? '手动补卡' : '修改打卡'" v-model="showEdit" width="700" align-center destroy-on-close>
            <el-form>
                <el-form-item v-if="selItem.序号 != ''" class="titleShow" label="打卡序号:">
                    {{ selItem.序号 }}
                </el-form-item>
                <el-form-item class="titleShow" label="员工编号:">
                    {{ selItem.员工编号 }}
                </el-form-item>
                <el-form-item class="titleShow" label="员工姓名:">
                    {{ selItem.员工姓名 }}
                    <ElButton v-if="selItem.序号 == ''" size="small" @click="onSelUser" class="ml-10 w-[50px]">
                        <Icon icon="iconamoon:search-bold" />
                    </ElButton>
                </el-form-item>
                <el-form-item class="titleShow" label="打卡日期:">
                    <el-date-picker v-model="selItem.打卡日期" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item class="titleShow" label="打卡时间:">
                    <el-time-select v-model="selItem.打卡时间" start="00:00" step="00:05" end="23:59" placeholder="打卡时间" />
                </el-form-item>
                <el-form-item  class="titleShow" label="修改备注:">
                    <el-input v-model="selItem.修改备注" placeholder="" />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer1">
                    <el-button type="warning" @click="showEdit = false">取消</el-button>
                    <el-button type="primary" @click="handleOper('确定修改', selItem)">{{ selItem.序号 == '' ? '手动补卡' : '修改打卡'
                        }}</el-button>

                </div>
            </template>
            <template #header>
                <div class="dialog-header flex justify-start items-center">
                    <div class="dialog-title mr-10">
                        {{ selItem.序号 == '' ? '手动补卡' : '修改打卡' }}
                    </div>
                    <el-button v-if="selItem.序号 != ''" type="danger"
                        @click="handleOper('确定删除', selItem)">删除打卡</el-button>
                </div>
            </template>
        </el-dialog>
        <DialogUser :param="''" v-model:show="showSelUserDlg" :title="t('msg.selectUser')" @on-submit="onSelCallback" />
    </div>

</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
