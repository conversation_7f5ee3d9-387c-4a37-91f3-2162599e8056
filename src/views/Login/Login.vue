<script setup lang="ts">
import { LoginForm, RegisterForm } from './components'
import { ThemeSwitch } from '@/components/ThemeSwitch'
import { LocaleDropdown } from '@/components/LocaleDropdown'
import { useI18n } from '@/hooks/web/useI18n'
import { underlineToHump } from '@/utils'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { ref } from 'vue'
import { onBeforeMount } from 'vue'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('login')

const appStore = useAppStore()

const { t } = useI18n()

const isLogin = ref(true)

const toRegister = () => {
  isLogin.value = false
}

const toLogin = () => {
  isLogin.value = true
}

onBeforeMount(()=>{

})

</script>

<template>
  <div
    :class="prefixCls"
    class="h-[100%] relative <xl:bg-v-dark <sm:px-10px <xl:px-10px <md:px-10px"
  >
    <div class="relative h-full flex mx-auto">
      <div
        :class="`${prefixCls}__left flex-1  bg-opacity-20 relative p-30px <xl:hidden`"
      >

        <div class="flex items-center relative">
          <img src="@/assets/imgs/wenbologo.png" alt="" class="w-50px h-35px mr-10px" />
          <span class="text-18px font-bold" style="color: #333;">{{ underlineToHump(appStore.getTitle) }}</span>
        </div>
        <div class="flex justify-center items-center h-[calc(100%-60px)]">
          <TransitionGroup
            appear 
            tag="div"
            enter-active-class="animate__animated animate__bounceInLeft"
          > 
            <!-- <img src="@/assets/svgs/login-box-bg.svg" key="1" alt="" class="w-350px" /> -->
            <img src="@/assets/imgs/login-image.png" key="1" alt="" class="w-700px" />
            <!-- <div class="text-3xl text-center text-white" key="2">{{ t('login.welcome')}}</div> -->
          </TransitionGroup>
        </div>
      </div>
      <div class="flex-1 p-30px <sm:p-10px dark:bg-v-dark relative">
        <div class="flex justify-between items-center text-white @2xl:justify-end @xl:justify-end">
          <div class="flex items-center @2xl:hidden @xl:hidden">
            <img src="@/assets/imgs/wenbologo.png" alt="" class="w-48px h-48px mr-10px" />
            <span class="text-20px font-bold">{{ underlineToHump(appStore.getTitle) }}</span>
          </div>

          <div class="flex justify-end items-center space-x-10px">
            <!-- <ThemeSwitch />
            <LocaleDropdown class="<xl:text-white dark:text-white" /> -->
          </div>
        </div>
        <Transition appear enter-active-class="animate__animated animate__bounceInRight">
          <div
          style="position: relative;"
          class="h-full flex items-center m-auto w-[100%] @2xl:max-w-420px @xl:max-w-430px @md:max-w-500px @lg:max-w-500px"
          >
          <div class="welcome-text">
            <div>HELLO!</div>
            <div>欢迎使用文博ERP</div>
          </div>
            <LoginForm
              v-if="isLogin"
              class="p-30px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
              @to-register="toRegister"
            />
            <RegisterForm
              v-else
              class="p-20px h-auto m-auto <xl:(rounded-3xl light:bg-white)"
              @to-login="toLogin"
            />
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-login';

.@{prefix-cls} {
  &__left {
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      // background-image: url('@/assets/svgs/login-bg.svg');
      background-image: url('@/assets/imgs/login-bc.png');
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
      content: '';
    }
  }
}
.welcome-text{
  position: absolute;
  top: 0;
  color: rgba(33, 205, 127, 1);
  font-size: 1.8rem;
  font-weight: bold;
  text-shadow: 0px 6px 36px 0px rgba(220,219,219,0.5);
  letter-spacing: 1px;
}
</style>
