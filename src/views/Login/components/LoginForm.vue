<script setup lang="ts">
import { reactive, ref, unref, watch,computed, onMounted } from 'vue'
import { Form } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElCheckbox, ElLink, ElMessage, ElMessageBox } from 'element-plus'
import { useForm } from '@/hooks/web/useForm'
import { loginApi, wstest } from '@/api/login'
import { getRoleListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'
import { useRouter } from 'vue-router'
import type { RouteLocationNormalizedLoaded, RouteRecordRaw } from 'vue-router'
import { UserType } from '@/api/login/types'
import { useValidator } from '@/hooks/web/useValidator'
import { FormSchema } from '@/types/form'
import {abilityTabMap} from '@/router'
import { useIcon } from '@/hooks/web/useIcon'
import { getRouterTabItem } from '@/router'

const isMobile = computed(() => appStore.getMobile)

const { required } = useValidator()

const appStore = useAppStore()

const permissionStore = usePermissionStore()

const { currentRoute, addRoute, push } = useRouter()

const { wsCache } = useCache()

const { t } = useI18n()

const rules = {
  username: [required()],
  password: [required()]
}

const username = ref('11')
const password = ref('22')

const schema = reactive<FormSchema[]>([])

onMounted(async() => {

  console.log('???', currentRoute.value)
    // 获取当前 URL 的查询字符串部分
  const queryString = window.location.search;
  console.log('queryString', queryString)
  const urlParams = new URLSearchParams(queryString);
  // 获取 code 和 state 参数值
  let code = urlParams.get('code');
  let state = urlParams.get('state');
  console.log('code', code)
  console.log('state', state)

  if(code){
    //根据CODE获取微信uniid
    console.log('请求微信接口获取uni')
    try {
        const response = await fetch(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxbcc0dcdf0045d059&secret=SECRET&code=0113rPkl2VEU5f44cBnl2UZcQ213rPk3&grant_type=authorization_code`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('data----', data)
      } catch (error) {
        console.error('获取access_token失败', error);
      }

  }

  username.value = localStorage.getItem('username') || ''
  password.value = localStorage.getItem('password') || ''
  remember.value = localStorage.getItem('remember') === 'true'

  // 在 onMounted 钩子中更新 schema
  schema.splice(0, schema.length, 
    {
      field: 'title',
      colProps: {
        span: 24
      }
    },
    {
      field: 'username',
      value: username.value,
      component: 'Input',
      colProps: {
        span: 24
      },
      componentProps: {
        placeholder: t('login.usernamePlaceholder'),
        prefixIcon: useIcon({ icon: 'line-md:account' }),
      }
    },
    {
      field: 'password',
      value: password.value,
      component: 'InputPassword',
      colProps: {
        span: 24
      },
      componentProps: {
        style: {
          width: '100%'
        },
        placeholder: t('login.passwordPlaceholder'),
        prefixIcon: useIcon({ icon: 'ph:lock-key' }),
        onKeyup: (e) => {
          const currentTimestamp = Date.now()
          const timeDifference = currentTimestamp - lastEventTimestamp

          if (e.keyCode === 13 && timeDifference > timeThreshold) {
            lastEventTimestamp = currentTimestamp
            console.log('----1')
            signIn()
          }
        }
      }
    },
    {
      field: 'tool',
      colProps: {
        span: 24
      }
    },
    {
      field: 'login',
      colProps: {
        span: 24
      }
    }
  )
})

// 登录
const signIn = async () => {
  console.log('11111----')
  const formRef = unref(elFormRef)
  await formRef?.validate(async (isValid) => {
    if (isValid) {
      //loading.value = true
      const { getFormData } = methods
      const formData = await getFormData<UserType>()

      try {
        const res = await loginApi(formData)
        console.log(res)
        if (res) {
          if (res.data.roles.length == 0) { //该用户没有配置权限，跳转到无权限页面
            // ElMessage.error('该用户没有配置权限，请联系管理员')
            // wsCache.clear()

            ElMessageBox.alert('该用户没有配置权限，禁止登录系统，请联系管理员.', '提示', {
              confirmButtonText: '确定',
              callback: () => {
                  //如果TOKEN 错误或者超时，则需要退出登录
                  wsCache.clear()
                  location.reload()
                  // window.location.reload()
                  }
            })

            return
          }


          wsCache.set(appStore.getUserInfo, res.data)
          console.log(res.data.token)
          //写入TOKEN到本地
          wsCache.set('Token',res.data.token)
          getRole(res.data.roles,res.data.id)


          // 如果有记住密码则保存一下密码
          if (remember.value) {
            localStorage.setItem('username', formData.username)
            localStorage.setItem('password', formData.password)
            localStorage.setItem('remember', 'true')
          } else {
            localStorage.removeItem('username')
            localStorage.removeItem('password')
            localStorage.setItem('remember', 'false')
          }
        }
      } finally {
        //loading.value = false
      }
    }
    else{
      localStorage.removeItem('username')
      localStorage.removeItem('password')
      localStorage.setItem('remember', 'false')
    }
  })
}
let lastEventTimestamp = 0;
const timeThreshold = 100; // 设置一个时间阈值，单位为毫秒

// const schema = reactive<FormSchema[]>([
//   {
//     field: 'title',
//     colProps: {
//       span: 24
//     }
//   },
//   {
//     field: 'username',
//     // label: t('login.username'),
//     value: username,
//     component: 'Input',
//     colProps: {
//       span: 24
//     },
//     componentProps: {
//       placeholder: t('login.usernamePlaceholder'),
//       prefixIcon: useIcon({ icon: 'line-md:account' }),
//     }
//   },
//   {
//     field: 'password',
//     // label: t('login.password'),
//     value:password,
//     component: 'InputPassword',
//     colProps: {
//       span: 24
//     },
//     componentProps: {
//       style: {
//         width: '100%'
//       },
//       placeholder: t('login.passwordPlaceholder'),
//       prefixIcon: useIcon({ icon: 'ph:lock-key' }),
//       onKeyup: (e) => {
//         const currentTimestamp = Date.now();
//         const timeDifference = currentTimestamp - lastEventTimestamp;

//         if (e.keyCode === 13 && timeDifference > timeThreshold) {
//           lastEventTimestamp = currentTimestamp;
//           console.log('----1')
//           signIn();
//         }
//       }
//     },
//   },
//   {
//     field: 'tool',
//     colProps: {
//       span: 24
//     }
//   },
//   {
//     field: 'login',
//     colProps: {
//       span: 24
//     }
//   }
// ])

const remember = ref(false)

const { register, elFormRef, methods } = useForm()

const loading = ref(false)

const redirect = ref<string>('')

watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
    console.log("进入watch->"+redirect.value)
  },
  {
    immediate: true
  }
)


const testWs = async()=>{
  console.time()
  //const res = await loginApi(formData)
  for(let i=1;i<=10;i++)
  {
    wstest('login'+i).then((ret)=>{
      console.log(ret)
    })

  }
  console.timeEnd()
}

//整合权限数组
const rebuildRoleList = (arry) => {
  console.log('1111111111111111---',arry)
    let out = []
    for(let one of arry)
    {
      for(let route of one.routes)
      {
        let find = out.find((item)=>{
          return item.name == route.name?item:undefined
          })
        if(find == undefined)
        {
          out.push(route)
        }
        else
        {
          find.routes = Array.from(new Set([...find.routes, ...route.routes]));
        }
      }
    }
    return out
  }

  //构造管理员
  const creatFullRole = (allRole)=>{
    let arrAll = []
    for(let root of allRole)
    {
        let one = {}
        one.name = root.name
        one.title = root.title
        one.routes = []
        for(let parent of root.routes)
        {
            one.routes.push(parent.path)
            for(let child of parent.children)
            {
                one.routes.push(parent.path+'/'+child.path)
            }
        }
        arrAll.push(one)
    } 
    return arrAll
  }

// 获取角色信息
const getRole = async (roles,id) => {
  const { getFormData } = methods
  const formData = await getFormData<UserType>()


  const res =  await getRoleListApi({
    ids:roles,
    page:1,
    count:100
  })
  if (res) {
    console.log("000000000")
    console.log(res.data)

    //测试期间临时替换远程权限
    //res.data = testMap 



    //整合所有角色种的权限
    let routersMap =  []
    if(id == 0) 
    {
      routersMap = creatFullRole(abilityTabMap)
    }
    else
    {
      routersMap = rebuildRoleList(res.data)
    }


    console.log('role',routersMap)
    //保存下远程权限配置
    const { wsCache } = useCache()
    wsCache.set('roleRouters', routersMap)
    console.log('--', routersMap)

    //登录过来的直接加载第一个大权限的路由
    let srcMap:AppRouteRecordRaw[] = []
    for(let i=0;i<abilityTabMap.length;i++)
    {
      if(abilityTabMap[i].name == routersMap[0]["name"])
      {
        srcMap = abilityTabMap[i].routes
      }
    }


    //push({ path: redirect.value || permissionStore.addRouters[0].path })
    if (isMobile.value) {
      const srcMap = getRouterTabItem('mobileRouterMap')?.routes
        console.log(srcMap)
        //构造所有roles
        let arrayRole = []
        for (const one of srcMap) {
          arrayRole.push(one.path)
          for (const item of one.children) {
            arrayRole.push(one.path+'/'+item.path)
          }
         }

        await permissionStore.generateRoutes(srcMap, arrayRole)
        permissionStore.getAddRouters.forEach((route) => {
          addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
        })
        permissionStore.setIsAddRouters(true)
        console.log("当前redire->>>"+redirect.value+"    "+permissionStore.addRouters[0].path)
        push({ path: '/mobile/main' })

        
    }
    else {
      console.log("999999")  
      console.log(abilityTabMap)
      console.log(srcMap)
      console.log(routersMap[0]["routes"])
      await permissionStore.generateRoutes(srcMap, routersMap[0]["routes"]).catch(() => {})

      permissionStore.getAddRouters.forEach((route) => {
        addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
        console.log('======',route)
      })
      permissionStore.setIsAddRouters(true)
      console.log(permissionStore.addRouters[0].path)
      console.log('??',permissionStore.addRouters)
      console.log("当前redire->>>"+redirect.value+"    "+permissionStore.addRouters[0].path)
      push({ path: permissionStore.addRouters[0].path })
    }
  }


  
}


</script>

<template>
  <Form
    :schema="schema"
    :rules="rules"
    label-position="top"
    hide-required-asterisk
    size="large"
    class="dark:(border-1 border-[var(--el-border-color)] border-solid) my-form"
    @register="register"
  >
    <template #title >
      <div class="text-22px font-bold text-center w-[100%]" style="line-height: 38px;">{{ t('login.login') }}</div>
      <div class="text-center text-12px w-[100%]" style="color: #999;margin-bottom: 20px;">WELCOME TO LOGIN</div>
    </template>

    <template #tool>
      <div class="flex justify-between items-center w-[100%]" style="margin-top: -30px;padding: 0 8px;margin-bottom: 20px;">
        <ElCheckbox class="mt-5"  style="font-size: 12px;" v-model="remember" label="记住账号密码" size="small" />
        <!-- <ElLink style="font-size: 12px;" type="primary" :underline="false">{{ t('login.forgetPassword') }}</ElLink> -->
      </div>
    </template>

    <template #login>
      <div class="w-[100%]">
        <ElButton :loading="loading" type="primary" class="w-[100%]" style="background-color:#21CD7F;font-size: 18px;" @click="signIn" round >
          {{ t('login.login') }}
        </ElButton>
      </div>
    </template>

  </Form>
</template>

<style lang="less" scoped>
:deep(.anticon) {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}
.my-form{
  box-shadow: 0px 6px 36px 16px rgba(220,219,219,0.5);
  padding: 38px 33px 40px;
  border-radius: 12px;
}
:deep(.el-input__wrapper){
  border-radius: 30px;
}
:deep(.el-form-item--large){
  margin-bottom: 34px !important;
}

:deep(.el-checkbox__input){
  border: 2px solid #999;
}
</style>
