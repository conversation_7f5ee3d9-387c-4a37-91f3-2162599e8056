<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElCard,ElTable,ElPopconfirm,ElTag,ElCheckbox,ElDatePicker,ElTreeSelect,ElSelect,ElOption,ElTooltip,ElTableColumn,ElButton,ElForm,ElFormItem,FormRules,ElDescriptions,ElDescriptionsItem, ElInput,ElImage, ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted,nextTick } from 'vue'
import { getProductListApi,getProductInfoApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount,watch } from 'vue'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogProductSel } from '@/components/DialogProductSel'
import {ceilToFixed, checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath} from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getOemOrderListApi,getOemOrderInfoApi,addOemOrderApi,updateOemOrderApi,getOemOrderNewnumApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep, last } from 'lodash-es'
import { getGUID } from '@/api/tool'
import { DialogSelParter } from '@/components/DialogSelParter'
import { DialogSelQuekou } from '@/components/DialogSelQuekou'
import { getParterListApi } from '@/api/customer'
import { DialogCheckShow } from '@/components/DialogCheckShow'

const { currentRoute,back,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    parter_nick: [{ required: true, message: t('msg.noParter'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
    pay_type:[{ required: true, message: t('msg.noPayType'), trigger: 'blur' }],
})


//销售单数据
const orderData = reactive(
    {
    "parter_id": "",
     parter_nick:'',
    "is_same_delivery": 0,
    "delivery_date": "", //交货日期
    "pay_type": "月结",
    "oem_order_num": "",
    "oem_date": "", //开单日期
    "oem_man_id": "", //下单人员
    "pdt_list": [], //关联单号
    fsm_can_trig_data:{
        审核触发:[],
        操作触发:[]
    }, //审核决策
    fsm_cur_state:'',    //当前节点状态
    fsm_exe_man_name:'',
    fsm_exe_log:'',
    fsm_exe_trig:'',//决策内容
    fsm_log_list:[]
})

//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])


//获取最新ID
const onChangeID = async()=>{
    const ret = await getOemOrderNewnumApi()
    if(ret)
    {
        console.log(ret)
        orderData.oem_order_num = ret.data.new_id
    }
}



//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val='') => {
  const ret = await getProductListApi({
    name:val,
    nick:val,
    _or:true,
    prop_list:['委外产品'],
    status:'正常',
    page: 1,
    count:30,
  })
  if(ret)
  {
    searchPdtData.splice(0,searchPdtData.length, ...ret.data)
    if(searchPdtData.length >0)
    {
        setCurrent(searchPdtData[0])
    }
  }
}

//根据产品列表中数量计算委外总数
const totleSale = computed(() => {
    let totle = 0
    for(const item of orderData.pdt_list)
    {
        if(item.委外数量 == undefined || item.id == undefined)
            continue
        totle += parseFloat(item.委外数量)
        totle += parseFloat(item.委外备品数量)
    }
    return totle
})

//根据产品列表计算委外 总价
const totleSalePrice = computed(() => {
    let totle = 0
    let span = 0
    for(const item of orderData.pdt_list)
    {
        if(item.总价 == undefined || item.id == undefined)
            continue
        if(item.sell_buy_digit != undefined)
            span = parseFloat(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }
    
    return  parseFloat(totle.toFixed(span))
})

//计算备品价格
const countBakPrice = (pdt)=>{
    //委外备品数量占采购数量的3%以内不算钱，超过3%最多只计算1%的钱
    // if(pdt.委外备品数量<=pdt.委外数量*0.03)
    // {
    //     return 0
    // }
    // else
    // {
    //     return Math.min(parseFloat((pdt.委外备品数量*pdt.oem_price_aft_tax).toFixed(pdt.sell_buy_digit)) , parseFloat((pdt.委外数量*0.01*pdt.oem_price_aft_tax).toFixed(pdt.sell_buy_digit)))
    // }
    return 0
}


// //重新计算产品相关数据
// let timeoutId: ReturnType<typeof setTimeout> | null = null;
// const reComputePdtInfo = (pdt,mode) => {
//     // 取消之前的延迟处理
//     if (timeoutId) {
//         clearTimeout(timeoutId);
//     }

//     // 创建新的延迟处理
//     timeoutId = setTimeout(() => {
//         reComputePdtInfoFun(pdt,mode)
//     }, 1000); // 设置延迟时间，单位为毫秒
// }

//重新分配PDT中的来源数量
const reComputeSourceCount = (pdt) => {
    //组合PDT 才需要调整
    console.log('pdt.原始需求数量', pdt.原始需求数量, pdt)
    if(pdt.原始需求数量.indexOf(',')>=0)
    {
        let nTotle = parseFloat(pdt.委外数量) + parseFloat(pdt.委外备品数量)
        let arCount:string[] = pdt.原始需求数量.split(',')
        let arSourceTotle = 0
        //计算总需求
        for(const item of arCount)
        {
            arSourceTotle += parseFloat(item)
        }
        if (nTotle < arSourceTotle) {
            //需要按照从前往后的顺序来分nTotle，每份不超过原始需求数量中对应每一份的数量
            let arOut:string[] = []
            for(let one of arCount)
            {
                if(nTotle>0)
                {
                    if(nTotle>=parseFloat(one))
                    {
                        arOut.push(one)
                        nTotle -= parseFloat(one)
                    }
                    else
                    {
                        arOut.push(nTotle)
                        nTotle = 0
                    }
                }
            }
            pdt.需求数量 = arOut.join(',')
            console.log('调整后:',pdt.需求数量)
        }
        else
        {
            //还原默认
            pdt.需求数量 = pdt.原始需求数量            
            console.log('不需要调整:',pdt.委外数量,pdt.委外备品数量,nTotle,arSourceTotle)
        }

        //先还原
        pdt.oem_order_num = pdt.原始oem_order_num
        pdt.oem_order_sub = pdt.原始oem_order_sub
        pdt.父子任务单号 = pdt.原始父子任务单号
        pdt.标识 = pdt.原始标识

        //pdt.oem_order_num,pdt.oem_order_sub 对应的字符传逗号分割的数量要和 pdt.需求数量  保持一直 后面多的删除
        let arSell:string[] = pdt.oem_order_num.split(',')
        let arOem: string[] = pdt.oem_order_sub.split(',')
        let arZRWDH: string[] = pdt.父子任务单号.split(',')
        let arNeed: string[] = pdt.需求数量.split(',')
        let arBS:string[] = pdt.标识.split(',')
        //arSell arOem 数组slice前arNeed长度 的内容
        let arSellOut:string[] = arSell.slice(0,arNeed.length)
        let arOemOut: string[] = arOem.slice(0, arNeed.length)
        let arZRWDHOut: string[] = arZRWDH.slice(0, arNeed.length)
        let arBSOut:string[] = arBS.slice(0,arNeed.length)

        console.log('arSellOut',arNeed.length,arSell.slice(0,arNeed.length))

        pdt.oem_order_num = arSellOut.join(',')
        pdt.oem_order_sub = arOemOut.join(',')
        pdt.父子任务单号 = arZRWDHOut.join(',')
        pdt.标识 = arBSOut.join(',')
    }
    else  //针对非组单也需要调整数量
    {
        let nTotle = parseFloat(pdt.委外数量) + parseFloat(pdt.委外备品数量)
        if(nTotle < parseFloat(pdt.原始需求数量))
        {
            pdt.需求数量 = nTotle 
        }
        else
        {
            pdt.需求数量 = pdt.原始需求数量
        }
    }

}

//重新计算产品相关数据
const reComputePdtInfo = (pdt,mode) => {
    pdt.委外数量 = pdt.委外数量 == ''?0:pdt.委外数量
    pdt.委外备品数量 = pdt.委外备品数量 == ''?0:pdt.委外备品数量
    pdt.oem_price_bef_tax = pdt.oem_price_bef_tax == ''?0:pdt.oem_price_bef_tax
    pdt.oem_price_aft_tax = pdt.oem_price_aft_tax == ''?0:pdt.oem_price_aft_tax


    if(mode == '委外数量')
    {
        //调整默认备品数量
       // pdt.委外备品数量 = parseFloat(pdt.委外数量*0.01)

        //根据销售数量计算总价 总价等于税后单价乘以销售数量
        //重新计算税后价格
        // pdt.oem_price_aft_tax = parseFloat(pdt.oem_price_bef_tax)+ parseFloat(pdt.oem_price_bef_tax)*pdt.发票税率/100
        
        pdt.总价 = ceilToFixed((pdt.oem_price_aft_tax*pdt.委外数量),6,pdt.总价)+countBakPrice(pdt)

        //调整来源中的数量
        reComputeSourceCount(pdt)
    }
    else if(mode  == '委外备品数量')
    {
        pdt.总价 = ceilToFixed((pdt.oem_price_aft_tax * pdt.委外数量),6,pdt.总价) + countBakPrice(pdt)
        
        reComputeSourceCount(pdt)
    }
    else if(mode == '税前单价')
    {
        //根据税前单价和税率计算出税后单价         
        pdt.oem_price_aft_tax = ceilToFixed(parseFloat(pdt.oem_price_bef_tax)+ parseFloat(pdt.oem_price_bef_tax)*pdt.发票税率/100,8,pdt.oem_price_aft_tax)
        //计算出总价
        pdt.总价 = ceilToFixed((pdt.oem_price_aft_tax*pdt.委外数量),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '税后单价')
    {
        //根据税后单价和税率计算出税前单价         
        pdt.oem_price_bef_tax = ceilToFixed((pdt.oem_price_aft_tax/(1+pdt.发票税率/100)),8,pdt.oem_price_bef_tax)
        //计算出总价
        pdt.总价 = ceilToFixed((pdt.oem_price_aft_tax*pdt.委外数量),6,pdt.总价)+countBakPrice(pdt)
    } 
    else if(mode == '发票税率')
    {
        //根据发票类型调整税率数
        if(!['普票','专票','电子票'].includes(pdt.发票类型))
        {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.oem_price_aft_tax = pdt.oem_price_bef_tax
        }
        //重新计算税后价格
        // pdt.oem_price_aft_tax = parseFloat(pdt.oem_price_bef_tax)+ parseFloat(pdt.oem_price_bef_tax)*pdt.发票税率/100
        pdt.oem_price_bef_tax = ceilToFixed((pdt.oem_price_aft_tax/(1+pdt.发票税率/100)),8,pdt.oem_price_bef_tax)
        //重新计算总价
        pdt.总价 = ceilToFixed((pdt.oem_price_aft_tax*pdt.委外数量),6,pdt.总价)+countBakPrice(pdt)
    }
    else if(mode == '总价')
    {
        //根据总价重新调整税前税后价格
        pdt.oem_price_aft_tax = ceilToFixed((pdt.总价/(parseFloat(pdt.委外数量))),8,pdt.oem_price_aft_tax)
        pdt.oem_price_bef_tax = ceilToFixed((pdt.总价/((1+pdt.发票税率/100)*(parseFloat(pdt.委外数量)))),8,pdt.oem_price_bef_tax)
    }

    pdt.委外数量 = parseFloat(pdt.委外数量)
    pdt.委外备品数量 = parseFloat(pdt.委外备品数量)
    pdt.oem_price_bef_tax = parseFloat(pdt.oem_price_bef_tax)
    pdt.oem_price_aft_tax = parseFloat(pdt.oem_price_aft_tax)

}


let flag_index = 1 //标识索引
onMounted(async () => {    
    console.log('------------------')
    if((currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined)&&currentRoute.value.query.oem_order_num == undefined)
    {
        title.value = t('oem.add_order')
        onChangeID()

        //新建需要默认可以提审
        const tmp = [...orderData.fsm_can_trig_data.操作触发,'提交审核']
        orderData.fsm_can_trig_data.操作触发 = tmp

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        orderData.oem_man_id = info.id
        orderData.oem_man_name = info.resident_name
        //默认日期为今天
        orderData.oem_date = getTodayDate()


        //是否有销售转采购任务
        if(currentRoute.value.query.tooem == '1')
        {
            orderData.source = currentRoute.value.query.source
            const arPdtNum = wsCache.get('tooem')
            console.log('===',arPdtNum)
            //添加pdt到列表
            for(const pdt of arPdtNum)
            {
                const ret = await getProductInfoApi({
                    name:pdt.pdt_name,
                    page: 1,
                    count:20,
                })
                if(ret)
                {
                    console.log('委外转委外11')
                    //如果有多个找到匹配的
                    let find =ret.data

                    handleCurrentSelectPdt(find,'',true)
                    if(orderData.source == '销售转委外')
                    {
                        console.log('委外转委外2')
                        if(pdt.销售备品数量 == undefined)
                            pdt.销售备品数量 = 0
                            find.sell_order_num = pdt.sell_order_num
                            find.需求数量 = pdt.当前需求
                            find.委外数量 = pdt.当前需求-pdt.销售备品数量
                            find.委外备品数量 = pdt.销售备品数量

                        //没有订单号表示不关联
                        if(pdt.sell_order_num == '')
                        {
                            find.标识 = getGUID(5)  
                           // find.子任务单号 = getNextFlag()
                            orderData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.oem_order_sub = pdt.oem_order_sub
                         //   find.子任务单号 = getNextFlag()//pdt.子任务单号
                            find.父子任务单号 = pdt.子任务单号
                        }
                    }
                    if(orderData.source == '委外转委外')
                    {
                        console.log('委外转委外3')
                        if(pdt.委外备品数量 == undefined)
                            pdt.委外备品数量 = 0
                        find.oem_order_num = pdt.oem_order_num
                        find.需求数量 = pdt.需求数量
                        find.委外数量 = pdt.当前需求-pdt.委外备品数量
                        find.委外备品数量 = pdt.委外备品数量

                        find.原始需求数量 = pdt.需求数量
                        find.原始oem_order_num = pdt.oem_order_num
                        find.原始oem_order_sub = pdt.oem_order_sub
                        find.原始父子任务单号 = pdt.子任务单号
                        find.原始标识 = pdt.标识

                        console.log('委外转委外',find)

                                                //没有订单号表示不关联
                        if(pdt.oem_order_num == '')
                        {
                            find.标识 = getNextFlag()//getGUID(5)  
                           // find.子任务单号 = getNextFlag()
                            orderData.source = ''
                        }
                        else
                        {
                            find.标识 = pdt.标识   
                            find.oem_order_sub = pdt.oem_order_sub
                          //  find.子任务单号 = getNextFlag()//pdt.子任务单号
                            find.父子任务单号 = pdt.子任务单号
                        }

                    }




                    reComputePdtInfo(find,'委外备品数量')
                    //  console.log('???',orderData.source,find.sell_order_num,find.oem_order_num)  
                
                }
            }
        }





        //追加默认行
        orderData.pdt_list.push({总价:'0'}) 
        
    }
    else
    {        

        if(currentRoute.value.query.type == 'info')
        {
            title.value = t('oem.look')
        }
        else
        {
            title.value = t('oem.modify_order')
        }

        
        //查询产品信息 
        const ret = await getOemOrderInfoApi({
            id:currentRoute.value.query.id,
            oem_order_num:currentRoute.value.query.oem_order_num==undefined?'':currentRoute.value.query.oem_order_num,
            page:1,
            count:100,
            realrole:'oem.order_list'
        })
        if(ret)
        {
            console.log(ret)
            Object.assign(orderData, ret.data)


            //处理下所有pdt 的新增标识
            for(let i=0;i<ret.data.pdt_list.length;i++)
            {
                ret.data.pdt_list[i].新增 = false
            }


            orderData.pdt_list = ret.data.pdt_list;
            orderData.pdt_list.push({总价:'0'}) 
            flag_index = orderData.pdt_list.length+1
        }
        nextTick(()=>{
            if(currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if(excludeDiv != null)
                {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else
                {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
})


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = ()=>{
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event)=>{

    const elTableContainers = document.querySelectorAll('.el-table-container');      
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }
        
    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if(!bIn)
        {
            showProductSel.value = false
        }
    }
}
const delaytime =ref<NodeJS.Timeout | null>(null); 
//处理输入
const onInputSearch =(val)=>{
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)
    
    delaytime.value = setTimeout(() => {
      // 输入停止后触发的方法
      getProductList(val)
    }, 200) 
}


//显示隐藏选择委外弹窗
const showSelParterDlg = ref(false)
const onSelParter = ()=>{
    showSelParterDlg.value = true
}
//选择客户回调
const onSelParterCallback = (id,name,customer)=>{
    console.log(id,name,customer)
    orderData.parter_id = id
    orderData.parter_name = name
    orderData.parter_nick = customer.parter_nick
    orderData.tax_type = customer.tax_type == undefined?'普票':customer.tax_type
    orderData.tax_rate = customer.tax_rate ==''?0:(customer.tax_rate=='不含税'?0:(customer.tax_rate.replace('%','')))
    console.log(orderData)

    //同步更新所有产品税率
    for(let item of orderData.pdt_list)
    {
        item.发票类型 = orderData.tax_type == ''?'专票':orderData.tax_type
        item.发票税率 =  parseInt(orderData.tax_rate)
        reComputePdtInfo(item,'发票税率')
    }
    //如果没有联系人列表则使用默认联系人
    if(customer.corp_linkman.length == 0 && customer.phone1 !='')
    {//mainer_name phone1
        customer.corp_linkman.push({
            name:customer.mainer_name,
            phone:customer.phone1
        })
    }

}


//显示隐藏选择操作员窗口变量
const showSelSaleUserDlg = ref(false)
//显示选择操作员弹窗
const onSelSale = ()=>{
    showSelSaleUserDlg.value = true
}
//选择操作员回调
const onSelSaleCallback = (id,name)=>{
    console.log(id,name)
    orderData.oem_man_id = id
    orderData.oem_man_name = name
}


//最后一次选择的行
const lastRow = ref({id:''})
//显示隐藏选择负责人窗口变量
const showSelMgrDlg = ref(false)
//显示选择负责人弹窗
const onSelMgr = (item)=>{
    showSelMgrDlg.value = true
    lastRow.value = item
}
//选择负责人回调
const onSelMgrCallback = (id,name)=>{
    console.log(id,name)
    lastRow.value.负责人_id = id
    lastRow.value.负责人 = name
}


//选择关联销售单
const showSelQuekouDlg = ref(false)
const onSelSellNum = (item)=>{
    showSelQuekouDlg.value = true
    lastRow.value = item
    console.log(item)
}
//选择结束回调
const onSelSellNumCallback = (list)=>{
    console.log(list)
    lastRow.value.sell_order_list.splice(0,lastRow.value.sell_order_list.length)
    for(let one of list)
    {
        lastRow.value.sell_order_list.push(one.销售单号)
    }
}


//获取下一个标识
const getNextFlag = ()=>{
    let txt = ''
    while(true)
    {
        txt = orderData.oem_order_num+'_'+flag_index++
        let bSame = false
        for(let pdt of orderData.pdt_list)
        {
            if(pdt.标识 == txt)
            {
                bSame = true
                break
            }
        }
        if(!bSame)
        {
            break
        }
    }
    return txt

}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item,old,fource = false) =>{
    if( item == undefined)
    {
        return
    }

    nCurSelPdtID.value = item.id
    if(!fource){
        return
    }
    txtSearch.value = ''
    
    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    },500)
       
   
    //增加属性    
    item.委外数量 = 1
    item.交货日期 = getTodayDate()
    item.发票类型 = '专票'
    item.发票税率 = 0
    item.总价 = 0
    item.标识 = getGUID(5)// orderData.oem_order_num+'_'+flag_index++//getGUID(10)
    item.子任务单号 = getNextFlag()
   // item.oem_order_sub= getNextFlag()
    item.委外备注 = ''
    item.最小包装 = 1
    
    item.已收货 = 0
    item.未收货 = 0
    item.良品退货 = 0
    item.不良退货 = 0
    item.良品数 = 0
    item.发料数 = 0
    item.不良数 = 0
    item.领料数 = 0
    item.物料赔付金 = 0
    item.附加费 = 0
    item.sell_order_num = ''
    item.新增 = true
    item.原始需求数量 = ''
    item.原始父子任务单号 = ''
    item.原始oem_order_num = ''
    item.原始oem_order_sub = ''
    item.原始标识 = ''

    const info = wsCache.get(appStore.getUserInfo)
    item.负责人_id = info.id
    item.负责人 = info.resident_name

    //设置默认价格
    item.oem_price_bef_tax = item.buy_price_bef_tax
    item.oem_price_aft_tax = item.buy_price_aft_tax

    item.委外备品数量 = 0

    //如果已经选择了客户则自动同步税率
    if(orderData.tax_rate != undefined)
    {
        item.发票类型 = orderData.tax_type
        item.发票税率 = orderData.tax_rate
    }


    //更新产品价格
    reComputePdtInfo(item,'委外数量')

    //构造一行产品
    orderData.pdt_list.splice(-1,0,item)
    //隐藏浮窗
    showProductSel.value = false    

    console.log(orderData)
    
}

//删除某一个产品
const onDelPdt = (index) => {
    if(orderData.pdt_list[index].已收货>0)
    {
        ElMessage.error('已收货的产品不能删除!')
        return
    }
    orderData.pdt_list.splice(index,1)
}




// //保存
// const onSave = async()=>{
//     console.log(orderData)

//     const rule = await checkFormRule(ruleFormRef.value)
//     if(!rule)
//     {
//         ElMessage.warning(t('msg.checkRule'))
//         return
//     }

//     //删除最后一行临时数据(克隆新数据提交不影响原数据)
//     const data = cloneDeep(orderData)
//     data.pdt_list.splice(-1,1)

//     if(!checkPdt())
//     {
//         console.log('???')
//         return 
//     }

//     //复位新增标记
//     for(let pdt of data.pdt_list)
//     {
//         pdt.新增 = false
//     }
    
//     if(orderData.id == undefined)
//     {
//         const ret = await addOemOrderApi(data)
//         if(ret)
//         {
//             ElMessage.success(t('msg.newOemOrderSuccess'))
//             baskFront()
//         }
//     }
//     else //修改
//     {
//         const ret =await updateOemOrderApi(data)
//         if(ret)
//         {
//             ElMessage.success(t('msg.updateOemOrderSuccess'))
//             baskFront()
//         }
//     }


// }
//校验pdt
const checkPdt = ()=>{
    if(orderData.pdt_list.length <= 1)
    {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//提交审核意见
const handleCheck = async(btn,bClose = true)=>{
    console.log(11)
    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if(!checkPdt())
    {
        return
    }
    const data = cloneDeep(orderData)


    data.pdt_list.splice(-1,1)

    //处理下空数据问题
    for(let pdt of data.pdt_list)
    {
        pdt.委外数量 = pdt.委外数量 == ''?0:pdt.委外数量
        pdt.委外备品数量 = pdt.委外备品数量 == ''?0:pdt.委外备品数量
        pdt.oem_price_bef_tax = pdt.oem_price_bef_tax == ''?0:pdt.oem_price_bef_tax
        pdt.oem_price_aft_tax = pdt.oem_price_aft_tax == ''?0:pdt.oem_price_aft_tax
    }


    const info = wsCache.get(appStore.getUserInfo)
    data.fsm_exe_man_name = info.resident_name
    data.fsm_exe_trig = btn

    if(data.id == undefined)
    {
        const ret = await addOemOrderApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.newOemOrderSuccess'))
            if(checkPermissionApi('委外订单同页面审核'))
            {
                const oldPath = currentRoute.value.path
                push({
                    path: '/systemmanage/tmp'
                }).then(()=>{
                    //刷新当前页面
                    console.log('刷新了！！！！')
                    push({
                        path: oldPath,
                        query:{
                            id:'',
                            oem_order_num:ret.data.oem_order_num,
                            type:'info',
                            cmd:'审核'
                        }
                    })
                })
            }
            else    
            {
                baskFront()
            }
            
        }
    }
    else //修改
    {
        const ret =await updateOemOrderApi(data)
        if(ret)
        {
            ElMessage.success(t('msg.updateOemOrderSuccess'))
            if(btn == '恢复')
            {
                //查询产品信息 
                const ret = await getOemOrderInfoApi({
                    id:currentRoute.value.query.id,
                    page:1,
                    count:100
                })
                if(ret)
                {
                    console.log(ret)
                    Object.assign(data, ret.data)

                    data.pdt_list = ret.data.pdt_list;
                    data.pdt_list.push({总价:'0'}) 
                    flag_index = data.pdt_list.length+1
                }
            }
            else {
                if(bClose)
                {
                    baskFront()
                    // closeOneTagByPath('/oemmanage/oemorderlist')
                }

            }
            if(btn == '同意')
            {
                bShowCheck.value = false
            }
            
        }
    }
    if(bClose)
    {
        //更新需求
        if(data.source == '销售转委外')
        {
            closeOneTagByPath('/salemanage/saledemand')
        }
        else if(data.source == '委外转委外')
        {
            closeOneTagByPath('/oemmanage/wlneedinfo')
        }
    }

}

const bShowCheck = ref(true)

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = ()=>{
    showCheckHisDlg.value = true
}

//返回上一页
const baskFront = () => {
    console.log('okokoko')
    // back()
    if (orderData.source == '销售转委外')
    {
        closeOneTagByPath('/salemanage/salemanage')
        push('/salemanage/salemanage')
    }
    else
        push('/oemmanage/oemorderlist')
    closeOneTagByName(currentRoute.value.meta.title)
}


// 使用 computed 定义计算属性
const sameDay = computed({
  get: () => (orderData.delivery_date === '' ? false : true),
  set: (value) => {
    if (value) {
        orderData.delivery_date = getTodayDate();
      //更新下面列表
      orderData.pdt_list.forEach((item, index) => {
          item.交货日期 = orderData.delivery_date;
      })
    } else {
        orderData.delivery_date = '';
    }
    console.log('->',orderData.delivery_date);
  },
});

const onChangeDeliverData = (value)=>{
    orderData.pdt_list.forEach((item, index) => {
          item.交货日期 = orderData.delivery_date;
      })
}

//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event)=>{
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if(event.keyCode === 27)
    {
        showProductSel.value = false  
        return
    }
    bKeyDown.value = true
    if(nCurSelPdtID.value == '')
    {
        setCurrent(searchPdtData[0])
    }
    else  
    {


        for(let i = 0;i<searchPdtData.length;i++)
        {
            if(searchPdtData[i].id == nCurSelPdtID.value)
            {
                if(event.keyCode === 38 && i>0)
                    setCurrent(searchPdtData[i-1])
                else if(event.keyCode === 40 && i<searchPdtData.length-1)
                    setCurrent(searchPdtData[i+1])
                //如果是回车，直接选择
                else if(event.keyCode === 13)
                {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if(row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent',searchRef)
    searchRef.value!.setCurrentRow(row)
}
const onRowClick = (row)=>{
    console.log('xuanle',row)
    handleCurrentSelectPdt(row,null,true)
}

</script>

<template>
    <ContentDetailWrap :title="title" @back="baskFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史</ElButton>
        </template>
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;" v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && orderData.fsm_can_trig_data.操作触发.includes('保存')" @click="handleCheck('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存并关闭
            </ElButton>
            <ElButton type="warning"  style="color: #fff;" v-show="currentRoute.query.cmd != '审核'&& currentRoute.query.type != 'info' && orderData.fsm_can_trig_data.操作触发.includes('保存')" @click="handleCheck('保存',false)">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <el-popconfirm  title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && orderData.fsm_can_trig_data.操作触发.includes('提交审核')"   type="success" >
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>
            
            <el-popconfirm  title="是否确认关闭订单?" @confirm="handleCheck('关闭')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && orderData.fsm_can_trig_data.操作触发.includes('关闭')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm  title="是否恢复已关闭订单?" @confirm="handleCheck('恢复')">
                <template #reference>
                    <ElButton  v-show="currentRoute.query.cmd != '审核' && orderData.fsm_can_trig_data.操作触发.includes('恢复')" type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        恢复订单
                    </ElButton>
                </template>
            </el-popconfirm>

        </template>
        <el-card id="check" v-if="orderData.fsm_can_trig_data.审核触发.length>0 && currentRoute.query.cmd == '审核' && bShowCheck" class="w-[100%]">
            <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ orderData.fsm_cur_state }}</span>
                <!-- <ElButton @click="handleCheck(btn)" v-for="btn in purchaseData.fsm_can_trig_data.审核触发.toReversed()" :key="btn" :type="btn=='同意'?'success':'danger'">{{ btn }}</ElButton> -->
                <ElButton v-show="orderData.fsm_can_trig_data.审核触发.includes('同意')" type="success" @click="handleCheck('同意')" >同意并关闭</ElButton>
                <ElButton v-show="orderData.fsm_can_trig_data.审核触发.includes('同意')" type="primary" @click="handleCheck('同意',false)" >同意</ElButton>
                <el-popconfirm  title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="orderData.fsm_can_trig_data.审核触发.includes('驳回')" type="danger" >驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm  title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="orderData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger" >拒绝</ElButton>
                    </template>
                </el-popconfirm>

            
            </div>
            </template>
            <el-input v-model="orderData.fsm_exe_log" class="mt-3"   :autosize="{ minRows: 5, maxRows: 2 }" type="textarea"/>
        </el-card>

        <el-form :rules="rules" :model="orderData" ref="ruleFormRef" >
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1" :label="t('parter.nick')"
                    class="flex">
                    <el-form-item prop="parter_nick">
                        <div class="mr-2">{{ checkPermissionApi('受托商名称显示')?orderData.parter_nick:'***' }}</div> 
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelParter">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('oem.name')"
                    class="flex">
                    <el-form-item prop="name" >
                        <div class="flex">
                            <el-input class="mr-1" v-model="orderData.oem_order_num" :disabled="orderData.id!=undefined" />
                            <ElButton v-if="orderData.id==undefined" type="primary"  @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" :label="t('oem.pay_type')"
                    class="flex">
                    <el-form-item prop="pay_type" >
                        <el-select v-model="orderData.pay_type" placeholder="请选择">
                            <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('oem.opter')"
                    class="flex">
                    <el-form-item>
                        <div class="mr-2">{{ orderData.oem_man_name }}</div> 
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelSale">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>                
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'交货日期'"
                    class="flex">
                    <el-form-item >
                        <el-checkbox style="margin-right: 10px;" v-model="sameDay" label="统一" size="large" />
                        <el-date-picker :clearable="false" v-if="sameDay" v-model="orderData.delivery_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" @update:model-value="onChangeDeliverData"/>
                    </el-form-item>
                </el-descriptions-item>
            </el-descriptions>        
        </el-form>

        <el-table class="mt-2 mb-2" header-cell-class-name="tableHeader" cell-class-name="table_cell" :data="orderData.pdt_list" style="width: 100%" border stripe>
            <el-table-column  :label="t('process.opt')" width="60" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined && currentRoute.query.type != 'info'"  type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer" style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>
                        
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="id" :label="t('userTable.id')" width="60" />
            <el-table-column  :label="t('sale.img')" width="80" >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px]" src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('oem.task_num')" min-width="130">
                <template #default="scope">
                    <el-input :disabled="!scope.row.新增" v-if="scope.row.id != undefined" v-model="scope.row.子任务单号" />
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip  prop="nick" :label="t('product_manage.name')" min-width="130">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick}}</div>
                    <el-input class="input-search" @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false' v-model="txtSearch" @input="onInputSearch" @click="onClickSearchPdt" v-if="scope.row.nick=='' || scope.row.nick == undefined">{{ scope.row.id }}</el-input>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.specs')" >
                <template #default="scope">
                    <el-tooltip
                        v-if="scope.row.id != undefined && scope.row.specs != ''"
                        class="box-item"
                        effect="dark"
                        :content="scope.row.specs_text"
                        placement="bottom"
                    >
                    <el-tag>{{ scope.row.specs_name=='自定义规格'?scope.row.specs_text:scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column  :label="t('oem.opt_bef_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-show="checkPermissionApi('委外订单价格显示')" v-model="scope.row.oem_price_bef_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税前单价')" type="number"/>
                        <div v-show="checkPermissionApi('委外订单价格显示') == false">*</div>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('oem.opt_aft_tax')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-show="checkPermissionApi('委外订单价格显示')"  v-model="scope.row.oem_price_aft_tax" class="!text-center" @blur="reComputePdtInfo(scope.row,'税后单价')" type="number"/>
                        <div v-show="checkPermissionApi('委外订单价格显示') == false">*</div>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('sale.tax_rate')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class1="flex">
                        <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票类型" placeholder="Select" size="small" @change="reComputePdtInfo(scope.row,'发票税率')">
                            <el-option  v-for="item in ['普票','专票','收据','不开票','电子票']" :key="item" :label="item" :value="item" />
                        </el-select>
                        <div class="flex mt-1" v-if="['普票','专票','电子票'].includes(scope.row.发票类型) ">
                            <el-select v-if="scope.row.id != undefined"  v-model="scope.row.发票税率" placeholder="Select" size="small" @change="reComputePdtInfo(scope.row,'发票税率')">
                                <el-option  v-for="item in [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]" :key="item" :label="item" :value="item" />
                            </el-select>
                            %
                        </div>      
                    </div>   
                    <div v-if="orderData.pdt_list.length>1 && scope.row.id == undefined" class=" font-bold">
                        总数合计:
                    </div>                 
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('oem.count')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex justify-center items-center flex-col">
                        <el-input v-model="scope.row.委外数量" class="!text-center !w-[100%] mr-1" @blur="reComputePdtInfo(scope.row,'委外数量')" type="number"/>
                        <div style="font-size: smaller;">{{ scope.row.base_unit }}</div>
                    </div>  
                    <div v-if="orderData.pdt_list.length>1 && scope.row.id == undefined">
                        {{ totleSale }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="备品数量"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex justify-center items-center flex-col">
                        <el-input v-model="scope.row.委外备品数量" class="!text-center !w-[100%] mr-1" @blur="reComputePdtInfo(scope.row,'委外备品数量')" type="number"/>
                        <div style="font-size: smaller;">{{ scope.row.base_unit }}</div>
                    </div>  
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip  prop="base_unit" :label="t('product_manage.b_unit')"/>
            <el-table-column  :label="t('oem.min_pack')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input  v-model="scope.row.最小包装" class="!text-center" />
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.inventory')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="!text-left font-bold" style="font-size: smaller;">
                        <div>{{'库存:'+ scope.row.库存 }} </div>
                        <div>{{'锁定:'+ scope.row.锁定 }} </div>                      
                    </div>
                    <div v-if="orderData.pdt_list.length>1 && scope.row.id == undefined" class=" font-bold">
                        价格合计:
                    </div>
                </template>
            </el-table-column>




            <el-table-column  :label="t('sale.totle_price')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-show="checkPermissionApi('委外订单价格显示')" v-model="scope.row.总价" class="!text-center" @blur="reComputePdtInfo(scope.row,'总价')" type="number"/>
                        <div v-show="checkPermissionApi('委外订单价格显示') == false">*</div>
                    </div>      
                    <div v-if="orderData.pdt_list.length>1 && scope.row.id == undefined">
                        <div v-show="checkPermissionApi('委外订单价格显示')">{{ totleSalePrice }}</div>    
                        <div v-show="checkPermissionApi('委外订单价格显示') == false">*</div>                    
                    </div>              
                </template>
            </el-table-column>
            <el-table-column  prop="delivery" :label="t('sale.delivery')"  min-width="90">
                <template #default="scope">
                    <div class="relative w-[100%]">                        
                        <el-date-picker :disabled="orderData.delivery_date != ''" class=" absolute left-0 top-0 !w-[100%]"  v-if="scope.row.id != undefined" v-model="scope.row.交货日期" type="date" placeholder="" format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD" />
                    </div>
                </template>
            </el-table-column>

            <el-table-column  :label="t('oem.mgr_man')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <div class="mr-2">{{ scope.row.负责人 }}</div> 
                        <ElButton @click="onSelMgr(scope.row)" size="small">
                            <Icon  icon="iconamoon:search-bold" />
                        </ElButton>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('purchase.source')"  width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined &&orderData.source !='' " style="text-align: left; font-size: smaller;">
                        <div v-if="orderData.source == '销售转委外'">{{ '销售单:'+scope.row.sell_order_num }}</div>
                        <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined && scope.row.oem_order_num.indexOf(',')<0">
                            <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined">{{ '委外单:'+scope.row.oem_order_num }}</div>
                            <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined">{{ '子单号:'+scope.row.父子任务单号 }}</div>
                            <div  v-if="orderData.source != ''&& orderData.source != undefined && scope.row.需求数量 != undefined"> {{ '总数量:'+scope.row.需求数量 }}</div>
                        </div>
                        <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined && scope.row.oem_order_num.indexOf(',')>0">
                            <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined">
                                {{ '委外单:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.oem_order_num"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small" >组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div v-if="orderData.source == '委外转委外' && scope.row.oem_order_num != undefined">
                                {{ '子单号:' }}
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.父子任务单号"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <div  v-if="orderData.source != ''&& orderData.source != undefined && scope.row.需求数量 != undefined">
                                 {{ '总数量:' }}
                                 <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.需求数量"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div>
                            <!-- <div  v-if="orderData.source != ''&& orderData.source != undefined && scope.row.需求数量 != undefined">
                                 {{ '标识:' }}
                                 <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="scope.row.标识"
                                    placement="right"
                                >
                                    <el-tag type="warning" effect="dark" size="small">组单</el-tag>                                
                                </el-tooltip>
                            </div> -->
                        </div>
                    </div>                    
                </template>
            </el-table-column>
            <el-table-column  :label="t('oem.remark')"  >
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.委外备注" class="!text-center"/>
                    </div>                    
                </template>
            </el-table-column>
        </el-table>
        <div v-if="showProductSel"  class=" bg-light-600 w-[90%] max-h-[400px] absolute z-99 p-1 shadow el-table-container">
            <el-table ref="searchRef"  :data="searchPdtData" style="width: 100%;" row-key="guuid" border
                highlight-current-row @current-change="handleCurrentSelectPdt"
                header-cell-class-name="tableHeader" height="300" @row-click='onRowClick'> 
                <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" width="150" />
                <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"  width="600"/>
                <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')"  />                
                <el-table-column show-overflow-tooltip fixed prop="specs_text" :label="t('product_manage.specify_info')"  />
                <el-table-column show-overflow-tooltip fixed prop="nick_brif" :label="t('product_manage.short_name')"  />
                <el-table-column show-overflow-tooltip fixed prop="note" :label="t('product_manage.help_name')"  />
            </el-table>
        </div> 
       
        




        <div class="mb-60"></div>

        <!-- 选择客户弹窗 -->
        <DialogSelParter v-model:show="showSelParterDlg" :title="t('parter.sel_parter')" @on-submit="onSelParterCallback"/>
        <!-- 选择销售 -->
        <DialogUser :param="''" v-model:show="showSelSaleUserDlg" :title="t('msg.selectUser')" @on-submit="onSelSaleCallback"/>
        <!-- 选择负责人 -->
        <DialogUser :param="''" v-model:show="showSelMgrDlg" :title="t('msg.selectUser')" @on-submit="onSelMgrCallback"/>

        <!-- 选择缺口弹窗 -->
        <DialogSelQuekou v-model:show="showSelQuekouDlg" :pdtid="lastRow.id" :title="t('oem.sel_sell')" @on-submit="onSelSellNumCallback"/>

        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="orderData.fsm_log_list" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}
.el-form-item--default{
    margin-bottom: unset;
}
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}
// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 500;
//  // white-space: nowrap;
//   text-align: center;
//   font-size: 13px;
// }
//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
  margin-bottom: 10px; 
}
//设置表单元格属性
:deep(.table_cell .cell){
    padding-left: 3px;   
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
  /* 添加你的样式 */
  text-align: center;
}
:deep(.bakinput .el-input__wrapper){
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
//下半部分表格标题
.table_self_title{
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner){
    text-align: center;
    font-size: 18px;
}

:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>