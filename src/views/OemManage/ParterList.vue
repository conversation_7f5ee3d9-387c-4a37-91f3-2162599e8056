<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElMessage,ElMessageBox,ElButton, ElTable, ElTableColumn, ElInput, ElSelect, ElOption,ElDatePicker,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getParterListApi,delParterApi } from '@/api/customer'
import { onBeforeMount } from 'vue';
import { cloneDeep } from 'lodash-es';
import { downloadFile } from '@/api/tool';
import { exportOemParterApi } from '@/api/extra';

const { push } = useRouter()
const { t } = useI18n()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  supplier_name:'',
  supplier_nick:'',
  phone: '',      //手机1
  telephone: '',    //电话
  mainer_name: '',      //负责人
  followe_name: '',    //跟单员
  address: '',     //地址
  remark: '',      //备注
  create_date: '',  //创建时间范围
  modify_date: '',  //修改时间范围
  status:'',      //状态
  linkman:'',    //联系人
  page: 1,
  count: 20
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//受托商数据源
const parterData = reactive([])

//查询受托商数据
const getParterData = async()=>{
  const ret = await getParterListApi(searchCondition)
  if(ret)
  {
    console.log(ret)
    parterData.splice(0,parterData.length,...ret.data)
    totleCount.value = parseInt( ret.count)
  }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getParterData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (tableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getParterData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getParterData()
}
//创建新受托商
const onAddCustmer = ()=>{
  push({
    path: '/oemmanage/addparter',
  })
}

//处理表格对象操作
const handleOper = (type,row) => {
  console.log(row)
  if(type==='edit' || type == 'info' ){
    push({
      path: '/oemmanage/addparter',
      query:{
        id:row.id,
      }
    })
  }
  else if(type==='del'){
    ElMessageBox.confirm(
      '确定是否删除该受托商？',
      t('msg.warn'),
      {
        confirmButtonText: t('msg.ok'),
        cancelButtonText: t('msg.channel'),
        type: 'warning',
      }
    )
      .then(async () => {

        const ret = await delParterApi({ "ids": [row.id] })
        if (ret) {
          getParterData()

          ElMessage({
            type: 'success',
            message: t('msg.delOK'),
          })
        }


      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: t('msg.delChannel'),
        })
      })
  }
}

//计算获取主要联系人
const mainConnectorInfo = (row)=>{
    if(row.corp_linkman.length>0)
    {
      //找到主要联系人
      for(let item of row.corp_linkman)
      {
        if(item.first)
        {
          return item
        }
      }
      //没找到，默认返回第一条
      return row.corp_linkman[0]
    }
    return {
      name:'',
      phone:''
    }
}


onMounted(()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //刷新表格
  getParterData()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});




const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    const ret = await exportOemParterApi(tmp)
    if (ret) {
      
      ElMessage.success('导出成功，等待下载！')
      setTimeout(() => {
        loadingExport.value = false
        downloadFile(ret.data.download, ret.data.filename)
      }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
  }, 10000)

}
</script>

<template>
  <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div  class="p-7 pt-0">
      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-7 pr-5 pl-5 pb-7 mb-5 bg-white">

        <div class="absolute top-8 left-15">
          <ElButton type="success" @click="onAddCustmer">
            <Icon icon="fluent-mdl2:people-add" />
            <div class="pl-2">{{ t('button.add') }}</div>
          </ElButton>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
            <Icon icon="carbon:export" />
            <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>

        <div class="text-center mb-10 font-bold">{{ t('oem.parter') }}</div>
        <div class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('parter.name') }}</div>
          <el-input v-model="searchCondition.parter_name" placeholder="" class="searchItem"/>
        </div>
        <div class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('parter.nick') }}</div>
          <el-input v-model="searchCondition.parter_nick" placeholder="" class="searchItem"/>
        </div>
        <div class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.connector_phone') }}</div>
          <el-input v-model="searchCondition.phone" placeholder="" class="searchItem"/>
        </div>
        <div   class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.mainer') }}</div>
          <el-input v-model="searchCondition.mainer_name" placeholder=""  class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.status') }}</div>
          <el-select class="" v-model="searchCondition.status" placeholder="请选择" >
            <el-option v-for="item in ['正常','禁用']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.address') }}</div>
          <el-input v-model="searchCondition.address" placeholder="" class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.remark') }}</div>
          <el-input v-model="searchCondition.remark" placeholder="" class="searchItem"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.createdate') }}</div>
          <el-date-picker v-model="searchCondition.create_date" type="daterange" range-separator="To" start-placeholder="开始"
            end-placeholder="结束" value-format="YYYY-MM-DD"/>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-8 mb-2">
          <div class="searchTitle">{{ t('customer.modifydate') }}</div>
          <el-date-picker v-model="searchCondition.modify_date" type="daterange" range-separator="To" start-placeholder="开始"
            end-placeholder="结束" value-format="YYYY-MM-DD"/>
        </div>
        <div  class="flex justify-end items-center mr-6 mt-6 mb-2">
          <el-checkbox :label="t('customer.senior')" v-model="senior" size="small" />
          <ElButton class="ml-5" type="primary" @click="onSearch">
            <Icon icon="ri:phone-find-line" />
            <div class="pl-2">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>
 
      <el-table ref="tableRef" header-cell-class-name="tableHeader" :data="parterData" style="width: 100%"
        :height="tableHeight" border stripe>        
        <el-table-column show-overflow-tooltip  prop="parter_name" :label="t('parter.name')" width="150" />
        <el-table-column show-overflow-tooltip  prop="parter_nick" :label="t('parter.nick')" width="200">
          <template #default="scope">
            <div class="nameStyle" @click="handleOper('info', scope.row)">{{ scope.row.parter_nick }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip  prop="linkman" :label="t('supplier.contact')" width="120" >
          <template #default="scope">
            <div>{{ mainConnectorInfo(scope.row).name }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip  prop="linkman" :label="t('supplier.phone')" width="120" >
          <template #default="scope">
            <div>{{ mainConnectorInfo(scope.row).phone }}</div>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="mainer_name" :label="t('supplier.manager')" width="90" />
        <el-table-column show-overflow-tooltip prop="status" :label="t('supplier.status')" width="80" />
        <el-table-column show-overflow-tooltip prop="corp_addr" :label="t('supplier.address')" width="100" />
        <el-table-column show-overflow-tooltip prop="pay_type" :label="t('parter.pay_type')" width="100" />
        <el-table-column show-overflow-tooltip prop="tax_rate" :label="t('parter.def_tax')" width="100" />
        <el-table-column show-overflow-tooltip prop="remark" :label="t('supplier.remark')"  min-width="200"/>
        <el-table-column fixed="right" :label="t('userTable.operate')" width="90">
          <template #default="scope">
            <el-dropdown trigger="click" placement="left">
              <span class="el-dropdown-link">
                <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleOper('info', scope.row)">{{ t('userOpt.detail') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('edit', scope.row)">{{ t('userOpt.edit') }}</el-dropdown-item>
                  <el-dropdown-item @click="handleOper('del', scope.row)">{{ t('userOpt.del') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="flex justify-end mt-4 mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[20, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }
.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
.searchItem{
  width: 200px;
}
</style>
