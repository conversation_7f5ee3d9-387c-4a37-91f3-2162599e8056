<script setup lang="ts">
import { ElButton, ElOption, ElSelect, ElMessageBox, ElMessage, ElDialog, ElInput, ElCheckbox, ElRadio, ElRadioGroup, ElNotification } from 'element-plus'
import * as printUtils from "@/utils/printUtils.js";
import { HzxJson } from "./common.js";
import { reactive, onBeforeUnmount, onMounted, shallowRef, ref, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import { getPrintTmpListApi,getPrintTmpInfoApi,delPrintTmpApi, updatePrintTmpApi } from '@/api/print'

import Vue3KindEditor from '@teihin/vue3-kind-editor'
import DialogPrintTmpSel from './DialogPrintTmpSel.vue'
import { zip,unzip,toUperNumber, downloadFile } from '@/api/tool';
import { getBuyerInfoApi,getParterInfoA<PERSON>,getSupplierInfo<PERSON><PERSON> } from "@/api/customer";
import * as cheerio from 'cheerio';
import { getHashApi, setHashApi } from '@/api/extra';

//批量打印数组
const nPrintMode = ref('组合打印')
const printArray = reactive([])
//传入的打印信息
const printInfo = reactive({
        printType:''
    })

let strPageSizeList = reactive([])    //打印机列表
let LODOP
const _this: any = reactive({
    modifyText: '修改内容',
    changeCss: 'hidden-box',

    nPageWidth: 210,
    nPageHeight: 297,
    nPaddingTop: 5,
    nPaddingBottom: 5,
    nPaddingLeft: 5,
    nPaddingRight: 5,
    nTopHeight: 0,
    nFootHeight: 0,
    nLeftWidth: 0,
    nRightWidth: 0,
    nMiddleHeight: 287,
    nMiddleWidth: 200,
    nFollowHeight: 0,

    sLodopMode: "Cloud",//打印模式
    printList: [],//打印机列表
    sPrintName: { label: "", value: '' },//打印机名称和val
    strPageSizeList: [],//纸张列表
    sPageType: " ",//纸类型
    nDirection: 1,
    pageDirection: 1,//纸方向
    offset_x: 0,
    offset_y: 0,
    nWidth: 210,
    nHeight: 297,
    sPrintTitle: '标题',
    sIdList: "00008532:销售单唐山启奥科技股份有限公司_00008532:1",//打印单据的第一个编号

    sTitleRandom: "",//随机数标题
    sTaskCmd: "",
    sBatchPrintFlag: "",
    sBodyType: "html",

    printTitle:'',  //模板标题
    printContent:'' //打印模板内容   zip base64
})

//模板列表
const tmpArray = reactive([])
//打印机列表
const printerList = reactive([])

//供应商信息
const supplierInfo = reactive({

})
//客户信息
const customerInfo = reactive({
    
})
//委外商信息
const oemInfo = reactive({
    
})

//当前选中模板
const curSelTmpID = ref('')
//调整选择模板
const onChangeSelTmp = async(value)=>{
    await getTmpInfo()
}


const updateTitleRandom = () => {
    _this.sTitleRandom = printUtils.randomNumber(6);
}
//获取标题
const getTitle = () => {
    var sTitleTemp = _this.sPrintTitle;
    if (_this.sPrintTitle != "") {
        sTitleTemp += "_";
    }
    sTitleTemp += _this.sTitleRandom;
    return sTitleTemp;
}
//打印机列表
function createPrintList() {
    _this.printList = []
    //控件和云打印模式加载打印机
    if (_this.sLodopMode == "Internet") {

    } else {
        let nPrintCount = LODOP.GET_PRINTER_COUNT();//获取打印机的个数
        printerList.splice(0,printerList.length)
        for (let i = 0; i <= nPrintCount - 1; i++) {
            let strDName = LODOP.GET_PRINTER_NAME(i);
            printerList.push({ value: i, label: strDName })
        }
    }
}
//打印机更换
function printChange() {
    createPagSizeList();
    HzxPrintView("");
}
//加载纸张列表
function createPagSizeList() {
    let tmp = ''
    if (_this.sLodopMode == "Internet") {
        return
    } else {
        var sPrintId = _this.sPrintName.value;
        tmp = LODOP.GET_PAGESIZES_LIST(sPrintId, "\n");
    }
    strPageSizeList.splice(0, strPageSizeList.length, ...(tmp.split('\n')))
    console.log('打印机',strPageSizeList)
}
//初始化
const printInit = () => {
    if (_this.sLodopMode == "ActiveX") {
        // LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
    } else if (_this.sLodopMode == "Cloud" || _this.sLodopMode == "NoControl") {
        LODOP = window.getLodop();
    }
    if(LODOP == undefined)
    {
        bNoCtl.value = true
    }
    else{
        bNoCtl.value = false
    }
    //获取模板
    getPrintTmpList()
    //获取目标信息
    getTargetInfo()

    LODOP.PRINT_INIT(getTitle());

    //获取打印机列表
    createPrintList();
    //加载纸张类型
    createPagSizeList();
    //纸张方向


    if (_this.sPdfFlag == "pdf" && _this.sPrintType == "more") {
        setTimeout("BatchPrint()", 1000);
    }
}

//查询模板列表
const getPrintTmpList = async(page = 1)=>{
  const ret = await getPrintTmpListApi({
    page,
    count:100,
    type:printInfo.printType
  })
  if(ret)
  {
    tmpArray.splice(0,tmpArray.length, ...(ret.data.filter(item=>item.enable == '1')))
    for(let item of tmpArray)
    {
        if(item.preset == '1')
        {
            curSelTmpID.value = item.id
            getTmpInfo()
        }
    }
  }
}

const getTmpInfo = async(bPreview = true,bModifyPrinter = true)=>{
    //获取当前模板内容并设置到界面
    const ret = await getPrintTmpInfoApi({
        id:curSelTmpID.value
    })
    if(ret)
    {
        let tmpPrinter = _this.sPrintName
        let tmpPaper = _this.sPageType

        Object.assign(_this,JSON.parse(unzip(ret.data.content)))
        console.log(_this)
        if (!bModifyPrinter)
        {
            _this.sPrintName = tmpPrinter
            _this.sPageType = tmpPaper
        }

        if (nPrintMode.value == '组合打印'&& printArray.length>1)
        {
            //组合打印把所有单子的pdt整合到一起            
            Object.assign(printInfo,JSON.parse(JSON.stringify(printArray[0])))

            printInfo.pdt_list.splice(0,printInfo.pdt_list.length)
            for (let i = 0; i < printArray.length; i++)
            {
                printArray[i].pdt_list.forEach(item => {
                    item.委外单号 = printArray[i].oem_order_num
                    if (printInfo.printType == '采购收货单') {
                        item.收货单号 = printArray[i].buy_drawin_num
                    }
                    else if (printInfo.printType == '委外收货单') {
                        item.收货单号 = printArray[i].oem_drawin_num
                    }
                    else if (printInfo.printType == '委外发料单') {
                        item.出库编号 = printArray[i].oem_takeout_num
                        item.发料日期 = printArray[i].create_date
                    }
                    else if(printInfo.printType == '采购入库'){
                        item.入库日期 = printArray[i].putin_date.split(' ')[0]
                    }
                    else if(printInfo.printType == '委外入库单'){
                        item.入库日期 = printArray[i].create_date.split(' ')[0]
                    }
                    item.采购单号 = printArray[i].buy_order_num
                    item.委外单号 = printArray[i].oem_order_num
                })
                // if (i == 0)
                //     continue
                console.log('===',printArray[i].pdt_list)
                printInfo.pdt_list.push(...printArray[i].pdt_list)
            }
            printInfo.buy_drawin_num = '批量'
            printInfo.oem_drawin_num = '批量'
            printInfo.oem_putin_num = '批量'
            printInfo.buy_putin_num = '批量'
            printInfo.buy_order_num = '批量'
            printInfo.oem_order_num = '批量'
            printInfo.sell_order_num = '批量'
            printInfo.buy_cancel_num = '批量'
            printInfo.oem_takeout_num = '批量'
            printInfo.putin_date = '批量'
            
            
            _this.printContent = replaceParam(_this.printContent)
        }
        else {
            Object.assign(printInfo,JSON.parse(JSON.stringify(printArray[0])))
            //替换打印变量
            _this.printContent = replaceParam(_this.printContent)
        }

        // for (let item of printInfo.pdt_list)
        // {
        //     console.log('jieguo',item.委外单号,item.收货单号,item.采购单号)
        // }
        // console.log('个数:',printInfo.pdt_list.length)
        
        HzxPrintView('',bPreview);
    }
}

const replaceParam = (input) => {
    
    const html = cheerio.load(input);
    if(printInfo.printType == '采购订单' || printInfo.printType == '销售订单')
    {
        html('td:contains("{$:产品编码}")').each((index, element) => {
            replaceLine(html,index,element)
        });
        html('td:contains("{$:产品名称}")').each((index, element) => {
            replaceLine(html,index,element)
        });
        html('td:contains("{图片空间")').each((index, element) => {
            const arSpan = html(element).find('span')
            for(let span of arSpan)
            {
                html(span).remove()
            }
            //增加图片
            let pics = getImageURLs(printInfo.file_list)
            console.log('-图片空间',pics)
            const add = html(element).append('<div style="text-align:center;display:flex;flex-wrap:wrap;"></div>')
            for(let pic of pics)
            {
                add.append('<img src="'+pic+'" style="width:100px;height:100px;margin:5px">')
            }

        });
    }
    else if(printInfo.printType === '采购收货单' || printInfo.printType === '委外收货单')
    {
        html('td:contains("{$:序号}")').each((index, element) => {
            replaceLine(html,index,element)
        });
    }
    else if(printInfo.printType === '销售出库单')
    {
        html('td:contains("{$:序号}")').each((index, element) => {
            replaceLine(html,index,element)
        });
    }
    else if (printInfo.printType === '委外单')
    {
        html('td:contains("{$:关联单号}")').each((index, element) => {
            replaceLine(html,index,element)
        });
    }
    else if( printInfo.printType === 'BOM完整结构图')
    {
        html('td:contains("{$:序号}")').each((index, element) => {
            replaceLineBomFull(html,index,element)
        });
    }
    else    
    {
        html('td:contains("{$:序号}")').each((index, element) => {
            replaceLine(html,index,element)
        });
    }
    
    input = html.html()











    input = input.replace(/{#:编号}/g, getOrderNum())
    input = input.replace(/{#:备注}/g, printInfo.note)
    input = input.replace(/{金额大写:{#:金额}}/g, toUperNumber(printInfo.合计金额))
    input = input.replace(/{##:填充行}/g, '')
    if(printInfo.pdt_list != undefined)
        input = input.replace(/{##:总行数}/g, printInfo.pdt_list.length)
    input = input.replace(/{公司:公司名称}/g, '东莞市文博工艺品有限公司')
    input = input.replace(/{公司:公司地址}/g, '')
    input = input.replace(/{公司:联系人电话}/g, '')
    input = input.replace(/{公司:公司邮箱}/g, ':<EMAIL>')
    if(printInfo.printType == '销售订单')
    {
        input = input.replace(/{#:销售日期}/g, printInfo.business_date)
    }
    else if(printInfo.printType == '采购订单')
    {
        input = input.replace(/{#:供应商}/g, printInfo.supplier_nick)
        input = input.replace(/{供应商:联系人手机}/g, supplierInfo.corp_linkman.length>0?([supplierInfo.corp_linkman[0].phone,supplierInfo.corp_linkman[0].telephone].find(value => value !== '') || ''):'--' )
        input = input.replace(/{供应商:联系人}/g, supplierInfo.corp_linkman.length>0?supplierInfo.corp_linkman[0].name:'--')
        input = input.replace(/{供应商:地址}/g, supplierInfo.corp_addr)
        input = input.replace(/{#:采购日期}/g, printInfo.business_date)        
        input = input.replace(/{#:采购人员}/g, printInfo.buy_man_name)
        input = input.replace(/{供应商:开票税率}/g, supplierInfo.tax_rate)
        input = input.replace(/{供应商:通信地址}/g, supplierInfo.corp_addr)
        input = input.replace(/{#:交货日期}/g, printInfo.pdt_list[0].交货日期)
        input = input.replace(/{#:付款方式}/g, printInfo.pay_type)
        input = input.replace(/{#:金额}/g, printInfo.合计金额.toFixed(2))
        input = input.replace(/{#:供应商支付方式}/g, supplierInfo.pay_type)
    }
    else if(printInfo.printType === '采购收货单')
    {
        input = input.replace(/{#:供应商}/g, printInfo.supplier_nick)
        input = input.replace(/{#:供应商名称}/g, printInfo.supplier_nick)
        input = input.replace(/{#:采购单号}/g, printInfo.buy_order_num)
        input = input.replace(/{#:收货日期}/g, printInfo.drawin_date)
        //input = input.replace(/{#:收货编号}/g, printInfo.buy_drawin_num)
        input = input.replace(/{#:收货备注}/g, printInfo.note)
        input = input.replace(/{#:采购人员}/g, printInfo.buy_man_name)
        input = input.replace(/{#:收货人员}/g, printInfo.drawin_man_name)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick.良品库)
    }
    else if(printInfo.printType === '委外收货单')
    {
        input = input.replace(/{委托:委托商名称}/g, printInfo.parter_nick)
        input = input.replace(/{#:收货单号}/g, printInfo.oem_drawin_num)
        input = input.replace(/{#:收货日期}/g, printInfo.drawin_date)
        input = input.replace(/{#:申请人员名字}/g, printInfo.drawin_man_name)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick.良品库)
    }
    else if(printInfo.printType === '销售出库单')
    {
        input = input.replace(/{#:出库编号}/g, printInfo.sell_takeout_num)
        input = input.replace(/{#:出库日期}/g, printInfo.takeout_date)
        input = input.replace(/{#:收货人}/g, printInfo.buyer_nick)
        input = input.replace(/{#:收货人手机}/g, '')
        input = input.replace(/{#:送货地址}/g, printInfo.express_data.收货地址)

        input = input.replace(/{#:合计出货数量}/g, printInfo.pdt_list.reduce((sum, item) => sum + (item.发货数量 || 0), 0))
        input = input.replace(/{#:出库备注}/g, printInfo.note)
    }
    else if(printInfo.printType === '委外单')
    {
        input = input.replace(/{#:委托组编号}/g, printInfo.oem_order_num)
        input = input.replace(/{#:受托方名称}/g, printInfo.parter_nick)
        input = input.replace(/{委托:手机}/g, oemInfo.corp_linkman.length>0?([oemInfo.corp_linkman[0].phone,oemInfo.corp_linkman[0].telephone].find(value => value!== '') || ''):'--' )
        input = input.replace(/{委托:联系人}/g, oemInfo.corp_linkman.length>0?oemInfo.corp_linkman[0].name:'--')
        input = input.replace(/{委托:地址}/g, oemInfo.corp_addr)
        input = input.replace(/{#:创建日期}/g, printInfo.create_date.split(' ')[0])
        input = input.replace(/{#:创建人员名字}/g, printInfo.oem_man_name)
        input = input.replace(/{#:付款方式}/g, printInfo.pay_type)
        input = input.replace(/{#:受托商支付方式}/g, oemInfo.pay_type)
        //计算加工费总价
        let nTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += item.oem_price_aft_tax*item.委外数量
        }
        input = input.replace(/{#:加工费总价}/g, parseFloat(nTotle.toFixed(2)))
    }
    else if (printInfo.printType === '委外发料单')
    {
        input = input.replace(/{#:出库编号}/g, printInfo.oem_takeout_num)
        input = input.replace(/{委托:委托商名称}/g, printInfo.parter_nick)
        input = input.replace(/{#:委外日期}/g, printInfo.modify_date)
        input = input.replace(/{委托:委托商名称}/g, printInfo.parter_nick)
        input = input.replace(/{#:委外编号}/g, printInfo.oem_order_num)
        

        let nTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += Number(item.发料数量)
        }
        input = input.replace(/{#:总数量}/g, nTotle)
    }
    else if(printInfo.printType === '采购入库')
    {
        input = input.replace(/{#:入库单编号}/g, printInfo.buy_putin_num)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick)
        input = input.replace(/{#:采购订单编号}/g, printInfo.buy_order_num)
        input = input.replace(/{#:供应商}/g, printInfo.supplier_nick)
        input = input.replace(/{#:入库日期}/g, printInfo.putin_date)
        input = input.replace(/{#:入库备注}/g, printInfo.note)
        input = input.replace(/{#:采购人员}/g, printInfo.buy_man_name)
        input = input.replace(/{#:入库人员}/g, printInfo.putin_man_name)
        input = input.replace(/{#:收货人员}/g, printInfo.drawin_man_name)

        let nTotle = 0
        let nPriceTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += item.入库数量
            nPriceTotle += item.总价
        }
        input = input.replace(/{#:总数量}/g, nTotle)
        input = input.replace(/{#:总金额}/g, parseFloat(nPriceTotle.toFixed(2)))
    }
    else if(printInfo.printType === '委外入库单')
    {
        input = input.replace(/{#:入库单编号}/g, printInfo.oem_putin_num)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick)
        input = input.replace(/{#:委外订单编号}/g, printInfo.oem_order_num)
        input = input.replace(/{#:受托商}/g, printInfo.parter_nick)
        input = input.replace(/{#:入库日期}/g, printInfo.putin_date==''?printInfo.create_date:printInfo.putin_date)
        input = input.replace(/{#:入库备注}/g, printInfo.note)
        input = input.replace(/{#:委外人员}/g, printInfo.oem_man_name)
        input = input.replace(/{#:入库人员}/g, printInfo.putin_man_name)
        input = input.replace(/{#:收货人员}/g, printInfo.drawin_man_name)
        
        let nTotle = 0
        let nPriceTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += item.入库数量
            nPriceTotle += item.总价
        }
        input = input.replace(/{#:总数量}/g, nTotle)
        input = input.replace(/{#:总金额}/g, parseFloat(nPriceTotle.toFixed(2)))
    }
    else if(printInfo.printType === '领料单')
    {
        input = input.replace(/{#:领料单编号}/g, printInfo.book_takein_num)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick)
        input = input.replace(/{#:申请日期}/g, printInfo.takein_date == '' ? printInfo.create_date : printInfo.takein_date)
        input = input.replace(/{#:申请人员}/g, printInfo.takein_man_name)
        let nTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += item.领料数量
        }
        input = input.replace(/{#:总数量}/g, nTotle)
    }
    else if (printInfo.printType === "付款申请单")
    {
        input = input.replace(/{#:申请单号}/g, printInfo.book_payment_num)
        input = input.replace(/{#:申请部门}/g, printInfo.payment_man_depts_name[0])
        input = input.replace(/{#:收款单位名称}/g, printInfo.payee_nick)
        input = input.replace(/{#:收款账号}/g, printInfo.corp_account)
        input = input.replace(/{#:开户行}/g, printInfo.corp_bank)
        input = input.replace(/{金额大写:{#:付款金额}}/g, toUperNumber(printInfo.money_amount))
        input = input.replace(/{#:付款金额}/g, printInfo.money_amount)
        input = input.replace(/{#:款项用途}/g, printInfo.money_use)
        input = input.replace(/{#:制单}/g, printInfo.payment_man_name)
        input = input.replace(/{#:申请日期}/g, printInfo.payment_date)
        input = input.replace(/{#:领导}/g, '')
        input = input.replace(/{#:会计}/g, '')
        input = input.replace(/{#:出纳}/g, '')
    }
    else if(printInfo.printType === '采购退货出库单')
    {
        input = input.replace(/{#:出库单编号}/g, printInfo.buy_cancel_num)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick)
        input = input.replace(/{#:采购订单编号}/g, printInfo.buy_order_num)
        input = input.replace(/{#:供应商}/g, printInfo.supplier_nick)
        input = input.replace(/{#:出库日期}/g, printInfo.create_date)
        input = input.replace(/{#:出库备注}/g, printInfo.note)
        input = input.replace(/{#:采购人员}/g, printInfo.order_man_name)
        input = input.replace(/{#:出库人员}/g, printInfo.cancel_man_name)

        let nTotle = 0
        let nPriceTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += parseFloat(item.退货数量)
            nPriceTotle += item.总价
        }
        input = input.replace(/{#:总数量}/g, nTotle)
        input = input.replace(/{#:总金额}/g, parseFloat(nPriceTotle.toFixed(2)))
    }
    else if(printInfo.printType === '委外退货单')
    {
        input = input.replace(/{#:出库单编号}/g, printInfo.oem_cancel_num)
        input = input.replace(/{#:仓库名称}/g, printInfo.store_nick)
        input = input.replace(/{#:委外单号}/g, printInfo.oem_order_num)
        input = input.replace(/{#:受托商}/g, printInfo.parter_nick)
        input = input.replace(/{#:出库日期}/g, printInfo.create_date)
        input = input.replace(/{#:出库备注}/g, printInfo.note)
        input = input.replace(/{#:委外人员}/g, printInfo.order_man_name)
        input = input.replace(/{#:出库人员}/g, printInfo.cancel_man_name)

        let nTotle = 0
        let nPriceTotle = 0
        for (let item of printInfo.pdt_list)
        {
            nTotle += parseFloat(item.退货数量)
            nPriceTotle += item.总价
        }
        input = input.replace(/{#:总数量}/g, nTotle)
        input = input.replace(/{#:总金额}/g, parseFloat(nPriceTotle.toFixed(2)))
    }
    else if(printInfo.printType === 'BOM完整结构图')
    {
        input = input.replace(/{#:产品编码}/g, printInfo.pdt_data.pdt_name)
        input = input.replace(/{#:产品名称}/g, printInfo.pdt_data.pdt_nick)
    }
    


    return input
}
// 检查 URL 是否是图片
function isImageURL(url) {
    return /\.(png|jpg|jpeg|gif)$/i.test(url);
}

// 从对象数组中提取所有图片 URL
function getImageURLs(arr) {
    return arr.filter(item => isImageURL(item.url)).map(item => item.url);
}

const replaceLine = (html, index, element) => {
    if (printInfo.pdt_list == undefined)
        return
 // 找到父元素的父元素，即<tr>行
    const parentTr = html(element).parent();
    let msg ='<tr>'+parentTr.html()+'</tr>'
    //循环替换内容
    for(let i = printInfo.pdt_list.length - 1; i >= 0; i--)
    {
        let one = msg
        one = one.replace(/\{\$:序号\}/g, (i+1).toString())
        one = one.replace(/\{\$:产品名称\}/g, printInfo.pdt_list[i].nick)
        one = one.replace(/\{\$:产品编码\}/g, printInfo.pdt_list[i].name)
        one = one.replace(/\{\$:产品编号\}/g, printInfo.pdt_list[i].name)
        one = one.replace(/\{\$:规格\}/g, printInfo.pdt_list[i].specs)
        one = one.replace(/\{\$:规格显示过滤\}/g, printInfo.pdt_list[i].specs)

        one = one.replace(/\{\$:采购数量\}/g, printInfo.pdt_list[i].采购数量)
        one = one.replace(/\{\$:采购备品数量\}/g, printInfo.pdt_list[i].采购备品数量)

        one = one.replace(/\{\$:税率\}/g, printInfo.pdt_list[i].发票税率+'%')
        
        one = one.replace(/\{\$:单位\}/g, printInfo.pdt_list[i].base_unit)
        if(printInfo.printType == '采购订单')
        {
            one = one.replace(/\{\$:数量\}/g, parseFloat(printInfo.pdt_list[i].采购数量) + parseFloat(printInfo.pdt_list[i].采购备品数量))
            // one = one.replace(/\{\{$:单价\}:2\}/g, printInfo.pdt_list[i].buy_price_aft_tax.toFixed(2))
            one = one.replace(/\{\[\{\$:\单价\}:2\]\}/g, printInfo.pdt_list[i].buy_price_aft_tax.toFixed(2))
            one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].buy_price_aft_tax)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)
            one = one.replace(/\{\$:需求总量\}/g, printInfo.pdt_list[i].需求总量)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].总价)
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].总价)
        }
        else if(printInfo.printType == '销售订单')
        {
            one = one.replace(/\{\$:数量\}/g, parseFloat(printInfo.pdt_list[i].销售数量)+parseFloat(printInfo.pdt_list[i].销售备品数量))
            one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].sell_price_aft_tax)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].总价)
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].总价)
        }
        else if(printInfo.printType == '采购收货单' || printInfo.printType == '委外收货单')
        {
            one = one.replace(/\{\$:累计收货数量\}/g, printInfo.pdt_list[i].已收货)
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].收货数量)
            one = one.replace(/\{\#:收货备注\}/g, printInfo.pdt_list[i].收货备注)
            one = one.replace(/\{\$:重量\}/g, printInfo.pdt_list[i].重量 == undefined?'':printInfo.pdt_list[i].重量)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)

            if (printInfo.printType == '采购收货单') {
                one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].收货数量))
                one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].buy_price_aft_tax * parseFloat(printInfo.pdt_list[i].收货数量))
                printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].收货数量)
            }
            else if (printInfo.printType == '委外收货单') {
                one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].收货数量))
                one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].收货数量))
                printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].收货数量)
            }

            if (nPrintMode.value == '组合打印'&&printArray.length>1)
            {
                one = one.replace(/\{\$:委外编号\}/g, printInfo.pdt_list[i].委外单号)
                one = one.replace(/\{\$:收货编号\}/g, printInfo.pdt_list[i].收货单号)
                one = one.replace(/\{\$:采购单号\}/g, printInfo.pdt_list[i].采购单号)
            }
            else
            {
                one = one.replace(/\{\$:委外编号\}/g, printInfo.oem_order_num)
                one = one.replace(/\{\$:收货编号\}/g,printInfo.printType == '采购收货单'? printInfo.buy_drawin_num: printInfo.oem_drawin_num)
                one = one.replace(/\{\$:采购单号\}/g, printInfo.buy_order_num)
            }
        }
        else if(printInfo.printType == '销售出库单')
        {
            one = one.replace(/\{\$:销售单编号\}/g, printInfo.sell_order_num)
            one = one.replace(/\{\$:主计量数量\}/g, printInfo.pdt_list[i].发货数量)
            one = one.replace(/\{\$:主计量单位\}/g, printInfo.pdt_list[i].base_unit)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].sell_price_aft_tax* parseFloat(printInfo.pdt_list[i].发货数量))
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].sell_price_aft_tax * parseFloat(printInfo.pdt_list[i].发货数量))
            printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].sell_price_aft_tax* parseFloat(printInfo.pdt_list[i].发货数量)
        }
        else if(printInfo.printType === '委外单')
        {
            one = one.replace(/\{\$:关联单号\}/g, printInfo.pdt_list[i].子任务单号)
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].委外数量)
            one = one.replace(/\{\$:备品\}/g, printInfo.pdt_list[i].委外备品数量)
            one = one.replace(/\{\$:加工费单价\}/g, printInfo.pdt_list[i].oem_price_aft_tax)
            one = one.replace(/\{\$:加工费总价\}/g, parseFloat(printInfo.pdt_list[i].总价.toFixed(2)))
            one = one.replace(/\{\$:交货时间\}/g, printInfo.pdt_list[i].交货日期)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].委外备注)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].总价)
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].总价)
        }
        else if (printInfo.printType === '委外发料单')
        {
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].remark)
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].发料数量)
            one = one.replace(/\{\$:计划数量\}/g, printInfo.pdt_list[i].所需数量)

            one = one.replace(/\{\$:委外单号\}/g, printInfo.pdt_list[i].委外单号 == undefined ? printInfo.oem_order_num : printInfo.pdt_list[i].委外单号)
            one = one.replace(/\{\$:出库编号\}/g, printInfo.pdt_list[i].出库编号 == undefined ? printInfo.oem_takeout_num : printInfo.pdt_list[i].出库编号)
            one = one.replace(/\{\$:发料日期\}/g, (printInfo.pdt_list[i].发料日期 == undefined ? printInfo.create_date : printInfo.pdt_list[i].发料日期).split(' ')[0])
            
            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].发料数量))
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].发料数量))
            printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].发料数量)
        }
        else if (printInfo.printType === '采购入库'||printInfo.printType === '委外入库单')
        {
            
            one = one.replace(/\{\$:采购订单编号\}/g, printInfo.pdt_list[i].采购单号 == undefined ? printInfo.buy_order_num : printInfo.pdt_list[i].采购单号)
            one = one.replace(/\{\$:委外订单编号\}/g, printInfo.pdt_list[i].委外单号 == undefined?printInfo.oem_order_num:printInfo.pdt_list[i].委外单号)
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].入库数量)
            
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)

            

            if (printInfo.printType == '采购入库') {
                one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].buy_price_aft_tax)
                one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].入库数量))
                one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].buy_price_aft_tax * parseFloat(printInfo.pdt_list[i].入库数量))
                printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].入库数量)
                one = one.replace(/\{\$:入库日期\}/g, printInfo.pdt_list[i].入库日期 == undefined? printInfo.putin_date.split(' ')[0] : printInfo.pdt_list[i].入库日期)
            }
            else if (printInfo.printType == '委外入库单') {
                one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].oem_price_aft_tax)
                one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].入库数量))
                one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].oem_price_aft_tax * parseFloat(printInfo.pdt_list[i].入库数量))
                printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].入库数量)
                one = one.replace(/\{\$:入库日期\}/g, printInfo.pdt_list[i].入库日期 == undefined? printInfo.create_date.split(' ')[0] : printInfo.pdt_list[i].入库日期)
            }
        }
        else if(printInfo.printType === '领料单')
        {
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].领料数量)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].出库备注)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].领料数量))
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].领料数量))
            printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].领料数量)
        }
        else if (printInfo.printType === '采购退货出库单')
        {
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].退货数量)
            one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].buy_price_aft_tax)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)
            one = one.replace(/\{\$:采购单号\}/g, printInfo.pdt_list[i].采购单号 == undefined ? printInfo.buy_order_num : printInfo.pdt_list[i].采购单号)
            one = one.replace(/\{\$:委外单号\}/g, printInfo.pdt_list[i].委外单号 == undefined ? printInfo.oem_order_num : printInfo.pdt_list[i].委外单号)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].退货数量))
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].buy_price_aft_tax * parseFloat(printInfo.pdt_list[i].退货数量))
            printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].buy_price_aft_tax* parseFloat(printInfo.pdt_list[i].退货数量)
        }
        else if (printInfo.printType === '委外退货单')
        {
            one = one.replace(/\{\$:数量\}/g, printInfo.pdt_list[i].退货数量)
            one = one.replace(/\{\$:单价\}/g, printInfo.pdt_list[i].buy_price_aft_tax)
            one = one.replace(/\{\$:备注\}/g, printInfo.pdt_list[i].note)         
            one = one.replace(/\{\$:委外单号\}/g, printInfo.pdt_list[i].委外单号 == undefined ? printInfo.oem_order_num : printInfo.pdt_list[i].委外单号)

            one = one.replace(/\{\[\{\$:\金额\}:2\]\}/g, printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].退货数量))
            one = one.replace(/\{\$:金额\}/g, printInfo.pdt_list[i].oem_price_aft_tax * parseFloat(printInfo.pdt_list[i].退货数量))
            printInfo.pdt_list[i].总价 = printInfo.pdt_list[i].oem_price_aft_tax* parseFloat(printInfo.pdt_list[i].退货数量)
        }

        let pics = getImageURLs(printInfo.pdt_list[i].pics)
        if(pics.length>0)
        {
            one = one.replace(/\{图片:\{产品:[^{}]+\}(?:[^{}]*\}|$)/, '<img src="' + pics[0] + '" style="width:50px;height:50px;">');
            one = one.replace(/\{\$:产品图片\}/g, '<img src="' + pics[0] + '" style="width:50px;height:50px;">')

        }
        else
        {
            one = one.replace(/\{图片:\{产品:[^{}]+\}(?:[^{}]*\}|$)/, '');
            one = one.replace(/\{\$:产品图片\}/g, '')

        }
        
         parentTr.after(one)
    }
    parentTr.remove();
}


//替换BOM结构行
const replaceLineBomFull = (html, index, element) => {
    if (printInfo.sub_bom == undefined)
        return
 // 找到父元素的父元素，即<tr>行
    const parentTr = html(element).parent();
    let msg ='<tr>'+parentTr.html()+'</tr>'
    pushBomItem([printInfo],parentTr, msg)
    // //循环替换内容
    // for(let i = printInfo.sub_bom.length - 1; i >= 0; i--)
    // {
    //     let one = msg
    //     one = one.replace(/\{\$:序号\}/g, printInfo.pdt_level)
    //     one = one.replace(/\{\$:物料名称\}/g, printInfo.pdt_data.nick)
    //     one = one.replace(/\{\$:物料编码\}/g, printInfo.pdt_data.name)
    //     one = one.replace(/\{\$:用量\}/g, printInfo.pdt_data.用量)
    //     one = one.replace(/\{\$:规格\}/g, printInfo.pdt_data.specs_text)
    //     one = one.replace(/\{\$:备注\}/g, printInfo.pdt_data.remark)
        
        
    //      parentTr.after(one)
    // }
    parentTr.remove();
}

const pushBomItem = (sub_bom,parentTr, msg) => {
    //循环替换内容
    for(let i = sub_bom.length - 1; i >= 0; i--)
    {
        let one = msg
        one = one.replace(/\{\$:序号\}/g, sub_bom[i].pdt_level)
        one = one.replace(/\{\$:物料名称\}/g, sub_bom[i].pdt_data.nick)
        one = one.replace(/\{\$:物料编码\}/g, sub_bom[i].pdt_data.name)
        one = one.replace(/\{\$:用量\}/g, sub_bom[i].pdt_data.用量== undefined ? 1 : sub_bom[i].pdt_data.用量)
        one = one.replace(/\{\$:规格\}/g, sub_bom[i].pdt_data.specs_text)
        one = one.replace(/\{\$:单位\}/g, sub_bom[i].pdt_data.base_unit || '')
        one = one.replace(/\{\$:备注\}/g, sub_bom[i].pdt_data.remark)

        pushBomItem(sub_bom[i].sub_bom,parentTr, msg)
         parentTr.after(one)
    }
}


const getOrderNum = ()=>{
    if(printInfo.printType == '销售订单')
    {
        return printInfo.sell_order_num
    }
    else if(printInfo.printType == '采购订单')
    {
        return printInfo.buy_order_num
    }
    else if(printInfo.printType == '委外订单')
    {
        return printInfo.oem_order_num
    }
}

//查询目标信息
const getTargetInfo = async()=>{
    if(printInfo.printType == '销售订单')
    {
        const ret = await getBuyerInfoApi({
            id:printInfo.sell_man_id
        });
        if (ret) {
            console.log('====',ret)
            Object.assign(supplierInfo,ret.data)
        }
    }
    else if(printInfo.printType == '采购订单')
    {
        const ret = await getSupplierInfoApi({
            id:printInfo.supplier_id
        });
        if (ret) {
            console.log('====',ret)
            Object.assign(supplierInfo,ret.data)
        } 
    }
    else if(printInfo.printType == '委外单')
    {
        const ret = await getParterInfoApi({
            id:printInfo.parter_id
        })
        if (ret) {
            console.log('====',ret)
            Object.assign(oemInfo,ret.data)
        }
        
    }
}



const AddPrintHTM = () => {
    LODOP.PRINT_INIT(getTitle());//预览初始化  将内容置空
    LODOP.SET_PRINT_MODE("POS_BASEON_PAPER", true);//设置输出位置以纸张边缘为基点。
    // LODOP.SET_SHOW_MODE("HIDE_PBUTTIN_PREVIEW", true);//隐藏打印按钮
    LODOP.SET_SHOW_MODE("HIDE_SBUTTIN_PREVIEW", true);//隐藏打印设置按钮
    //纸张方向
    if (!printUtils.isDecimal(_this.offset_x + '') || !printUtils.isDecimal(_this.offset_y + '')) {
        ElMessageBox.alert("偏移值办理入不正确，请办理入一个有效的整数或小数");
        return;
    }
    // getContext()
    var nOffSetX = parseInt(Number(_this.offset_x));
    var nOffSetY = parseInt(Number(_this.offset_y));
    if (_this.sPageType == " ") {
        if (_this.pageDirection == 1) {
            LODOP.SET_PRINT_PAGESIZE(1, _this.nWidth, _this.nHeight, "");
        } else {
            LODOP.SET_PRINT_PAGESIZE(2, _this.nHeight, _this.nWidth, "");
        }
    } else {
        LODOP.SET_PRINT_PAGESIZE(_this.pageDirection - 0, 0, 0, _this.sPageType);
    }
    //页眉
    var Top = "";
    var Left = "";
    var Width = "";
    var Height = "";
    if (_this.nTopHeight > 0) {
        Top = (_this.nPaddingTop + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + nOffSetX) + "mm";
        Width = (_this.nPageWidth - _this.nPaddingLeft - _this.nPaddingRight) + "mm";
        Height = _this.nTopHeight + "mm";
        LODOP.ADD_PRINT_HTM(Top, Left, Width, Height, _this.sTopContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 1);
    }
    //下述代码主要是取消分页符在真正打印中出现
    var sContext = _this.printContent//valueHtml.value;
    // console.log('valueHtml.value', valueHtml.value)
    var nPosition1 = sContext.indexOf("<hr");
    var nPosition2 = sContext.indexOf("/>", nPosition1);
    while (nPosition1 > 0 && nPosition2 > 0) {
        sContext = sContext.substring(0, nPosition1) + "<div style=\"page-break-after: always;\">&nbsp;</div>" + sContext.substring(nPosition2 + 2);
        nPosition1 = sContext.indexOf("<hr");
        nPosition2 = sContext.indexOf("/>", nPosition1);
    }
    if (_this.sBodyType == "table") {
        Top = (_this.nPaddingTop + _this.nTopHeight + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + _this.nLeftWidth + nOffSetX) + "mm";
        Width = _this.nMiddleWidth + "mm";
        Height = (_this.nMiddleHeight - _this.nFollowHeight) + "mm";
        LODOP.ADD_PRINT_TABLE(Top, Left, Width, Height, sContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 0);
    } else {
        Top = (_this.nPaddingTop + _this.nTopHeight + nOffSetY) + "mm";
        Left = (_this.nPaddingLeft + _this.nLeftWidth + nOffSetX) + "mm";
        Width = _this.nMiddleWidth + "mm";
        Height = (_this.nMiddleHeight - _this.nFollowHeight) + "mm";
        LODOP.ADD_PRINT_HTM(Top, Left, Width, Height, sContext);
        LODOP.SET_PRINT_STYLEA(0, "ItemType", 0);
    }

    LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);//横向时的正向显示
    //判断是否选择打印机
    LODOP.SET_PRINTER_INDEX(_this.sPrintName.value);//指定打印设备
    LODOP.SET_PRINT_MODE("RESELECT_ORIENT", true);//设置是否可以重新选择打印方向
    LODOP.SET_PRINT_MODE("RESELECT_PAGESIZE", true);//设置是否可以重新选择纸张
    LODOP.SET_PRINT_MODE("RESELECT_COPIES", true);//设置是否可以重新选择打印份数。

    //=====授权信息，请不要更改或删除！！===================================
    // LODOP.SET_LICENSES("", "949BF0C7516C3851E0F5AA728041700D", "C94CEE276DB2187AE6B65D56B3FC2848", "");
    LODOP.SET_LICENSES("","EE0887D00FCC7D29375A695F728489A6","C94CEE276DB2187AE6B65D56B3FC2848","");
    //============================================================
}

//调用打印预览函数
const HzxPrintView = (type = "",bPreview=true) => {
    let sPrintId = _this.sPrintName.value;
    if (sPrintId === "") {
        if(printerList.length>0)
        {
            //设置默认打印机1
            _this.sPrintName = printerList[printerList.length - 1];
            sPrintId = _this.sPrintName.value;
        }
        if (sPrintId === "")
        {
            return ElMessage({
                message: '请选择打印机!',
                type: 'warning',
            })
        }

    }
    AddPrintHTM();
    if(bPreview)
    {
        if (type == 'full') {
            LODOP.SET_SHOW_MODE("PREVIEW_IN_BROWSE", false);
            LODOP.PREVIEW();
        } else {
            _this.sTaskCmd = "PREVIEW";
            if (_this.sLodopMode == "ActiveX") {
                LODOP.SET_SHOW_MODE("PREVIEW_IN_BROWSE", true);
                LODOP.PREVIEW();
                if (_this.sBatchPrintFlag == "begin") {
                    ActiveXNextPrint(nPrintCount);
                }
            } else {
                LODOP.PREVIEW('iframelodop');
            }
        }
    }

}
//全屏预览
function FullScreen() {
    HzxPrintView('full');
}

const props = defineProps<{
    show: boolean,
}>()
const show = ref(false);
//监听外部传入的显示设置
watch(() => props.show, async (val) => {
    if (val) {
        show.value = true
        console.log(11111)
        openPrint()
        getPrintTimes()
    }
})
const openPrint = async () => {
    let param = JSON.parse(sessionStorage.getItem('printInfo') || '{}')

    //支持批量打印
    if(Array.isArray(param))
    {
        printArray.splice(0, printArray.length, ...param)
    }
    else
    {
        printArray.splice(0, printArray.length)
        printArray.push(param)
    }


    Object.assign(printInfo,printArray[0])
    console.log('_this.orderInfo', JSON.parse(sessionStorage.getItem('printInfo') || '{}'))

    if (_this.sLodopMode == "ActiveX") {//控件模式
        printInit();
    }
    updateTitleRandom();
    _this.sLodopMode = "Cloud";
    LODOP = await window.getLodop();
    //如果lodop不存在则提示下载安装
    if (!LODOP) { 
       console.log('lodop不存在')
    }

    printInit();
}

/***********批量打印结束****************/
//另存为图片
function SaveImage() {
    if (_this.sLodopMode != "ActiveX") {
        AddPrintHTM();
    }
    var sTitleTemp = _this.sPrintTitle;
    if (_this.sPrintTitle == "") {
        sTitleTemp = _this.sDate;
    }
    LODOP.SAVE_TO_FILE(sTitleTemp + ".jpg");
}
function changeLodopType() {
    // printInit()
}
function Modify() {
    var s = _this.modifyText;
    if (s == "修改内容") {
        _this.changeCss = ''
        _this.modifyText = "保存修改";
    } else {
        _this.changeCss = 'hidden-box'
        _this.modifyText = "修改内容";
        HzxPrintView();
    }
}


//打开模板管理
const bShowTmpConfig = ref(false);
const onShowTmpConfig = () => {
    bShowTmpConfig.value = true;
    console.log(printInfo.printType)
    console.log(strPageSizeList)
}


const onPrint = async() => {
    
    const ret = await setHashApi({
    name: printInfo.printType+printInfo.id,
    json: {count:打印次数.value+1}
    })
    if (ret) {
        getPrintTimes()
    }

    AddPrintHTM();
    LODOP.PRINT();
    ElMessage.success('已发送到打印机，请查收！')
}
//批量打印
const onPrintMul = async () => {
    const ret = await setHashApi({
    name: printInfo.printType+printInfo.id,
    json: {count:打印次数.value+1}
    })
    if (ret) {
        getPrintTimes()
    }
    
    if (nPrintMode.value == '组合打印')
    {
        // await getTmpInfo(false,false)
        // LODOP.PRINT();
        AddPrintHTM();
        LODOP.PRINT();
        ElNotification({
                title: '成功',
                message: '已发送到打印机！',
                type: 'success',
                position: 'bottom-right',
                duration:2000,
            })

        //组合打印把所有单子的pdt整合到一起
        // Object.assign(printInfo, printArray[0])
        // for (let i = 1; i < printArray.length; i++)
        // {
        //     printInfo.pdt_list.push(...printArray[i].pdt_list)
        // }
        // console.log('组合和',printInfo.pdt_list)
    }
    else
    {
        console.log('分页打',printInfo.pdt_list)
        for (let i = 0; i < printArray.length; i++)
        {        
            Object.assign(printInfo, printArray[i])
            console.log('打印:', printInfo)
            await getTmpInfo(false,false)
            LODOP.PRINT();
            
            ElNotification({
                title: '成功',
                message: '任务 ' + (i + 1) + '/' + printArray.length + ' 已发送到打印机！',
                type: 'success',
                position: 'bottom-right',
                duration:2000,
            })
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    

}
//调整选择模板
const onChangePrintMode= async(value)=>{
    await getTmpInfo(true,false)
}

const onDownCtl = ()=>{
    // downloadFile('/CLodop_Setup_for_Win32NT.exe','CLodop_Setup_for_Win32NT.exe')
    
    downloadFile('https://winbosz.oss-cn-shenzhen.aliyuncs.com/tool/CLodop_Setup_for_Win32NT.exe','CLodop_Setup_for_Win32NT.exe')
}
//重新连接远程打印服务
const onConnectRemote =()=>{
    window.reInitLodop('remote',_this.serverip,()=>{
        console.log('连接成功',window.getCLodop)
        printInit();
    })
}

const bNoCtl = ref(false)


const 打印次数 = ref(1)
const getPrintTimes = async () => {
    const ret = await getHashApi({
        name: printInfo.printType+printInfo.id,
        page: 1,
        count: 10000
    })
    if (ret) {
        if(ret.data.json != undefined)
        {
            打印次数.value = ret.data.json.count
        }
        else
        {
            打印次数.value = 1
        }
    }
}
</script>

<template>
    <div>
        <el-dialog destroy-on-close v-model="show" fullscreen title="打印" >
            <div v-show="bNoCtl" class="text-center p-5 text-red-500">
                <span class="zoom-animation">未安装打印插件，请下载安装后刷新页面重试！</span>
                <ElButton class="ml-5" type="primary" @click="onDownCtl">下载安装</ELbutton>
            </div>
            <!-- <div class="bg-white"> -->
            <form id='mainform' action="#" method="post" class="h-[100%]">
                <table class='htable h-[94%]' style='width:100%;margin:0px auto;' >
                    <tr style='height:35px;'>
                        <td colspan=4 style='height:30px;'>
                            <div class="flex justify-evenly pl-2 pr-2">
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="() => { show = false }">
                                    关闭
                                </ElButton>
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="Modify">
                                    {{ _this.modifyText }}
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton id='buttonModify' style='width:150px;height:30px;line-height:30px'
                                    @click="FullScreen">全屏预览
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton style='width:150px;height:30px;line-height:30px' @click="() => LODOP.PRINT()">
                                    另保存(pdf)
                                </ElButton>
                                &nbsp;&nbsp;&nbsp;
                                <ElButton style='width:150px;height:30px;line-height:30px' @click="SaveImage">
                                    另保存(图片)
                                </ElButton>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th style='width:120px;height:30px;'>打印机信息</th>
                        <td class="p-1" style='text-align:left;'>
                            <span id='birdgelist'></span>
                            <ElSelect id='printlist selectprint' v-model="_this.sPrintName" placeholder="--请选择打印机--"
                                size="small" @change="printChange">
                                <ElOption v-for="item in printerList" :key="item.value" :label="item.label"
                                    :value="item" />
                            </ElSelect>
                            <ElSelect id='spanPaperList PaperList' v-model="_this.sPageType" placeholder="选择纸张"
                                size="small" @change="HzxPrintView()">
                                <ElOption label="自定义纸张" value=" " />
                                <ElOption v-for="item in strPageSizeList" :key="item" :label="item"
                                    :value="item" />
                            </ElSelect>
                           {{ _this.spagewidth }}mm *{{ _this.spageheight }}mm
                            进纸方向:
                            <ElSelect id='pagedirection' v-model="_this.pageDirection" style='width:100px;height:25px;'
                                size="small" @change="HzxPrintView()">
                                <ElOption label="纵向" :value="1" />
                                <ElOption label="横向" :value="2" />
                            </ElSelect>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            水平偏移:
                            <ElInput id='offset_x' v-model='_this.offset_x' @change="HzxPrintView()" size="small"
                                style='width:30px;margin:3px;height:20px;text-align:center' />
                            毫米&nbsp;&nbsp;&nbsp;&nbsp;
                            垂直偏移:
                            <ElInput id='offset_y' v-model='_this.offset_y' @change="HzxPrintView()" size="small"
                                style='width:30px;margin:3px;height:20px;text-align:center' />
                            毫米&nbsp;&nbsp;&nbsp;&nbsp;
                        </td>
                        <td class="w-150px text-center flex flex-col">
                            <ElSelect id='lodoptype' style='width:100px;height:25px;' size="small"
                                v-model="_this.sLodopMode" @change='changeLodopType()'>
                                <!-- <ElOption value='ActiveX' label="控件模式" /> -->
                                <ElOption value='Cloud' label="云打印模式" />
                                <ElOption value='NoControl' label="无插件模式" />
                                <!-- <ElOption value='Internet' label="互联网打印" /> -->
                            </ElSelect>
                            <div v-if="_this.sLodopMode == 'NoControl'">
                                <ElInput v-model='_this.serverip' size="small"
                                style='width:90px;margin:3px;height:20px;text-align:center' />
                                <ElButton @click="onConnectRemote" type="primary" size="small" class="w-[40px]">连接</ElButton>
                            </div>
                          
                        </td>
                    </tr>
                    <tr>
                        <th style='width:120px;height:30px;'>模板</th>
                        <td class="p-1">
                            <el-radio-group v-model="curSelTmpID" @change="onChangeSelTmp">
                                <el-radio v-for='item of tmpArray' :label="item.id" :key="item.id" size="small">{{item.template}}</el-radio>
                            </el-radio-group>
                        </td>
                        <td class="w-150px text-center">
                            <ElButton @click="onShowTmpConfig">设置</ElButton>
                        </td>
                    </tr>
                    <tr>
                        <th style='width:120px;height:30px;'>操作</th>
                        <td class="p-1 flex items-center" >
                            <div class="mr-2">
                                第<span class="text-red-500 "><strong>{{ 打印次数 }}</strong></span>次打印
                            </div>
                            <ElButton v-if="printArray.length==1" @click="onPrint">打印</ElButton>
                            <div v-if="printArray.length>1" class="flex items-center justify-start">
                                <ElButton @click="onPrintMul">批量打印</ElButton>
                                <span class="ml-3 mr-3" v-if="nPrintMode == '分页打印'">当前共{{ printArray.length }}个任务</span>
                                <div>
                                    <el-radio-group v-model="nPrintMode" @change="onChangePrintMode">
                                        <el-radio v-for='item of ["组合打印", "分页打印"]' :label="item" :key="item" size="small">{{item}}</el-radio>
                                    </el-radio-group>       
                                </div>

                            </div>

                        </td>
    
                        <td class="w-150px text-center">
              
                        </td>
                    </tr>
                    <tr id='trText' :class="_this.changeCss" style='height:100%;'>
                        <th>内容</th>
                        <td style='text-align:center;' colspan='2'>
                            <vue3-kind-editor id="editor_1" height="1000" width="800" v-model="_this.printContent"  :loadStyleMode="false">1</vue3-kind-editor>
                        </td>
                    </tr>
                    <tr id='trLodop' :class="_this.changeCss ? '' : 'hidden-box'" style="height:1000px;">
                        <td colspan=3 style='text-align:center; height: 1000px'>
                            <iframe id='iframelodop' name='iframelodop' style='width:100%;height:100%'></iframe>
                        </td>
                    </tr>
                </table>
            </form>
            <!-- </div> -->
        </el-dialog>

        <DialogPrintTmpSel v-model:show="bShowTmpConfig" :printType="printInfo.printType"  @on-submit="getPrintTmpList"/>

        <div id='topcontext' style="display:none;"></div>
        <div id='footcontext' style='display:none;'></div>
        <div id='contextfollow' style='display:none;'></div>
        <div id='leftcontext' style='display:none;'></div>
        <div id='rightcontext' style='display:none;'></div>
    </div>
</template>

<style lang="less" scoped>
@import './print.css';

.hidden-box {
    display: none !important;
}

:deep(.el-dialog__body) {
    padding: 0;
    color: var(--el-text-color-regular);
    font-size: var(--el-dialog-content-font-size);
    height: 100%;
}

.zoom-animation {
    display: inline-block;
    animation: zoomInOut 1s infinite alternate ease-in-out;
}

@keyframes zoomInOut {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
</style>
