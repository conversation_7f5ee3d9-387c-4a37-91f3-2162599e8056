<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag,ElTooltip,ElProgress,ElUpload, ElButton, ElMessage, ElMessageBox,ElImage,  ElInput, ElSelect, ElOption, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElPagination } from 'element-plus';
import { reactive, ref, onMounted, onActivated,onBeforeUnmount,watch } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { getPurchaseListApi,delPurchaseApi,updatePurchaseApi, updateOrderNotifyApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { DialogFileList } from "@/components/DialogFileList";
import { ceilToFixed, checkPermissionApi, downloadFile, getDateArea } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { cloneDeep } from 'lodash-es';
import { exportBuyListApi } from '@/api/extra';
import { getOssSignApi, ossUpload } from '@/api/oss';
import { importBuyOrderApi } from '@/api/task';
import { DialogImportTask } from '@/components/DialogImportTask'
import { clearDefer, useDefer } from '@/hooks/web/useDefer';

import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { DialogProductLite } from '@/components/DialogProductLite';
const defer = useDefer()

const { currentRoute,push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()

//采购单数据源
const purchaseData = reactive([])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])
const rootData = {
  added: {
    
  }
}

//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  fsm_cur_state:'',
  buy_order_num: '',
  supplier_nick:'',
  产品编码:'',
  产品名称:'',
  供应商:'',
  订单状态:'',
  收货状态:'',
  供应商单号:'',
  产品规格:'',
  采购人员:'',
  下单日期:['',''],
  交货日期:['',''],
  支付方式:'',
  收票标识:'',
  产品品牌:'',
  销售单:'',
  委外单:'',
  是否付款:'',
  采购备注:'',
  采购件数0:'',
  采购件数1:'',
  采购单价0:'',
  采购单价1:'',
  采购部门:'',
  订单总价0:'',
  订单总价1:'',
  跟单人员:'',
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getPurchaseList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getPurchaseList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getPurchaseList(val)
}

//处理表格对象操作
const handleOper = async(type, item) => {
  //编辑产品
  if(type === 'edit' || type === 'info')
  {
    if(type === 'info' && !checkPermissionApi('采购订单明细查看'))
    {
      ElMessage.error('无权限')
      return
    }
    if(type === 'edit' && !checkPermissionApi('采购订单修改'))
    {
      ElMessage.error('无权限')
      return
    }
    const retPath = (currentRoute.value.name.indexOf("Stone")>=0)?'/inventorymanage/stoneaddpurchase':'/purchasemanage/addpurchase'
    

    push({
      path: retPath,
      query:{
        id:item.id,
        type:type,
        cmd:bCheckMode.value?'审核':''
      }
    })
  }
  else if(type === 'del') //启用
  {
    if(!checkPermissionApi('采购订单删除'))
    {
      ElMessage.error('无权限')
      return
    }

    ElMessageBox.confirm(t('msg.confirm_del_purchase')+'--> '+item.buy_order_num, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const info = wsCache.get(appStore.getUserInfo)

      const ret = await delPurchaseApi({
        ids: [item.id],
        fsm_exe_man_name : info.resident_name,
        fsm_exe_trig : '删除'
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getPurchaseList()
      }
    }
    ).catch(() => {})
  }
  else if(type === 'receipt') //收货
  {
    push({
      path: '/purchasemanage/addreceipt',
      query:{
        id:'',
        purchase_id:item.buy_order_num
      },
    })
  }
  else if(type === 'return') //退货
  {
    push({
      path: '/purchasemanage/addreturn',
      query:{
        id:'',
        buy_order_num:item.buy_order_num
      }
    })
  }
  else if(type === 'receipt_list') //收货单列表
  {
    push({
      path: '/purchasemanage/receiptlist',
      query:{
        buy_order_num:item.buy_order_num,
        精确查询:'buy_order_num'
      }
    })
  }
  else if(type === 'return_list') //退货单列表
  {
    push({
      path: '/purchasemanage/returnlist',
      query:{
        buy_order_num:item.buy_order_num
      }
    })
  }
  else if(type === '关闭通知'){
    let ret = await updateOrderNotifyApi({
      order_num:item.buy_order_num,
      order_type:'采购单',
      enable_message:'关闭'
    })
    if(ret){
      ElMessage.success('关闭成功')
      getPurchaseList()
    }
  }
  else if(type === '打开通知'){
    let ret = await updateOrderNotifyApi({
      order_num:item.buy_order_num,
      order_type:'采购单',
      enable_message:'开启'
    })
    if(ret){
      ElMessage.success('关闭成功')
      getPurchaseList()
    }
  }
}

//进入新增界面
const onAddPurchase = ()=>{
  push({
    path: '/purchasemanage/addpurchase',
    query:{
      id:''
    },
  })
}

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '采购订单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const 总采购数量 = ref(0)
//查询采购单列表
const getPurchaseList = async (page = 1) => {
  clearDefer()
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.采购件数0
  delete tmp.采购件数1
  delete tmp.采购单价0
  delete tmp.采购单价1
  delete tmp.订单总价0
  delete tmp.订单总价1
  
  tmp.采购件数 = searchCondition.采购件数0+','+searchCondition.采购件数1
  tmp.采购单价 = searchCondition.采购单价0+','+searchCondition.采购单价1
  tmp.订单总价 = searchCondition.订单总价0+','+searchCondition.订单总价1
  tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1]
  tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1]

  isLoading.value = true
  purchaseData.splice(0,purchaseData.length)

  const ret = await getPurchaseListApi(tmp)
  if(ret)
  {
    purchaseData.splice(0,purchaseData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
    总采购数量.value = 0
    purchaseData.forEach(item=>{
      总采购数量.value += (parseInt(item.采购数量)+parseInt(item.采购备品数量))


      for(let one of item.pdt_list)
      {
        if(isNaN(Number(one.发票税率)))
        {
          console.error('有问题:',item.id,one.发票税率)
        }
        //修复字符串到数字
        one.buy_price_aft_tax = Number(one.buy_price_aft_tax)

        //校验有问题的单子
        if(Math.abs(ceilToFixed(one.buy_price_aft_tax*one.采购数量,6,0)  - one.总价)>0.0001 )
        {
          console.log('问题单子:'+item.buy_order_num,ceilToFixed(one.buy_price_aft_tax*one.采购数量,6,0),one.总价)
        }
      }
    })
    Object.assign(rootData, ret)
  }
  isLoading.value = false
}

//审核模式
const bCheckMode = ref(false)
onMounted(async() => {

  //初始化默认时间
  let days = getDateArea(365)
  defaultCondition.下单日期 = [days[0], days[1]]
  searchCondition.reset()


  if(currentRoute.value.name === "PurchaseCheck")
  {
    bCheckMode.value = true
    searchCondition.fsm_cur_state = '等待审核'
  }
  if(currentRoute.value.query.buy_order_num != undefined)
    searchCondition.buy_order_num = currentRoute.value.query.buy_order_num
  getPurchaseList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });

  adjustScrollerHeight()
  window.addEventListener('resize', adjustScrollerHeight)
})

onBeforeMount(() => {

});




//显示文件列表
const curSelItem = ref({id:'0',file_list:[],hetong_list:[]})
const curSelFileType = ref('图片')
const curTitle = ref('')
const showFileList = ref(false)
const onShowFileList = (item,type)=>{
  showFileList.value = true
  console.log(item)
  curSelItem.value = item
  curSelFileType.value = type 
  if(curSelFileType.value === '图片')
  {
    curTitle.value = '采购图片查看'
  }
  else
  {
    curTitle.value = '采购文件查看'
  }
}
const onUpdateFileList = async(list)=>{
  if(curSelFileType.value === '图片')
  {
    curSelItem.value.file_list = [...list]
  }
  else
  {
    curSelItem.value.hetong_list = [...list]
  }
  // console.log('11111',curSelItem)
  curSelItem.value.fsm_exe_trig= '保存'
  //更新文件列表
  const ret =await updatePurchaseApi(curSelItem.value)
  if(ret)
  {
      ElMessage.success('更新文件成功！')
      getPurchaseList()
  }
}

//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
  scrollY.value = document.getElementById('mainscroll')?.scrollTop
  console.log('离开了',scrollY)
  next()
})
onActivated(()=>{
  document.getElementById('mainscroll')?.scrollTo(0,scrollY.value)
})

const isLoading = ref(false)


const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    delete tmp.采购件数0
    delete tmp.采购件数1
    delete tmp.采购单价0
    delete tmp.采购单价1
    delete tmp.订单总价0
    delete tmp.订单总价1
    
    tmp.采购件数 = searchCondition.采购件数0+','+searchCondition.采购件数1
    tmp.采购单价 = searchCondition.采购单价0+','+searchCondition.采购单价1
    tmp.订单总价 = searchCondition.订单总价0+','+searchCondition.订单总价1
    tmp.下单日期 = searchCondition.下单日期[0]+','+searchCondition.下单日期[1]
    tmp.交货日期 = searchCondition.交货日期[0]+','+searchCondition.交货日期[1]

    const ret = await exportBuyListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}



//正在上传
const bShowTaskDlg = ref(false)
const onShowTaskDlg = async()=>{
  bShowTaskDlg.value = true
}
const loading = ref(false)
const uploadImg = async(file) => {
  const info = wsCache.get(appStore.getUserInfo)
  console.log(info)

    let path = 'import/'+info.username+'/buy/'
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        loading.value = true
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
        })  
        loading.value = false   
        // fileData.push({
        //     name:file.file.name,
        //     url:end.url
        // })
        //上传完成调用导入
        const imp = await importBuyOrderApi({
          url:end.url
        })
        if(imp)
        {
            ElMessage.success("上传文件成功，等待系统导入！")
            console.log('上传完成',end.url)
            onShowTaskDlg()
        }
        else
        {
          ElMessage.error("创建导入任务失败，请重试！")
        }

    }
}

const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(purchaseData, () => {
  adjustScrollerHeight()
})

//显示产品信息
const curSelItemPdt = ref({id:'0',file_list:[],hetong_list:[]})
const showPdtInfo = ref(false)
const onShowPdtInfo = (item)=>{
  showPdtInfo.value = true
  curSelItemPdt.value = item
}
</script>

<template>
  <!-- 采购单列表 -->
  <div ref="rootRef" class1="flex-col w-[100%] relative">

    <div class="p-2 pt-0">
      <div  class="pt-1 pr-5 pl-5 pb-2 mb-2 bg-white">
        <div class="absolute top-8 left-15 flex">
          <ElButton class="mr-3" v-if="checkPermissionApi('采购订单新增')" type="success" @click="onAddPurchase">
            <Icon icon="carbon:document-add" />
            <div class="pl-2">{{ t('button.add') }}</div>
          </ElButton>
          <el-upload
              class='mr-3'
              :http-request="(file) => uploadImg(file)"
              :auto-upload="true"
              :show-file-list = 'false'
              >
            <template #trigger>
              <el-button color="#409EFF" plain :loading="loading" :disabled="loading">
                <Icon icon="clarity:import-line" />
                {{ loading?'上传中..':'导入' }} 
              </el-button>
            </template>
          </el-upload>
          <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
              <Icon icon="carbon:export" />
              <div class="pl-2">{{ t('button.export') }}</div>
          </ElButton>
        </div>

        <div class="text-center pt-2 mb-5 font-bold">{{ t('purchase.list')+(bCheckMode?'-审核':'') }}</div>

        <!-- 检索条件 -->
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.check_status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择" >
            <el-option v-for="item in ['订单创建', '等待审核', '等待修改', '等待提交', '已审核', '已拒绝', '已关闭']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.id') }}</div>
          <el-input size="small"   v-model="searchCondition.buy_order_num" placeholder="" class="searchItem" />
        </div>
        <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small"  v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small"  v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.supplier') }}</div>
          <el-input size="small"  v-model="searchCondition.supplier_nick" placeholder="" class="searchItem" />
        </div>
        <!-- <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.订单状态" placeholder="请选择" >
            <el-option v-for="item in ['进行中+已完成','进行中','已完成','已暂停','已停止']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.receipt_status') }}</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.收货状态" placeholder="请选择" >
            <el-option v-for="item in ['未收货','部分收货','完全收货','超量收货','收货且退货','未收货+部分收货']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">{{ t('purchase.supplier_sup_id') }}</div>
          <el-input size="small"  v-model="searchCondition.供应商单号" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品规格</div>
          <el-input size="small"  v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购人员</div>
          <el-input size="small"  v-model="searchCondition.采购人员" placeholder="" class="searchItem" />
        </div>

        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">支付方式</div>
            <el-select size="small"  class="searchItem" v-model="searchCondition.支付方式" placeholder="" >
              <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
            </el-select>
        </div>
        <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">收票标识</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.收票标识" placeholder="" >
            <el-option v-for="item in ['不收发票','未收发票','部分收票','完全收票']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">产品品牌</div>
          <el-input size="small"  v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联销售单</div>
          <el-input size="small"  v-model="searchCondition.销售单" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">关联委外单</div>
          <el-input size="small"  v-model="searchCondition.委外单" placeholder="" class="searchItem" />
        </div>

        <!-- <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">是否付款</div>
          <el-select size="small"  class="searchItem" v-model="searchCondition.是否付款" placeholder="" >
            <el-option v-for="item in ['未付款','部分付款','完全付款','未付款+部分付款']" :key="item" :label="item" :value="item" />
          </el-select>
        </div> -->
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购备注</div>
          <el-input size="small"  v-model="searchCondition.采购备注" placeholder="" class="searchItem" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购件数</div>
          <el-input size="small"  v-model="searchCondition.采购件数0" placeholder="" class="!w-[60px]" type="number"/>
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.采购件数1" placeholder="" class="!w-[60px]" type="number" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购单价</div>
          <el-input size="small"  v-model="searchCondition.采购单价0" placeholder="" class="!w-[60px]" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.采购单价1" placeholder="" class="!w-[60px]" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">采购部门</div>
          <el-input size="small"  v-model="searchCondition.采购部门" placeholder="" class="searchItem" />
        </div>
        <div v-if="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">订单总价</div>
          <el-input size="small"  v-model="searchCondition.订单总价0" placeholder="" class="!w-[60px]" />
          <div class="searchTitle !w-32px">到</div>
          <el-input size="small"  v-model="searchCondition.订单总价1" placeholder="" class="!w-[60px]" />
        </div>
        <div v-show="senior" class="inline-flex items-center ml-1 mb-1">
          <div class="searchTitle">跟单人员</div>
          <el-input size="small"  v-model="searchCondition.跟单人员" placeholder="" class="searchItem" />
        </div>
        <div  class="inline-flex items-center mr-5 mb-2">
            <div class="searchTitle">下单日期</div>
            <el-date-picker size="small"  class="searchItem" v-model="searchCondition.下单日期" type="daterange" range-separator="To"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div v-if="senior" class="inline-flex items-center mr-5 mb-2">
          <div class="searchTitle">交货日期</div>
          <el-date-picker size="small"  class="searchItem" v-model="searchCondition.交货日期" type="daterange" range-separator="To"
            start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
        </div>
        <div  class="flex justify-end items-center mr-6 mt-1">
          <el-checkbox :label="t('customer.senior')" size="small" v-model="senior"/>
          <ElButton type="primary" class="ml-4" @click="onSearch">
            <Icon icon="tabler:search" />
            <div class="pl-1">查询</div>
          </ElButton>
          <ElButton type="warning" @click="onClear">
            <Icon icon="ant-design:clear-outlined" />
            <div class="pl-2">清除</div>
          </ElButton>
        </div>
      </div>
      <!-- 图例 -->
      <div class="flex mb-1">
        <div class="flex text-sm items-center mr-6">
          <div class="title_unreceipt w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          未收货
        </div>
        <div class="flex text-sm items-center mr-6">
          <div class="title_partial w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          部分收货
        </div>
        <div class="flex text-sm items-center mr-6">
          <div class="title_ok w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          完全收货
        </div>
        <div class="flex text-sm items-center">
          <div class="title_over w-[14px] h-[14px] rounded-[50%] mr-1"></div>
          超量收货
        </div>
      </div>

      <!-- 产品列表 -->
      <div v-loading.lock="isLoading">
        <!-- 表头 -->
        <div class="flex header headerBk">
          <div class="w-[60%] flex">
            <div class="w-[50%] flex-grow">产品名称</div>
            <div class="w-[10%] ">数量</div>
            <div class="w-[20%] " v-show="checkPermissionApi('采购订单价格显示')">单价</div>
            <div class="w-[20%] ">交货日期</div>
          </div>
          <div class="w-[40%] flex  !p-0">
            <div class="!max-w-[100px] rightcss rightcss_title" v-show="checkPermissionApi('采购订单价格显示')">金额</div>
            <div class="!max-w-[100px] rightcss rightcss_title">收货状态</div>
            <div class="!max-w-[100px] rightcss rightcss_title">订单状态</div>
            <div class="rightcss rightcss_title">备注</div>
            <div class="rightcss_title !min-w-[70px]">操作</div>
          </div>

        </div>
        <!-- 表内容 -->
        <DynamicScroller :items="purchaseData" :min-item-size="89.57" key-field="id" class="scroller" id="dynamicScroller">
          <template #default="{ item, index,active }">
            <DynamicScrollerItem 
              :item="item" :size-dependencies="[
                  item.pdt_list.length
                ]" 
              :data-index="index"
              :active="active"
            >
              <div> 
                <!-- 内容头 -->
                <div>
                  <div class="p-1  flex flex-nowrap text-[12px] font-bold border-1px" style="background-color: #BFEFFF;" >
                    <div class="flex items-center mr-1 w-80px">
                      <div class="rounded p-1 pl-2 pr-2" style="color: #fff;"
                        :class="{'title_unreceipt': item.收货状态 === '未收货', 'title_partial': item.收货状态 === '部分收货', 'title_ok': item.收货状态 === '完全收货', 'title_over': item.收货状态 === '超量收货'}">
                        {{ item.收货状态}}
                      </div>
                    </div>
                    <div class="w-[35%] min-w-[200px] flex items-center">
                      <div class="mr-3 font-bold">{{ item.create_date.split(' ')[0] }}</div>
                      <div>订单号: {{ item.buy_order_num }}</div>
                    </div>
                    <div class="min-w-[200px] flex items-center">
                      <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                      <div class=''>{{ checkPermissionApi('供应商名称显示')?item.supplier_nick:'***' }}</div>
                    </div>
                    <div class="flex justify-center items-center min-w-[250px]">
                      <div class="mr-5">采购:{{ item.buy_man_name }}</div>
                      <Icon v-if="checkPermissionApi('采购图片查看')" style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                      <div v-if="checkPermissionApi('采购图片查看')" class="cursor-pointer" @click="onShowFileList(item,'图片')">图片({{ item.file_list.length }})</div>
                      <Icon v-if="checkPermissionApi('采购文件查看')" style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                      <div v-if="checkPermissionApi('采购文件查看')" class="cursor-pointer" @click="onShowFileList(item,'文件')">文件({{ item.hetong_list.length }})</div>
                    </div>
                    <!-- 靠右的其他信息 -->
                    <div class="ml-auto flex justify-center items-center min-w-[200px]">
                      <div class="flex items-center mr-5">
                          通知:{{ item.enable_message }}
                        </div>
                      <!-- <Icon style="scale: 1.1; margin:0 5px 0 50px;" icon="ic:baseline-qrcode" /> -->
                      <Icon title="打印" style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline" 
                            @click="toPrintPage(item)"
                          />
                      <!-- <Icon style="scale: 1.1; margin:0 5px 0 10px;" icon="uil:copy" /> -->
                    </div>
                  </div>
                </div>
                <!-- 内容体 -->
                <div class="flex">
                  <!-- 左边产品列表 -->
                  <div class="w-[60%]  table_self">
                    <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%] min-h-[80px]">
                      <div class="w-[50%] flex-grow !min-w-[300px]">
                        <div class="flex justify-start items-center w-[100%] ">
                          <el-image v-if="pdt.pics.length>0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" :src="pdt.pics[0].url"  @click="onShowPdtInfo(pdt)"/>
                          <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[60px] h-[60px] min-w-[60px] cursor-pointer" src="/nopic.jpg" @click="onShowPdtInfo(pdt)"/>
                          <!-- <div class="ml-2 max-w-[100%]">
                            <div style="white-space: normal;" class="nameStyle" @click="handleOper('info', item)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                            <div v-if="pdt.sell_order_num!=''" style="font-size: smaller; color: rgb(81, 141, 146);" class="mt-2 cursor-pointer">销售单:{{ pdt.sell_order_num }}</div>
                            <div v-if="pdt.sell_order_num!=''" style="font-size: smaller; color: rgb(81, 141, 146);" class="mt-2 cursor-pointer">销售单:{{ pdt.sell_order_num }}</div>
                          </div> -->
                          <div class="ml-2 inline-block text-left max-w-[100%]">
                            <div class="flex">
        
                              <div v-if="pdt.sell_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt mr-5 mb-1  cursor-pointer flex ">
                                <span>销售单:</span>
                                <span v-if="pdt.sell_order_num.indexOf(',')<0">{{ pdt.sell_order_num }}</span>
                                <el-tooltip
                                  v-if="pdt.sell_order_num.indexOf(',')>=0"
                                  class="box-item"
                                  effect="dark"
                                  :content="pdt.sell_order_num"
                                  placement="bottom"
                                >
                                  <el-tag type="info" effect="dark">组合备料</el-tag>
                                
                                </el-tooltip>
                              </div>
                              <div v-if="pdt.oem_order_num!=''" style="color: rgb(81, 141, 146);" class="extxt  cursor-pointer flex items-center">
                                <span>委外单:</span>
                                <span v-if="pdt.oem_order_num.indexOf(',')<0">{{ pdt.oem_order_num +'  ['+pdt.父子任务单号+']'}}</span>
                                <el-tooltip
                                  v-if="pdt.oem_order_num.indexOf(',')>=0"
                                  class="box-item"
                                  effect="dark"
                                  :content="pdt.oem_order_num"
                                  placement="bottom"
                                >
                                  <el-tag class="ml-2" type="warning" effect="dark">组单</el-tag>
                                
                                </el-tooltip>
                                <el-tooltip
                                  v-if="pdt.oem_order_num.indexOf(',')>=0"
                                  class="box-item"
                                  effect="dark"
                                  :content="pdt.父子任务单号"
                                  placement="bottom"
                                >
                                  <el-tag class="ml-2" type="info" effect="dark">子单</el-tag>
                                
                                </el-tooltip>
                              </div>
                              <!-- <div v-if="pdt.oem_order_num!=''" class="extxt mr-5 mb-1 flex flex-col">
                                <span>子单号:</span>
                                <span>{{ pdt.父子任务单号 }}</span>
                              </div> -->
                            </div>

                            <div style="white-space: normal;" class="mb-1 nameStyle" @click="onShowPdtInfo(pdt)">{{ '['+pdt.name+']'+pdt.nick }}</div>
                            <div class="ex_text">{{ pdt.specs_text }}</div>
                          </div>
                        </div>
                      </div>
                      <div class="w-[15%] flex flex-col justify-center items-center border-left-1px">
                        <div>{{ pdt.采购数量+' '+pdt.base_unit }}</div>
                        <div class="ex_text">备:{{ pdt.采购备品数量+' '+pdt.base_unit }}</div>
                      </div>
                      <div class="w-[10%] flex justify-center items-center flex-col border-left-1px" v-show="checkPermissionApi('采购订单价格显示')">
                        <!-- {{ parseFloat(pdt.buy_price_bef_tax.toFixed(8)) }} -->
                        <span>{{ parseFloat(pdt.buy_price_aft_tax.toFixed(8)) }}</span>
                        <div class="ex_text">含税{{ pdt.发票税率 }}%</div>
                      </div>
                      <div class="w-[25%] flex justify-center flex-col border-left-1px">
                        <div class="flex ml-4 mb-1">
                          {{ pdt.交货日期 }}
                        </div>
                        <div class="ex_text flex ml-4 mb-1"><div class="mr-2">已收货{{ pdt.已收货 }}</div><div class="mr-2">未收货{{ pdt.未收货 }}</div><div v-if="pdt.已退货>0" class=" text-red-500">已退货{{ pdt.已退货 }}</div><div v-if="(pdt.已收货-pdt.已退货)-(parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量))>0" class=" text-red-500">超量{{ (pdt.已收货-pdt.已退货)-(parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量)) }}</div></div>
                        <el-progress class="w-[90%] ml-4"
                          :percentage="Math.min(parseFloat((((pdt.已收货-pdt.已退货) / (parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量))) * 100).toFixed(2)), 100)"
                          :status="(pdt.已收货 - pdt.已退货) == (parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量)) ? 'success' : ((pdt.已收货-pdt.已退货) < (parseInt(pdt.采购数量)+parseInt(pdt.采购备品数量)) ? 'warning' : 'exception')"
                            />
                      </div>
                    </div>
                  </div>
                  <!-- 右边其他数据 -->
                  <div class="w-[40%] flex  text-center  right">
                    <div class="!max-w-[100px] rightcss flex justify-start items-center flex-col" v-show="checkPermissionApi('采购订单价格显示')">
                      <div style="word-wrap: break-word;padding: 0;" class="mt-5">￥{{ item.合计金额 }}</div>
                      <div class="ex_text !p-0 mt-1">{{ item.pay_type }}</div>
                    </div>
                    <div class="!max-w-[200px] rightcss flex justify-start items-center flex-col">
                      <div class="mb-1 !p-0 mt-5">{{ item.收货状态 }}</div>
                      <el-progress class="w-[90%] !p-0"
                            :text-inside="true"
                            :stroke-width="14"
                        :percentage="parseFloat((((item.收货数量-item.退货数量)/(parseInt(item.采购数量)+parseInt(item.采购备品数量)))*100).toFixed(2))"
                        :status="(item.收货数量-item.退货数量)==(parseInt(item.采购数量)+parseInt(item.采购备品数量))?'success':((item.收货数量-item.退货数量) < (parseInt(item.采购数量)+parseInt(item.采购备品数量))?'warning':'exception')"                      
                          />
                    </div>
                    <div class="!max-w-[100px] rightcss flex justify-center items-start">
                      <div style="font-size: 12px; border-radius: 4px;" class="mt-5" :class="['!p-1',{'title_create': ['订单创建','等待审核','等待修改','等待提交'].includes(item.fsm_cur_state), 'title_checked': item.fsm_cur_state === '已审核', 'title_ok2': ['已入库','已关闭','已拒绝'].includes(item.fsm_cur_state), 'title_wait': item.fsm_cur_state === '等待修改'}]">{{ item.fsm_cur_state }}</div>
                      <div class="text-red-400 mt-1">{{ item.fsm_log_list.length>0?item.fsm_log_list[0][5]:'' }}</div>
                    </div>
                    <div class="rightcss border-l-0.5px !max-w-[400px]" style="text-align: left;">{{ item.note }}</div>
                    <div class="!min-w-[70px] flex justify-center items-start mt-5 ">
                      <ElButton v-if="bCheckMode" type="success" size="small" @click="handleOper('info',item)">{{ t('cmd.check') }}</ElButton>
                      <el-dropdown v-if="!bCheckMode" trigger="click" placement="bottom">
                        <span class="el-dropdown-link">
                          <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                        </span>
                        <template #dropdown>
                          <div  class="flex flex-wrap w-[240px]">
                            <span class="flex flex-wrap w-[240px]" v-if="item.fsm_cur_state == '已审核'">
                              <el-dropdown-item :disabled="item.收货状态 == '完全收货'" @click="handleOper('receipt', item)">{{ t('receipt.get') }}</el-dropdown-item>
                              <el-dropdown-item @click="handleOper('receipt_list', item)">{{ t('receipt.get_list')+'('+item.drawin_count+')' }}</el-dropdown-item>
                              <el-dropdown-item @click="handleOper('return', item)">{{ t('receipt.out') }}</el-dropdown-item>
                              <el-dropdown-item @click="handleOper('return_list', item)">{{ t('receipt.out_list') +'('+item.cancel_count+')'}}</el-dropdown-item>
                            </span>
                            <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                            <el-dropdown-item @click="handleOper('edit', item)">{{ t('userOpt.edit') }}</el-dropdown-item>
                            <el-dropdown-item v-if="item.drawin_count<=0" @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>

                            <el-dropdown-item v-if="item.enable_message.indexOf('开启')>=0" @click="handleOper('关闭通知', item)">关闭通知</el-dropdown-item>
                            <el-dropdown-item v-if="item.enable_message.indexOf('关闭')>=0" @click="handleOper('打开通知', item)">打开通知</el-dropdown-item>
                          </div>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </div>

            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
    </div>

      <div class="flex mt-2 items-center">
        <div class="ml-auto ">
          <span class="font-bold">税前总金额:</span>
          <span class="mr-6">{{ rootData.added.税前合计 }}</span>
          <span class="font-bold">总金额:</span>
          <span class="mr-6">{{ rootData.added.税后合计 }}</span>
          <span class="font-bold">总数量:</span>
          <span class="mr-6">{{ rootData.added.采购合计 }}</span>
        </div>
        <el-pagination 
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[10, 50, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    <DialogFileList :path="'file/cg/'+curSelItem.buy_order_num+'/'" v-model:show="showFileList" :files="curSelFileType=='图片'?curSelItem.file_list:curSelItem.hetong_list" @on-update="onUpdateFileList" :type="curSelFileType" :title="curTitle"/>
    <PrintModal v-model:show="dialogVisible" />
    <DialogImportTask v-model:show="bShowTaskDlg" :mod="'buy_order'" :cmd="'import_list'" @on-submit="getPurchaseList"/>

    <DialogProductLite :id="curSelItemPdt.id" type="info" categ="" :exinfo="{}"  :title="t('product_manage.add_product')" v-model:show="showPdtInfo" @on-submit="()=>{}"/>
    <!-- <div class="h-[300px]"></div> -->
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
// }

.nameStyle {
  cursor: pointer;
}
.nameStyle:hover{
  color: rgb(130, 130, 255);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 150px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  color: #333;
  font-weight: bold;
}
.headerBk{
  background-color: #fff !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  // padding: 7px;
  >div{
    padding: 2px;
  }
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 2cqh;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
  flex: 1;
  min-width: 0; /* 设置最小宽度，防止内容撑大 */
  text-align: center; /* 文字居中对齐 */
  word-wrap: break-word; /* 文字超长时换行处理 */
  font-size: 11px;
}
.rightcss_title{
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}


//---------------列表对象标题栏条件颜色-------------------
.title_unreceipt{ //未收货
  background-color:#79bbff ;
}
.title_partial{ //部分收货
  background-color: #eebe77;
}
.title_ok{ //完全收货
  background-color: #95d475;
}
.title_over{//超量收货
  background-color: #FFC0CB;
}

.title_create{ //已创建
  color: #409EFF;
  background-color: color-mix(in oklab, #409EFF , transparent 80%);
}
.title_checked{ //已审核
  color: #529b2e;
  background-color: color-mix(in oklab, #529b2e, transparent 80%);

}
.title_ok2{ //已入库
  color: #67C23A;
  background-color: color-mix(in oklab, #67C23A, transparent 80%);
}
.title_wait{//等待修改
  color: #F56C6C;
  background-color: color-mix(in oklab, #F56C6C, transparent 80%);
}

:deep(.el-progress__text) {
  min-width: 0 !important;
}
</style>
