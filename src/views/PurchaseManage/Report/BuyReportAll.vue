<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElButton, ElTable, ElTableColumn, ElDatePicker, ElMessage, ElMessageBox, } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath, downloadFile } from '@/api/tool';
import { getBuyReportAllApi, getSellBayBillTJApi, getSellReportAllApi } from '@/api/tj';
import { cloneDeep } from 'lodash-es';
import { exportBuyReportAllApi } from '@/api/extra';


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    年份: '',
    page: 1,
    count: 200
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getBuyReportAll = async () => {
    loading.value = true
    const ret = await getBuyReportAllApi(searchCondition)
    if (ret) {
        console.log(ret)
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getBuyReportAll()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getBuyReportAll()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getBuyReportAll()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

onMounted(() => {
    //设置默认年份
    searchCondition.年份 = new Date().getFullYear().toString()
    //刷新表格
    getBuyReportAll()
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['签约总金额_元','付款总金额_元'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] = '￥'+sum.toLocaleString(); // 添加千位分隔符
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}
const loading = ref(false)




const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)

    const ret = await exportBuyReportAllApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">


            <div class="text-center pt-3 mb-5 font-bold" style="color:#333">{{ searchCondition.年份 + '年采购统计总表' }}</div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">年份</div>
                <el-date-picker @change="onSearch" @calendar-change="onSearch" :clearable="false"
                    v-model="searchCondition.年份" type="year" placeholder="Pick a year" format="YYYY"
                    value-format="YYYY" />
                    <ElButton class="ml-5" type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                        <Icon icon="carbon:export" />
                        <div class="pl-2">{{ t('button.export') }}</div>
                    </ElButton>
            </div>
            <el-table v-loading="loading" ref="userTableRef11" header-cell-class-name="tableHeader" :data="reportData"
                style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries" border stripe>

                <el-table-column align="center" show-overflow-tooltip prop="月份" :label="'月份'" />
                <el-table-column align="center" show-overflow-tooltip prop="签约总金额_元" :label="'签约总金额(元)'">
                    <template #default="scope">
                        <div>{{ '￥'+scope.row.签约总金额_元.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="付款总金额_元" :label="'付款总金额(元)'">
                    <template #default="scope">
                        <div>{{ '￥'+scope.row.付款总金额_元.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count" :page-sizes="[20, 50, 100, 300]" :background="true"
          layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" /> -->
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
