<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElTag, ElButton,ElTooltip, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted,onBeforeUnmount,watch } from 'vue'
import { useRouter } from 'vue-router'
import { getReturnListApi,delQualityCheckApi } from '@/api/product'
import { checkPermissionApi, downloadFile } from '@/api/tool';
import PrintModal from '@/views/PrintManage/PrintModal.vue'
import { DynamicScroller,DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { cloneDeep } from 'lodash-es';
import { exportBuyReturnListApi } from '@/api/extra';

const { currentRoute,push } = useRouter()
const { t } = useI18n()


//退货单数据源
const returnData = reactive([])
const rootData = {
  added: {
    
  }
}
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  采购单号: '',
  退货单号: '',
  产品编码: '', 
  产品名称: '',
  供应商: '',
  fsm_cur_state: '',
  收货状态: '',
  产品规格: '',
  采购单价0: '',
  采购单价1: '',
  退货时间: ['', ''],
  page: 1,
  count: 10
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)


//开始查询
const onSearch = () => {
  getReturnList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}

//page控件发生切换
const handleSizeChange = (val: number) => {
  getReturnList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getReturnList(val)
}

//处理表格对象操作
const handleOper = (type, item) => {
    console.log(item)
  //编辑
  if(type === 'edit' || type == 'info')
  {
    const retPath = currentRoute.value.name == "StoneReturnList"?'/inventorymanage/stoneaddreturn':'/purchasemanage/addreturn'
    
    push({
      path: retPath,
      query:{
          id:item.id,
          type:type,
          buy_order_num:item.buy_order_num
      }
    })
  }
  // else if(type === 'del')
  // {
  //   ElMessageBox.confirm(t('msg.confirm_del_qualitycheck')+'--> '+item.buy_drawin_num, t('msg.notify'), {
  //     confirmButtonText: t('msg.ok'),
  //     cancelButtonText: t('msg.channel'),
  //     type: 'error',
  //   }
  //   ).then(async () => {
  //     console.log(item)
  //     const ret = await delQualityCheckApi({
  //       ids: [item.id],
  //     })
  //     if (ret) {
  //       ElMessage({
  //         type: 'success',
  //         message: t('msg.success'),
  //       })
  //       getReturnList()
  //     }
  //   }
  //   ).catch(() => {})
  // }
}


//查询退货单列表
const getReturnList = async (page = 1) => {
  searchCondition.page = page

  let tmp = cloneDeep(searchCondition)
  tmp.采购单价 = tmp.采购单价0 + ',' + tmp.采购单价1
  delete tmp.采购单价0
  delete tmp.采购单价1
  tmp.退货时间 = tmp.退货时间[0]+','+tmp.退货时间[1]
  isLoading.value = true
  const ret = await getReturnListApi(tmp)
  if(ret)
  {
    returnData.splice(0,returnData.length, ...ret.data)
    console.log(returnData)
    totleCount.value = parseInt(ret.count)
    Object.assign(rootData, ret)
  }
  isLoading.value = false
}

onMounted(async () => {
  if(currentRoute.value.query.buy_order_num != undefined)
    searchCondition.采购单号 = currentRoute.value.query.buy_order_num as string
  
  getReturnList()  
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})

const dialogVisible = ref(false)
//去打印
const toPrintPage = (item) => {
  let printInfo = { ...item, printType: '采购退货出库单' }
  sessionStorage.setItem('printInfo', JSON.stringify(printInfo))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}
//批量打印
const toPrintPageMul = () => {
  //拿到所有勾选
  let arrayAll = returnData.filter((one) => one.checked)
  if (arrayAll.length == 0)
  {
    ElMessage.warning('请先勾选要打印的单据！')
    return
  }
  console.log(arrayAll)
  let params = []
  for(let one of arrayAll)
  {
    params.push({ ...one, printType: '采购退货出库单' })
  }
  sessionStorage.setItem('printInfo', JSON.stringify(params))
  dialogVisible.value = true
  setTimeout(()=>{dialogVisible.value = false})
}

const checkAll = ref(false)
const onCheckedAll = () => {
  for (let one of returnData)
  {
    one.checked = checkAll.value
  }
}


const adjustScrollerHeight = () => {
  const height = document.getElementById('mainscroll')?.clientHeight
  const scroller = document.getElementById('dynamicScroller');
  if (scroller) {
    scroller.style.height = `${height}px`;
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustScrollerHeight)
})

watch(returnData, () => {
  adjustScrollerHeight()
})

const isLoading = ref(false)




const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let tmp = cloneDeep(searchCondition)
    tmp.采购单价 = tmp.采购单价0 + ',' + tmp.采购单价1
    delete tmp.采购单价0
    delete tmp.采购单价1
    tmp.退货时间 = tmp.退货时间[0]+','+tmp.退货时间[1]
    const ret = await exportBuyReturnListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          loadingExport.value = false
          downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

</script>

<template>
    <!-- 列表 -->
    <div ref="rootRef">

      <div class="p-1 pt-0">
        <div  class="pt-1 pb-1 pl-1 pr-1 pb-1 mb-1 bg-white relative">
          <div class="absolute top-1 left-15 flex">
            <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                <Icon icon="carbon:export" />
                <div class="pl-2">{{ t('button.export') }}</div>
            </ElButton>
          </div>
          <div class="text-center mb-5 font-bold">采购退货出库</div>
          <!-- 检索条件 -->
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">退货单号</div>
            <el-input size="small" v-model="searchCondition.退货单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.id') }}</div>
            <el-input size="small" v-model="searchCondition.采购单号" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.id') }}</div>
            <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.supplier') }}</div>
            <el-input size="small" v-model="searchCondition.供应商" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.status') }}</div>
            <el-select size="small" class="searchItem" v-model="searchCondition.fsm_cur_state" placeholder="请选择" >
              <el-option v-for="item in ['进行中','已完成','已暂停','已停止']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <!-- <div class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('purchase.receipt_status') }}</div>
            <el-select class="searchItem" v-model="searchCondition.收货状态" placeholder="请选择" >
              <el-option v-for="item in ['未收货','部分收货','完全收货','超量收货','收货且退货']" :key="item" :label="item" :value="item" />
            </el-select>
          </div> -->
          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input size="small" v-model="searchCondition.产品规格" placeholder="" class="searchItem" />
          </div>


          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.buy_price') }}</div>
            <el-input size="small" v-model="searchCondition.采购单价0" placeholder="" class="!w-[60px]" />
            <div class="searchTitle !w-32px">到</div>
            <el-input size="small" v-model="searchCondition.采购单价1" placeholder="" class="!w-[60px]" />
          </div>


          <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">退货时间</div>
              <el-date-picker size="small" class="searchItem" v-model="searchCondition.退货时间" type="daterange"
                range-separator="To" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
          </div>


          
          <div  class="text-center mt-5 mb-2 flex justify-end items-center">
            <!-- <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/> -->
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <div class="flex mb-2">
          <ElButton type="success" class="ml-auto" plain @click="toPrintPageMul">
            <Icon title="打印" icon="material-symbols:print-outline"/>
            批量打印
          </ElButton>
        </div>
        <!-- 产品列表 -->
        <div v-loading.lock="isLoading">
            <!-- 表头 -->
            <div class="flex header headerBk " >              
              <div class="w-[70%] flex items-center justify-center">
                <ElCheckbox class="min-w-[14px] !h-[14px] ml-5" v-model="checkAll" @change="onCheckedAll" />
                <div class="min-w-[60%] text-center">产品名称</div>
                <div class="min-w-[20%] text-center">出库数量</div>
                <div class="min-w-[20%] text-center">价格</div>
              </div>
              <div class="flex flex-grow !p-0">
                <div class="rightcss rightcss_title">状态</div>
                <div class="rightcss rightcss_title">备注</div>
                <div class="rightcss_title !min-w-[70px]">操作</div>
              </div>
            </div>
            <DynamicScroller :items="returnData" :min-item-size="89.57" key-field="id" class="scroller" id="dynamicScroller">
              <template #default="{ item, index,active }">
                <DynamicScrollerItem 
                  :item="item" :size-dependencies="[
                      item.pdt_list.length
                    ]" 
                  :data-index="index"
                  :active="active"
                >
                  <!-- 表内容 -->
                  <div class="mt-4 bg-white"  style="box-shadow:var(--el-box-shadow-lighter);">
                    <!-- 内容头 -->
                    <div>
                      
                      <div class="bg-light-500 font-bold p-2 flex flex-nowrap text-[13px] items-center">
                          <ElCheckbox v-model="item.checked" />
                          <div class="ml-2 w-[45%]  flex items-center">
                              <div class="mr-8 font-bold">
                                  {{ item.create_date.split(' ')[0] }}
                              </div> 
                              <div class="mr-6">出库单号: {{ item.buy_cancel_num }}</div>
                              <div>采购单编号: {{ item.buy_order_num }}</div>
                          </div>
                        <div class="min-w-[200px] flex items-center">
                          <Icon style="scale: 1.1;color: rgb(123, 175, 60); margin-right: 5px;" icon="mdi:company" />
                          {{ checkPermissionApi('供应商名称显示')?item.supplier_nick:'***' }}
                        </div>
                        <div class="flex justify-center items-center min-w-[250px]">
                          
                          <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin-right: 5px;" icon="solar:file-bold" />
                          <div>文件(0)</div>
                          <Icon style="scale: 1.1;color: rgb(101, 101, 236); margin:0 5px 0 10px;" icon="material-symbols:contract" />
                          <div>合同(0)</div>
                        </div>
                        <!-- 靠右的其他信息 -->
                        <div class="ml-auto flex justify-center items-center min-w-[200px]">
                          <div class="mr-8">出库人:{{ item.cancel_man_name }}</div>
                          <div class="mr-5">仓库:{{ item.store_nick }}</div>
                          <Icon title="打印" style="scale: 1.5; margin:0 5px 0 10px;color: rgb(45, 153, 253);cursor: pointer;" icon="material-symbols:print-outline" 
                            @click="toPrintPage(item)"
                          />
                        </div>
                      </div>
                    </div>
                    <!-- 内容体 -->
                    <div class="flex">
                      <!-- 左边产品列表 -->
                      <div class="w-[70%]  table_self">
                        <div v-for="pdt in item.pdt_list" :key="pdt.id" class="flex w-[100%]">
                          <div class="min-w-[60%] text-center flex-grow ">
                            <div class="flex justify-start items-center w-[100%] p-1">
                              <el-image v-if="pdt.pics.length>0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" :src="pdt.pics[0].url" />
                              <el-image v-if="pdt.pics.length<=0"  class="object-fill w-[80px] h-[80px] min-w-[80px]" src="/nopic.jpg" />
                              <div class="inline-block text-left max-w-[100%] ml-4">
                                <div style="white-space: normal;">{{ '['+pdt.name+']'+pdt.nick }}</div>
                              </div>
                            </div>
                          </div>                  
                          <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{ pdt.退货数量+' '+pdt.base_unit }}</div>
                          <div class="min-w-[20%] flex justify-center items-center flex-col border-l-1px">{{ checkPermissionApi('采购退货单显示价格')?(pdt.退货数量+'*'+pdt.buy_price_aft_tax+'='+ parseFloat(pdt.退货数量)*pdt.buy_price_aft_tax):'*' }}</div>
                        </div>
                      </div>
                      <!-- 右边其他数据 -->
                      <div class="flex flex-grow text-center  right">
                        <div class="rightcss" style="text-align:center;font-weight: bold;"> 已出库 </div>
                        <div class="rightcss">{{ item.note }}</div>
                        <div class="!min-w-[70px] flex justify-center items-center flex-col" style="text-align: center;">
                          <ElButton type="primary" size="small" @click="handleOper('info', item)">{{ t('userOpt.detail') }}</ElButton>
                          <!-- <el-dropdown trigger="click" placement="bottom">
                            <span class="el-dropdown-link">
                              <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                            </span>
                            <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item @click="handleOper('info', item)">{{ t('userOpt.detail') }}</el-dropdown-item>
                                <el-dropdown-item @click="handleOper('del', item)">{{ t('userOpt.del') }}</el-dropdown-item>
                              </el-dropdown-menu>
                            </template>
                          </el-dropdown> -->
                        </div>
                      </div>
                    </div>
                  </div>

                
                </DynamicScrollerItem>
              </template>
            </DynamicScroller>




        </div>
      </div>

    <div class="flex mt-2 items-center">
        <div class="ml-auto ">
          <span class="font-bold">税前总金额:</span>
          <span class="mr-6">{{ rootData.added.税前合计 }}</span>
          <span class="font-bold">总金额:</span>
          <span class="mr-6">{{ rootData.added.税后合计 }}</span>
          <span class="font-bold">总数量:</span>
          <span class="mr-6">{{ rootData.added.退货合计 }}</span>
        </div>
        <el-pagination 
          v-model:current-page="searchCondition.page"
          v-model:page-size="searchCondition.count"
          :page-sizes="[10, 50, 100, 300]"
          :background="true"
          layout="sizes, prev, pager, next"
          :total="totleCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    <!-- <div class="h-[300px]"></div> -->
    <PrintModal v-model:show="dialogVisible" />
    </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
:deep(.current-row) {
  background-color: #ffe48d !important;
}

:deep(.tableHeader) {
  background-color: #6d92b4 !important;
  color: #fff;
  font-weight: 400;
}

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ffe48d !important;
}

.searchItem{
  width: 140px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
  font-weight: bold;
  color: #333;
}
.headerBk{
  // background-color: #6d92b4 !important;
  background-color: #fff;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  font-size: 13px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

.table_self{
  font-size: 14px;
}
.table_self > div,
.right >div
{
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}
.test{
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: var(--el-border-color-lighter);
  padding: 7px;
}

.ex_text{
  font-size: 11px;
  color: #646464;
}
.ex_text_danger{
  &:extend(.ex_text);
  color: red;
}

//每个项目右侧4个列的CSS
.rightcss {
    flex: 1;
    min-width: 0; /* 设置最小宽度，防止内容撑大 */
    text-align: left; /* 文字居中对齐 */
    word-wrap: break-word; /* 文字超长时换行处理 */
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

.rightcss_title{
    display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
  align-items: center;
  font-size: 13px;
}

:deep(.el-checkbox__input){
  border: 1px solid #999;
}

</style>
