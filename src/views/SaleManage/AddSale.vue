<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive, nextTick } from 'vue'
import { ElCard, ElTable, ElPopconfirm, ElTag, ElCheckbox, ElDatePicker, ElTreeSelect, ElSelect, ElOption, ElTooltip, ElTableColumn, ElButton, ElForm, ElFormItem, FormRules, ElDescriptions, ElDescriptionsItem, ElInput, ElImage, ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { onMounted, set } from 'vue'
import { getProductListApi } from '@/api/product'
import type { FormInstance } from 'element-plus'
import { onBeforeUnmount, watch } from 'vue'
import { DialogSelCustomer } from '@/components/DialogSelCustomer'
import { DialogProductSel } from '@/components/DialogProductSel'
import { ceilToFixed, checkFormRule, checkPermissionApi, closeOneTagByName, closeOneTagByPath } from '@/api/tool'
import { DialogUser } from '@/components/DialogUser'
import { getSaleInfoApi, addSaleApi, updateSaleApi, getSaleNewnumApi } from '@/api/product'
import { getDepartmentListApi } from '@/api/usermanage'
import { useCache } from '@/hooks/web/useCache'
import { useAppStore } from '@/store/modules/app'
import { getTodayDate } from '@/api/tool'
import { computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { getGUID } from '@/api/tool'
import { getSupplierListApi } from '@/api/customer'
import { saleDef } from '@/types/logic'
import { DialogCheckShow } from '@/components/DialogCheckShow'
import { DialogFileList } from "@/components/DialogFileList";

const { currentRoute, back } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//标题
const title = ref('')
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    buyer_nick: [{ required: true, message: t('msg.noCustom'), trigger: 'blur' }],
    noSaleName: [{ required: true, message: t('msg.noCustomer'), trigger: 'blur' }],
})


//销售单数据
const saleData = reactive(
    {
        create_date: '',
        "sell_order_num": "",
        "diy_order_num": "",
        "buy_man_id": "",
        "is_same_delivery": 0,
        "delivery_date": "",
        "sell_man_id": "",
        "follow_man_id": "",
        "sell_dept_id": "",
        "sell_date": "",
        "note": "",
        "express_fee": 0.00,
        "other_fee": 0.00,
        "money_type": "人民币",
        "pay_type": "月结",
        "delivery_type": "",
        "express_type": "",
        "setup_type": "",
        "pack_type": "",
        "receive_man_id": "",
        "pdt_list": [],
        "remark": "",
        tax_type: '',
        tax_rate: '',
        source: '',
        fsm_can_trig_data: {
            审核触发: [],
            操作触发: ['保存']
        }, //审核决策
        fsm_cur_state: '订单创建',    //当前节点状态
        fsm_exe_man_name: '',
        fsm_exe_log: '',
        fsm_exe_trig: '',//决策内容
        fsm_log_list: [],
        deliver_date: '',
        hetong_list: [],
        file_list: [],
        order_type:'',//订单类型 大货单 样品单
    })



//默认币种
const moneyTypeData = reactive([
    '人民币',
    '美元',
    '港币',
    '日元',
    '欧元',
    '英镑',
    '韩元',
    '澳大利亚元',
])
//支付方式
const payTypeData = reactive([
    '现金',
    '月结',
    '月结45',
    '月结60',
    '支票',
    'POS机',
    '银行转账',
    '支付宝',
    '微信',
    '银联',
])

//联系人
const receive_users = reactive([])

//treeProp属性
const treeProps = {
    multiple: true,
    checkStrictly: true,
    value: 'id',
    children: 'sub_dept',
    label: 'name',
    emitPath: false
}

//获取最新ID
const onChangeID = async () => {
    const ret = await getSaleNewnumApi()
    if (ret) {
        console.log(ret)
        saleData.sell_order_num = ret.data.new_id
    }
}



//查询产品绑定的查询结果
const searchPdtData = reactive<any>([])
//查询产品列表根据编号
const getProductList = async (val = '') => {
    const ret = await getProductListApi({
        name: val,
        nick: val,
        _or: true,
        prop_list: ['销售产品'],
        status: '正常',
        page: 1,
        count: 30,
    })
    if (ret) {

        searchPdtData.splice(0, searchPdtData.length, ...ret.data)
        if (searchPdtData.length > 0) {
            setCurrent(searchPdtData[0])
        }
    }
}

//根据产品列表中数量计算销售总数
const totleSale = computed(() => {
    console.log('111')
    let totle = 0
    for (const item of saleData.pdt_list) {
        if (item.销售数量 == undefined || item.id == undefined)
            continue
        totle += parseFloat(item.销售数量)
        totle += parseFloat(item.销售备品数量)
    }
    return totle
})

//根据产品列表计算销售 总价
const totleSalePrice = computed(() => {

    let totle = 0
    let span = 0
    for (const item of saleData.pdt_list) {
        if (item.总价 == undefined || item.id == undefined)
            continue
        if (item.sell_buy_digit != undefined)
            span = parseInt(item.sell_buy_digit)
        totle += parseFloat(item.总价)
    }

    return parseFloat(totle.toFixed(span))
})

//计算合计费用(包含运费和其他费用)
const totleSalePriceAll = computed(() => {
    return totleSalePrice.value + parseFloat(saleData.express_fee.toString()) + parseFloat(saleData.other_fee.toString())
})



// //重新计算产品相关数据
// let timeoutId: ReturnType<typeof setTimeout> | null = null;
// const reComputePdtInfo = (pdt,mode) => {
//     // 取消之前的延迟处理
//     if (timeoutId) {
//         clearTimeout(timeoutId);
//     }

//     // 创建新的延迟处理
//     timeoutId = setTimeout(() => {
//         reComputePdtInfoFun(pdt,mode)
//     }, 1000); // 设置延迟时间，单位为毫秒
// }
//重新计算产品相关数据
const reComputePdtInfo = (pdt, mode) => {
    pdt.销售数量 = pdt.销售数量 == '' ? 0 : pdt.销售数量
    pdt.销售备品数量 = pdt.销售备品数量 == '' ? 0 : pdt.销售备品数量
    pdt.sell_price_bef_tax = pdt.sell_price_bef_tax == '' ? 0 : pdt.sell_price_bef_tax
    pdt.sell_price_aft_tax = pdt.sell_price_aft_tax == '' ? 0 : pdt.sell_price_aft_tax
    pdt.大模具费 = pdt.大模具费 == '' ? 0 : pdt.大模具费


    pdt.大模具费 = parseFloat(pdt.大模具费)
    console.log('1111', pdt.大模具费)

    if (mode == '销售数量') {
        //根据销售数量计算总价 总价等于税后单价乘以销售数量
        //重新计算税后价格
        // pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        // pdt.sell_price_bef_tax = parseFloat((pdt.sell_price_aft_tax/(1+pdt.发票税率/100)).toFixed(pdt.sell_buy_digit))

        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)

        //更新默认备品数
        // pdt.销售备品数量 = parseFloat(pdt.销售数量*0.01)
    }
    else if (mode == '销售备品数量') {
        //根据销售数量计算总价 总价等于税后单价乘以销售数量
        //重新计算税后价格
        // pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)

    }
    if (mode == '大模具费') {
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)
    }
    else if (mode == '税前单价') {
        //根据税前单价和税率计算出税后单价         
        pdt.sell_price_aft_tax = ceilToFixed(parseFloat(pdt.sell_price_bef_tax) + parseFloat(pdt.sell_price_bef_tax) * pdt.发票税率 / 100, 8, pdt.sell_price_aft_tax)
        //计算出总价
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)
    }
    else if (mode == '税后单价') {
        //根据税后单价和税率计算出税前单价         
        pdt.sell_price_bef_tax = ceilToFixed((pdt.sell_price_aft_tax / (1 + pdt.发票税率 / 100)), 8, pdt.sell_price_bef_tax)
        //计算出总价
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)
    }
    else if (mode == '折扣') {
        //根据折扣重新计算总价
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)
    }
    else if (mode == '发票税率') {
        //根据发票类型调整税率数
        if (!['普票', '专票', '电子票'].includes(pdt.发票类型)) {
            pdt.发票税率 = 0
            //重新调整税后价格
            pdt.sell_price_aft_tax = pdt.sell_price_bef_tax
        }
        //重新计算税后价格
        //pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_bef_tax)+ parseFloat(pdt.sell_price_bef_tax)*pdt.发票税率/100
        pdt.sell_price_bef_tax = ceilToFixed((pdt.sell_price_aft_tax / (1 + pdt.发票税率 / 100)), 8, pdt.sell_price_bef_tax)
        //重新计算总价
        pdt.总价 = ceilToFixed((pdt.sell_price_aft_tax * pdt.销售数量 + pdt.大模具费), 6, pdt.总价)
    }
    else if (mode == '总价') {
        //根据总价重新调整税前税后价格
        pdt.sell_price_aft_tax = ceilToFixed(((pdt.总价 - pdt.大模具费) / pdt.销售数量), 8, pdt.sell_price_aft_tax)
        pdt.sell_price_bef_tax = ceilToFixed(((pdt.总价 - pdt.大模具费) / ((1 + pdt.发票税率 / 100) * pdt.销售数量)), 8, pdt.sell_price_bef_tax)
    }

    pdt.销售数量 = parseFloat(pdt.销售数量)
    pdt.销售备品数量 = parseFloat(pdt.销售备品数量)
    pdt.sell_price_bef_tax = parseFloat(pdt.sell_price_bef_tax)
    pdt.sell_price_aft_tax = parseFloat(pdt.sell_price_aft_tax)
    pdt.大模具费 = parseFloat(pdt.大模具费)
}



onMounted(async () => {
    getDepartmentTreeInfo()

    if (currentRoute.value.query.id == '' || currentRoute.value.query.id == undefined) {
        title.value = t('sale.add')+' ['+currentRoute.value.query.order_type+'] '
        onChangeID()

        //新建需要默认可以提审
        const tmp = [...saleData.fsm_can_trig_data.操作触发, '提交审核']
        saleData.fsm_can_trig_data.操作触发 = tmp

        //设置默认人员部门
        const info = wsCache.get(appStore.getUserInfo)
        saleData.sell_man_id = info.id
        saleData.sell_man_name = info.resident_name
        saleData.follow_man_id = info.id
        saleData.follow_man_name = info.resident_name
        saleData.sell_dept_id = info.depts[0]
        //默认销售日期为今天
        saleData.sell_date = getTodayDate()
        saleData.create_date = getTodayDate()
        saleData.order_type = currentRoute.value.query.order_type as string

        //是否是售前转销售任务
        if (currentRoute.value.query.tosell == '1') {
            saleData.source = currentRoute.value.query.source
            const arPdtNum = wsCache.get('tosell')
            console.log(arPdtNum)
            //添加pdt到列表
            for (let pdt of arPdtNum) {
                const ret = await getProductListApi({
                    name: pdt.pdt_name,
                    page: 1,
                    count: 20,
                })
                if (ret) {
                    if (ret.data.length > 0) {
                        handleCurrentSelectPdt(ret.data[0], '', true)
                        if (saleData.source == '售前转销售')
                            ret.data[0].sell_offer_num = pdt.sell_offer_num

                        ret.data[0].销售数量 = pdt.pdt_amount
                        ret.data[0].标识 = pdt.标识

                    }
                }
            }
            //添加客户信息到订单
            saleData.buyer_nick = currentRoute.value.query.buyer_nick
            saleData.buyer_id = currentRoute.value.query.buyer_id
        }


        //追加默认行
        saleData.pdt_list.push({ 总价: '0' })
    }
    else {

        //查询产品信息 
        const ret = await getSaleInfoApi({
            id: currentRoute.value.query.id,
            page: 1,
            count: 100
        })
        if (ret) {
            console.log(ret)
            Object.assign(saleData, ret.data)


            saleData.pdt_list = ret.data.pdt_list;
            saleData.pdt_list.push({ 总价: '0' })

            if (currentRoute.value.query.type == 'info') {
                title.value = t('sale.look')+' ['+saleData.order_type+'] '
            }
            else {
                title.value = t('sale.edit')+' ['+saleData.order_type+'] '
            }


        }

        nextTick(() => {
            if (currentRoute.value.query.type === 'info') //查看模式
            {
                let components = document.querySelectorAll('.el-input__inner,.el-textarea__inner');
                let excludeDiv = document.getElementById('check');
                if (excludeDiv != null) {
                    // 使用 :not() 伪类排除特定的 div 元素下的子元素
                    let filteredComponents = Array.from(components).filter(
                        component => !excludeDiv.contains(component)
                    );
                    filteredComponents.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }
                else {
                    components.forEach((component) => {
                        component.setAttribute('disabled', true);
                    });
                }


                components = document.querySelectorAll('.el-input__wrapper,.el-textarea');
                components.forEach((component) => {
                    component.classList.add('infomode')
                });
                const suffixElements = document.querySelectorAll('.el-input__suffix');
                suffixElements.forEach(suffixElement => {
                    suffixElement.style.display = 'none';
                });
            }
        })
    }
})


//产品查找的输入
const txtSearch = ref('')
//点击了查找产品input
const onClickSearchPdt = () => {
    console.log('点了')
    getProductList(txtSearch.value)
    showProductSel.value = true
    // 添加全局点击事件监听器
    window.addEventListener('click', handleGlobalClick);
}
const handleGlobalClick = (event) => {

    const elTableContainers = document.querySelectorAll('.el-table-container');
    let clickedInsideTable = false;
    for (const container of elTableContainers) {
        if (container.contains(event.target)) {
            // 点击的元素在 el-table 容器内部
            clickedInsideTable = true;
            break;
        }
    }

    if (!clickedInsideTable) {
        // 判断点击位置是否在输入框和浮动窗口外部
        // const isOutsideClick = !inputElement.contains(event.target) && !floatWindowElement.contains(event.target);
        const bIn = (event.target as HTMLElement).classList.contains('el-input__inner')
        // 根据判断结果来决定是否隐藏浮动窗口
        if (!bIn) {
            showProductSel.value = false
        }
    }
}
//处理输入
const delaytime = ref<NodeJS.Timeout | null>(null);
const onInputSearch = (val) => {
    showProductSel.value = true
    console.log(val)
    if (delaytime.value !== undefined && typeof delaytime.value === 'number')
        clearTimeout(delaytime.value)

    delaytime.value = setTimeout(() => {
        // 输入停止后触发的方法
        getProductList(val)
    }, 200)
}


//显示隐藏选择客户弹窗
const showSelCusDlg = ref(false)
const onSelCustom = () => {
    showSelCusDlg.value = true
}
//选择客户回调
const onSelCusCallback = (id, name, type, customer) => {
    console.log(id, name, type, customer)

    //检测是否配置了税号，
    if (customer.corp_taxnum == undefined || customer.corp_taxnum == '') {
        ElMessage.error('请先配置客户 （' + customer.buyer_nick + '） 的税号！')
        return
    }


    saleData.buyer_id = id
    saleData.buyer_name = name
    saleData.buyer_nick = customer.type == '公司' ? customer.corp_name : customer.buyer_nick
    saleData.tax_type = customer.tax_type == undefined ? '普票' : customer.tax_type
    saleData.tax_rate = customer.tax_rate == '' ? 0 : (customer.tax_rate == '不含税' ? 0 : (customer.tax_rate.replace('%', '')))
    console.log(saleData)

    //同步更新所有产品税率
    for (let item of saleData.pdt_list) {
        item.发票类型 = saleData.tax_type
        item.发票税率 = parseInt(saleData.tax_rate)
        reComputePdtInfo(item, '发票税率')
    }
    //如果没有联系人列表则使用默认联系人
    if (customer.corp_linkman.length == 0 && customer.phone1 != '') {//mainer_name phone1
        customer.corp_linkman.push({
            name: customer.mainer_name,
            phone: customer.phone1
        })
    }

    //更新联系人
    Object.assign(receive_users, customer.corp_linkman)
    //复位已选择的客户
    saleData.receive_man_id = ""
}


//显示隐藏选择销售员窗口变量
const showSelSaleUserDlg = ref(false)
//显示选择销售员弹窗
const onSelSale = () => {
    showSelSaleUserDlg.value = true
}
//选择销售员回调
const onSelSaleCallback = (id, name) => {
    console.log(id, name)
    saleData.sell_man_id = id
    saleData.sell_man_name = name
}

//显示隐藏选择跟单员窗口变量
const showSelFollowerUserDlg = ref(false)
//显示选择跟单员弹窗
const onSelFollower = () => {
    showSelFollowerUserDlg.value = true
}
//选择跟单员回调
const onSelFollowerCallback = (id, name) => {
    console.log(id, name)
    saleData.follow_man_id = id
    saleData.follow_man_name = name
}

//部门数据源
const deptData = reactive([])
//查询组织信息
const getDepartmentTreeInfo = async () => {
    const ret = await getDepartmentListApi({})
    console.log(ret)
    if (ret) {

        //deptData.splice(0, deptData.length, ...data)
        deptData.splice(0, deptData.length, ...ret.data.all_depts)
        console.log(deptData.length)
    }
}

//勾选使用统一交付时间事件
const onChangeDeliveryTime = (val) => {
    if (val) //勾选
    {
        if (saleData.delivery_date == "") {
            saleData.delivery_date = getTodayDate()
        }
    }
}

//显示隐藏选择产品浮窗
const showProductSel = ref(false)
//选择了某一个产品
const handleCurrentSelectPdt = (item, old, fource = false) => {
    if (item == undefined) {
        return
    }

    nCurSelPdtID.value = item.id
    if (!fource) {
        return
    }
    txtSearch.value = ''

    setTimeout(() => {
        const inputElement = document.querySelector('.input-search input');
        inputElement?.focus()
        onClickSearchPdt()
    }, 500)




    //增加属性    
    item.材质 = '',
        item.类型 = '正常'
    item.库存数量 = 0
    item.可用数量 = 0
    item.销售数量 = 1
    item.销售备品数量 = 0
    item.交货日期 = saleData.delivery_date == '' ? getTodayDate() : saleData.delivery_date
    item.大模具费 = 0
    item.发票类型 = '专票'
    item.发票税率 = 0
    item.总价 = 0
    item.标识 = getGUID(10)
    item.销售备注 = ''
    item.locked_list = []
    item.sell_offer_num = ''

    //设置默认价格
    item.sell_price_bef_tax = item.sell_price_bef_tax
    item.sell_price_aft_tax = item.sell_price_aft_tax

    //如果已经选择了客户则自动同步税率
    if (saleData.tax_rate != undefined) {
        item.发票类型 = saleData.tax_type
        item.发票税率 = saleData.tax_rate
    }


    //更新产品价格
    reComputePdtInfo(item, '销售数量')

    //构造一行产品
    saleData.pdt_list.splice(-1, 0, item)
    //隐藏浮窗
    showProductSel.value = false

    console.log(saleData)

}

//删除某一个产品
const onDelPdt = (index) => {
    saleData.pdt_list.splice(index, 1)
}

//修改产品类型
const onChangeType = (item) => {
    if (item.类型 == '正常') {
        item.sell_price_bef_tax = item.备用税前
        item.sell_price_aft_tax = item.备用税后
        item.大模具费 = item.备用大模具费
        reComputePdtInfo(item, '销售数量')
    }
    else {
        item.备用税前 = item.sell_price_bef_tax
        item.备用税后 = item.sell_price_aft_tax
        item.备用大模具费 = item.大模具费
        item.sell_price_bef_tax = 0
        item.sell_price_aft_tax = 0
        item.大模具费 = 0
        reComputePdtInfo(item, '销售数量')

    }
}

//校验pdt
const checkPdt = () => {
    if (saleData.pdt_list.length <= 1) {
        ElMessage.warning(t('msg.pdtEmpty'))
        console.log('没有产品')
        return false
    }
    return true
}

//保存
const onSave = async () => {
    console.log(saleData)

    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //删除最后一行临时数据(克隆新数据提交不影响原数据)
    const data = cloneDeep(saleData)
    data.pdt_list.splice(-1, 1)

    if (!checkPdt()) {
        console.log('???')
        return
    }


    if (saleData.id == undefined) {
        const ret = await addSaleApi(data)
        if (ret) {
            ElMessage.success(t('msg.newSaleSuccess'))
            baskFront()
        }
    }
    else //修改
    {
        const ret = await updateSaleApi(data)
        if (ret) {
            ElMessage.success(t('msg.updateSaleSuccess'))
            baskFront()
        }
    }


}

//提交审核意见
const handleCheck = async (btn) => {
    const rule = await checkFormRule(ruleFormRef.value)
    if (!rule) {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    if (saleData.pay_type == "") {
        ElMessage.error('请选择付款方式！')
        return
    }
    if (saleData.hetong_list.length <= 0 && currentRoute.value.query.cmd != '审核' && saleData.order_type !='样品单') {
        ElMessage.error('请上传成本资料')
        return
    }

    //处理下空数据问题
    for (let pdt of saleData.pdt_list) {
        pdt.销售数量 = pdt.销售数量 == '' ? 0 : pdt.销售数量
        pdt.销售备品数量 = pdt.销售备品数量 == '' ? 0 : pdt.销售备品数量
        pdt.sell_price_bef_tax = pdt.sell_price_bef_tax == '' ? 0 : pdt.sell_price_bef_tax
        pdt.sell_price_aft_tax = pdt.sell_price_aft_tax == '' ? 0 : pdt.sell_price_aft_tax
        pdt.大模具费 = pdt.大模具费 == '' ? 0 : pdt.大模具费
    }


    const info = wsCache.get(appStore.getUserInfo)
    saleData.fsm_exe_man_name = info.resident_name
    saleData.fsm_exe_trig = btn
    const data = cloneDeep(saleData)
    data.pdt_list.splice(-1, 1)

    if (!checkPdt()) {
        return
    }


    //校验交货日期是否大于销售日期，如果大于则提示报错
    console.log(data)
    for (let pdt of data.pdt_list) {
        if (pdt.交货日期 != '') {
            const saleDate = new Date(data.create_date)
            const deliveryDate = new Date(pdt.交货日期)
            if (deliveryDate.getTime() < saleDate.getTime()) {
                ElMessage.error('交货日期不能小于销售日期！')
                return
            }
        }
    }

    //校验pdt数量，如果存在小于3000的则提示进入审核流程
    let bCheck = false
    let strPdtName = ''
    for (let pdt of data.pdt_list) {
        if (pdt.销售数量 < 3000) {
            bCheck = true
            strPdtName = pdt.nick
            break
        }
    }
    async function tijiao() {
        if (saleData.id == undefined) {
            const ret = await addSaleApi(data)
            if (ret) {
                ElMessage.success(t('msg.newPurchaseSuccess'))
                baskFront()
            }
        }
        else //修改
        {
            const ret = await updateSaleApi(data)
            if (ret) {
                ElMessage.success(t('msg.updatePurchaseSuccess'))

                if (btn == '恢复') {
                    //查询产品信息 
                    const ret = await getSaleInfoApi({
                        id: currentRoute.value.query.id,
                        page: 1,
                        count: 100
                    })
                    if (ret) {
                        console.log(ret)
                        Object.assign(saleData, ret.data)


                        saleData.pdt_list = ret.data.pdt_list;
                        saleData.pdt_list.push({ 总价: '0' })
                    }
                }
                else {
                    baskFront()
                }

            }
        }
    }
    if (bCheck &&  currentRoute.value.query.cmd != '审核' && saleData.order_type != '样品单' && btn!='驳回') {


        ElMessageBox.confirm('[ ' + strPdtName + ' ]' + ' 数量小于3000，需要提交领导审核？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(async () => {
            tijiao()
        }).catch(() => {
            // 用户点击了取消按钮
        });
    }
    else {
        tijiao()
    }



}

//显示任务历史
const showCheckHisDlg = ref(false)
const handleCheckHis = () => {
    showCheckHisDlg.value = true
}

//返回上一页
const baskFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
    //刷新上一页
    closeOneTagByPath('/salemanage/salemanage')
}

const baskFrontNoFreshFront = () => {
    back()
    closeOneTagByName(currentRoute.value.meta.title)
}

// const sameDay = computed(() => {
//   const get = () => {
//     return saleData.delivery_date === '' ? false : true;
//   },

//   const set = (val) => {
//     if (val) {
//       saleData.delivery_date = getTodayDate();
//     } else {
//       saleData.delivery_date = '';
//     }
//   }
// });
// 使用 ref 定义响应式变量
// const numberRef = ref(0);
// const stringRef = ref("");

// 使用 computed 定义计算属性
const sameDay = computed({
    get: () => (saleData.delivery_date === '' ? false : true),
    set: (value) => {
        if (value) {
            saleData.delivery_date = getTodayDate();
            //更新下面列表
            saleData.pdt_list.forEach((item, index) => {
                item.交货日期 = saleData.delivery_date;
            })
        } else {
            saleData.delivery_date = '';
        }
        console.log('->', saleData.delivery_date);
    },
});

const onChangeDeliverData = (value) => {
    saleData.pdt_list.forEach((item, index) => {
        item.交货日期 = saleData.delivery_date;
    })
}

//键盘上下只切换不选择
const nCurSelPdtID = ref('')
const bKeyDown = ref(true)
const onKeyDownOnePdt = (event) => {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // 阻止默认行为，以保持光标位置不变
        event.preventDefault();
    }
    //esc按键关闭table
    if (event.keyCode === 27) {
        showProductSel.value = false
        return
    }
    bKeyDown.value = true
    if (nCurSelPdtID.value == '') {
        setCurrent(searchPdtData[0])
    }
    else {


        for (let i = 0; i < searchPdtData.length; i++) {
            if (searchPdtData[i].id == nCurSelPdtID.value) {
                if (event.keyCode === 38 && i > 0)
                    setCurrent(searchPdtData[i - 1])
                else if (event.keyCode === 40 && i < searchPdtData.length - 1)
                    setCurrent(searchPdtData[i + 1])
                //如果是回车，直接选择
                else if (event.keyCode === 13) {
                    onRowClick(searchPdtData[i])
                    return
                }
                return
            }
        }
    }

}
const searchRef = ref<InstanceType<typeof ElTable>>()

const setCurrent = (row?) => {
    if (row == undefined)
        nCurSelPdtID.value = ''
    console.log('setCurrent', searchRef)
    searchRef.value!.setCurrentRow(row)
}
const onRowClick = (row) => {
    console.log('xuanle', row)
    handleCurrentSelectPdt(row, null, true)
}


//显示文件列表
const curSelFileType = ref('图片')
const curTitle = ref('')
const showFileList = ref(false)
const onShowFileList = (type) => {
    showFileList.value = true
    curSelFileType.value = type

    if (curSelFileType.value === '图片') {
        curTitle.value = '销售图片查看'
    }
    else {
        curTitle.value = '销售文件查看'
    }
}
const onUpdateFileList = async (list) => {
    if (curSelFileType.value === '图片') {
        saleData.file_list = [...list]
    }
    else {
        saleData.hetong_list = [...list]
    }
    console.log(saleData)
}
</script>

<template>
    <ContentDetailWrap :title="title + '-' + saleData.fsm_cur_state" @back="baskFrontNoFreshFront()">
        <template #left>
            <ElButton type="warning" class="ml-5" @click="handleCheckHis">
                <Icon class="mr-0.5" icon="material-symbols:history" />
                任务历史
            </ElButton>
        </template>
        <template #right>
            <ElButton
                v-show="currentRoute.query.cmd != '审核' && currentRoute.query.type != 'info' && saleData.fsm_can_trig_data.操作触发.includes('保存')"
                color="#409EFF" style="color: white;" @click="handleCheck('保存')">
                <Icon class="mr-0.5" icon="carbon:save" />
                保存
            </ElButton>
            <el-popconfirm title="是否确认提交审核?" @confirm="handleCheck('提交审核')">
                <template #reference>
                    <ElButton
                        v-show="currentRoute.query.cmd != '审核' && saleData.fsm_can_trig_data.操作触发.includes('提交审核')"
                        type="success">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        提交审核
                    </ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm title="是否确认关闭订单?" @confirm="handleCheck('关闭')" v-if="checkPermissionApi('销售显示关闭订单')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && saleData.fsm_can_trig_data.操作触发.includes('关闭')"
                        type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        关闭订单
                    </ElButton>
                </template>
            </el-popconfirm>
            <el-popconfirm title="是否恢复已关闭的订单?" @confirm="handleCheck('恢复')" v-if="checkPermissionApi('销售显示关闭订单')">
                <template #reference>
                    <ElButton v-show="currentRoute.query.cmd != '审核' && saleData.fsm_can_trig_data.操作触发.includes('恢复')"
                        type="danger">
                        <Icon class="mr-0.5" icon="carbon:close-outline" />
                        恢复订单
                    </ElButton>
                </template>
            </el-popconfirm>

            <el-popconfirm title="是否撤回审核?" @confirm="handleCheck('驳回')">
                <template #reference>
                    <ElButton
                        v-show="currentRoute.query.cmd != '审核' && saleData.fsm_can_trig_data.审核触发.includes('驳回')"
                        type="danger">
                        <Icon class="mr-0.5" icon="mingcute:send-line" />
                        撤回提交审核
                    </ElButton>
                </template>
            </el-popconfirm>

        </template>


        <el-form :rules="rules" :model="saleData" ref="ruleFormRef">
            <el-descriptions class="flex-1 mt-2" :column="3" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle1"
                    :label="t('customer.prop_cus')" class="flex">
                    <el-form-item prop="buyer_nick">
                        <div class="rounded mr-2 border pl-2 pr-2 min-w-150px max-w-[76%]" style="color: #606266;">
                            {{ checkPermissionApi('客户名称显示') ? (saleData.buyer_nick ? saleData.buyer_nick : '请选择客户') : '***' }}
                        </div>
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelCustom">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('sale.name')" class="flex">
                    <el-form-item prop="name">
                        <div class="flex">
                            <el-input v-model="saleData.sell_order_num" :disabled="saleData.id != undefined" />
                            <ElButton class="ml-2" v-if="saleData.id == undefined" type="primary" @click="onChangeID">
                                <Icon class="mr-0.5" icon="radix-icons:update" />
                                {{ t('button.update') }}
                            </ElButton>
                        </div>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle"
                    :label="t('sale.cutome_def_id')" :span="2" class="flex">
                    <el-form-item prop="name">
                        <el-input v-model="saleData.diy_order_num" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1"
                    :label="t('sale.mondyType')" class="flex">
                    <el-select v-model="saleData.money_type" placeholder="Select">
                        <el-option v-for="item in moneyTypeData" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('sale.saler')"
                    class="flex">
                    <el-form-item>
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
                            {{ saleData.sell_man_name ? saleData.sell_man_name : '请选择销售人员' }}
                        </div>
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelSale">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1"
                    :label="t('sale.follower')" :span="2" class="flex">
                    <el-form-item>
                        <div class="rounded mr-2 border pl-2 pr-2 w-150px" style="color: #606266;">
                            {{ saleData.follow_man_name ? saleData.follow_man_name : '请选择跟单人员' }}
                        </div>
                        <ElButton v-if="currentRoute.query.type != 'info'" @click="onSelFollower">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="'销售部门'"
                    class="flex">
                    <el-form-item>
                        <el-tree-select v-model="saleData.sell_dept_id" :data="deptData" check-strictly
                            :render-after-expand="false" :props="treeProps">
                            <template #default="scope">
                                <Icon icon="bx:category" />
                                {{ scope.data.name }}
                            </template>
                        </el-tree-select>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1"
                    :label="t('purchase.business_date')" class="flex">
                    <el-form-item>
                        <el-checkbox style="margin-right: 10px;" v-model="sameDay" label="统一" size="large" />
                        <el-date-picker v-if="sameDay" v-model="saleData.delivery_date" type="date" placeholder=""
                            format="YYYY/MM/DD" value-format="YYYY-MM-DD" @update:model-value="onChangeDeliverData"
                            :clearable="false" />
                    </el-form-item>
                </el-descriptions-item>

                <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" label="销售日期" class="flex">
                    <el-form-item>
                        <el-date-picker v-model="saleData.create_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" :clearable="false" />
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item v-if="saleData.order_type!='样品单'" label-class-name='labelStyle require' class-name="conentStyle1" label="成本资料"
                    class="flex">
                    <el-form-item prop="hetong_list">
                        <div class="rounded mr-2 border pl-2 pr-2 min-w-150px max-w-[76%]" style="color: #606266;">
                            {{ saleData.hetong_list.length }}个文件
                        </div>
                        <ElButton v-if="currentRoute.query.type != 'info' && checkPermissionApi('销售文件查看')"
                            @click="onShowFileList('文件')">
                            <Icon icon="iconamoon:search-bold" />
                        </ElButton>
                    </el-form-item>
                </el-descriptions-item>


                <!-- <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle1" :label="t('sale.same_date')"
                    class="flex">
                    <el-form-item >
                        <el-checkbox v-model="is_same_delivery_BOOL"  size="large"  class="!mr-3" @change="onChangeDeliveryTime"/>
                        <el-date-picker v-if="is_same_delivery_BOOL" v-model="purchaseData.delivery_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-form-item>
                </el-descriptions-item> -->
            </el-descriptions>

        </el-form>

        <el-table class="mt-4" header-cell-class-name="tableHeader" cell-class-name="table_cell"
            :data="saleData.pdt_list" style="width: 100%" border stripe>
            <el-table-column :label="t('process.opt')" width="60">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined && currentRoute.query.type != 'info'" type="primary">
                        <el-popconfirm title="是否确认删除?" @confirm="onDelPdt(scope.$index)">
                            <template #reference>
                                <Icon icon="material-symbols:delete-outline" class=" cursor-pointer"
                                    style="scale: 1.5; color: red;" />
                            </template>
                        </el-popconfirm>

                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="id" :label="t('userTable.id')" width="60" />
            <el-table-column :label="t('sale.img')" width="80">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex items-start w-[60px] h-[60px]">
                        <el-image v-if="scope.row.pics.length > 0" class="object-fill w-[60px] h-[60px] min-w-[60px]"
                            :src="scope.row.pics[0].url" />
                        <el-image v-if="scope.row.pics.length <= 0" class="object-fill w-[60px] h-[60px] min-w-[60px]"
                            src="/nopic.jpg" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="name" :label="t('product_manage.id')" max-width="140" />
            <el-table-column show-overflow-tooltip prop="nick" :label="t('product_manage.name')" min-width="160">
                <template #default="scope">
                    <div class="whitespace-normal">{{ scope.row.nick }}</div>
                    <el-input class="input-search" @keydown='onKeyDownOnePdt' @keyup='bKeyDown = false'
                        v-model="txtSearch" placeholder="请点击输入搜索" @input="onInputSearch" @click="onClickSearchPdt"
                        v-if="scope.row.nick == '' || scope.row.nick == undefined">{{ scope.row.id }}</el-input>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="nick" :label="t('sale.material')">
                <template #default="scope">
                    <el-input v-if="scope.row.id != undefined" v-model="scope.row.材质" />
                </template>
            </el-table-column>
            <!-- <el-table-column show-overflow-tooltip  prop="nick" :label="t('sale.type')">
                <template #default="scope">
                    <el-select v-if="scope.row.id != undefined"   v-model="scope.row.类型" placeholder="Select" @change="onChangeType(scope.row)">
                        <el-option  v-for="item in ['正常','备品','赠品','样品']" :key="item" :label="item" :value="item" />
                    </el-select>
                </template>
            </el-table-column> -->
            <el-table-column :label="t('sale.specs')">
                <template #default="scope">
                    <el-tooltip v-if="scope.row.id != undefined && scope.row.specs != ''" class="box-item" effect="dark"
                        :content="scope.row.specs_text" placement="bottom">
                        <el-tag>{{ scope.row.specs_name == '自定义规格' ? scope.row.specs_text : scope.row.specs_name }}</el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip :label="t('sale.inventory')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="!text-left font-bold" style="font-size: smaller;">
                        <div>{{ '库存:' + scope.row.库存 }} </div>
                        <div>{{ '待发:' + scope.row.待发 }} </div>
                        <div>{{ '可用:' + scope.row.可用 }} </div>
                    </div>
                    <div v-if="saleData.pdt_list.length > 1 && scope.row.id == undefined" class=" font-bold">
                        数量合计:
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip :label="t('sale.count')" width="140">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class="flex justify-center items-center">
                        <el-input v-model="scope.row.销售数量" class="!text-center mr-1 !w-[80%] flex"
                            @blur="reComputePdtInfo(scope.row, '销售数量')" type="number" />
                        <div style="font-size: smaller;">({{ scope.row.base_unit }})</div>
                    </div>
                    <div v-if="saleData.pdt_list.length > 1 && scope.row.id == undefined">
                        {{ totleSale }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="nick" :label="t('sale.bak_count')">
                <template #default="scope">
                    <el-input :disabled="scope.row.类型 != '正常'" v-if="scope.row.id != undefined" v-model="scope.row.销售备品数量"
                        @blur="reComputePdtInfo(scope.row, '销售备品数量')" type="number" />
                </template>
            </el-table-column>
            <el-table-column prop="delivery" :label="t('sale.delivery')" min-width="150">
                <template #default="scope">
                    <div class="relative w-[100%]">
                        <el-date-picker :disabled="saleData.delivery_date != ''"
                            class=" absolute left-0 top-0 !w-[100%]" v-if="scope.row.id != undefined"
                            v-model="scope.row.交货日期" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" :clearable="false" />
                        <!-- <div class="absolute left-0 top-0 w-[100%] pointer-events-none">{{ scope.row.交货日期 }}</div> -->

                    </div>
                </template>
            </el-table-column>

            <el-table-column :label="t('sale.bef_tax')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input :disabled="scope.row.类型 != '正常'" v-model="scope.row.sell_price_bef_tax"
                            class="!text-center" @blur="reComputePdtInfo(scope.row, '税前单价')" type="number"
                            v-show="checkPermissionApi('销售订单价格显示')" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.after_tax')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input :disabled="scope.row.类型 != '正常'" v-model="scope.row.sell_price_aft_tax"
                            class="!text-center" @blur="reComputePdtInfo(scope.row, '税后单价')" type="number"
                            v-show="checkPermissionApi('销售订单价格显示')" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.large_mod_cost')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input :disabled="scope.row.类型 != '正常'" v-model="scope.row.大模具费" class="!text-center"
                            @blur="reComputePdtInfo(scope.row, '大模具费')" type="number" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.tax_rate')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined" class1="flex">
                        <el-select v-if="scope.row.id != undefined" v-model="scope.row.发票类型" placeholder="请选择"
                            size="small" @change="reComputePdtInfo(scope.row, '发票税率')">
                            <el-option v-for="item in ['普票', '专票', '收据', '不开票', '电子票']" :key="item" :label="item"
                                :value="item" />
                        </el-select>
                        <div class="flex mt-1" v-if="['普票', '专票', '电子票'].includes(scope.row.发票类型)">
                            <el-select v-if="scope.row.id != undefined" v-model="scope.row.发票税率" placeholder="Select"
                                size="small" @change="reComputePdtInfo(scope.row, '发票税率')">
                                <el-option v-for="item in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]"
                                    :key="item" :label="item" :value="item" />
                            </el-select>
                            %
                        </div>
                    </div>
                    <div v-if="saleData.pdt_list.length > 1 && scope.row.id == undefined" class=" font-bold">
                        总价合计:
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.totle_price')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.总价" class="!text-center" @blur="reComputePdtInfo(scope.row, '总价')"
                            type="number" v-show="checkPermissionApi('销售订单价格显示')" />
                    </div>
                    <div v-if="saleData.pdt_list.length > 1 && scope.row.id == undefined"
                        v-show="checkPermissionApi('销售订单价格显示')">
                        {{ totleSalePrice }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.remark')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.销售备注" class="!text-center" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="'印刷厂'">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.印刷厂" class="!text-center" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="'印刷位'">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        <el-input v-model="scope.row.印刷位" class="!text-center" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="t('sale.his_price')">
                <template #default="scope">
                    <div v-if="scope.row.id != undefined">
                        {{ scope.row.历史价格 }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div v-if="showProductSel"
            class=" bg-light-600 w-[90%] max-h-[400px] absolute z-99 p-1 shadow el-table-container">
            <el-table ref="searchRef" :data="searchPdtData" style="width: 100%;" row-key="id" border
                highlight-current-row @current-change="handleCurrentSelectPdt" header-cell-class-name="tableHeader"
                height="300" @row-click='onRowClick'>
                <el-table-column show-overflow-tooltip fixed prop="name" :label="t('product_manage.id')" width="130" />
                <el-table-column show-overflow-tooltip fixed prop="nick" :label="t('product_manage.name')"
                    width="600" />
                <el-table-column show-overflow-tooltip fixed prop="brand" :label="t('product_manage.brand')" />
                <el-table-column show-overflow-tooltip fixed prop="specs_text"
                    :label="t('product_manage.specify_info')" />
                <el-table-column show-overflow-tooltip fixed prop="nick_brif" :label="t('product_manage.short_name')" />
                <el-table-column show-overflow-tooltip fixed prop="note" :label="t('product_manage.help_name')" />
            </el-table>
        </div>
        <!-- 显示合计 -->
        <div class="flex mt-4">
            <!-- 左边部分 -->
            <div class="w-[60%] text-center">
                <div class="flex">
                    <table class="table-auto border-collapse table_self w-[100%]">
                        <tr>
                            <td class="table_self_title min-w-[100px] p-2">收货人:</td>
                            <td class="table_self !w-[100%] p-3 justify-start text-left">
                                <el-select v-model="saleData.receive_man_id" placeholder="请选择" class="w-[100%]"
                                    :no-data-text="saleData.buyer_nick ? saleData.buyer_nick : '请先选择客户'">
                                    <el-option v-for="item in receive_users" :key="item.phone"
                                        :label="item.name + ':' + item.phone" :value="item.name + ':' + item.phone" />
                                </el-select>
                            </td>
                        </tr>
                        <tr>
                            <td class="table_self_title min-w-[100px] p-2 require">付款方式:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-select v-model="saleData.pay_type" placeholder="请选择" class="w-[100%]">
                                    <el-option v-for="item in payTypeData" :key="item" :label="item" :value="item" />
                                </el-select>
                            </td>
                        </tr>
                        <tr>
                            <td class="table_self_title min-w-[100px] p-2">送货方式:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-select v-model="saleData.express_type" placeholder="请选择" class="w-[100%]">
                                    <el-option v-for="item in ['快递', '物流', '送货', '自提']" :key="item" :label="item"
                                        :value="item" />
                                </el-select>
                            </td>
                        </tr>
                        <tr>
                            <td class="table_self_title min-w-[100px] p-2">包装方式:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-input v-model="saleData.pack_type" />
                            </td>
                        </tr>
                        <tr>
                            <td class="table_self_title min-w-[100px] p-2">备注:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-input v-model="saleData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }"
                                    type="textarea" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="flex-grow text-center">
                <div class="flex">
                    <div class="w-[100%]">
                        <div>
                            <td class="table_self_title min-w-[100px] p-2 ">合计:</td>
                            <td class="table_self !w-[100%] p-3">
                                {{ checkPermissionApi('销售订单价格显示') ? totleSalePrice : '*' }}
                            </td>
                        </div>
                        <div>
                            <td class="table_self_title min-w-[100px] p-2">运费:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-input v-model="saleData.express_fee" type="number"
                                    v-show="checkPermissionApi('销售订单价格显示')" />
                            </td>
                        </div>
                        <div>
                            <td class="table_self_title min-w-[100px] p-2">其他费用:</td>
                            <td class="table_self !w-[100%] p-3">
                                <el-input v-model="saleData.other_fee" type="number"
                                    v-show="checkPermissionApi('销售订单价格显示')" />
                            </td>
                        </div>
                        <div>
                            <td class="table_self_title min-w-[100px] p-2 ">合计金额:</td>
                            <td class="table_self !w-[100%] p-3">
                                {{ checkPermissionApi('销售订单价格显示') ? totleSalePriceAll : '*' }}
                            </td>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-card id="check" shadow="never"
            v-if="saleData.fsm_can_trig_data.审核触发.length > 0 && currentRoute.query.cmd == '审核'" class="w-[100%] mt-4">
            <!-- <template #header>
            <div class="flex items-center">
                <span>当前节点:</span>
                <span class="text-red-500 mr-3">{{ saleData.fsm_cur_state }}</span>                
            </div>
        </template> -->
            审核原因：
            <el-input v-model="saleData.fsm_exe_log" class="mt-3" :autosize="{ minRows: 5, maxRows: 2 }"
                type="textarea" />
            <div class="mt-4 flex justify-end">
                <div class="flex items-center text-12px">
                    <span>当前节点:</span>
                    <span class="text-red-500 mr-3">{{ saleData.fsm_cur_state }}</span>
                </div>
                <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('同意')" type="success"
                    @click="handleCheck('同意')">同意
                </ElButton>
                <el-popconfirm title="是否驳回该订单?" @confirm="handleCheck('驳回')">
                    <template #reference>
                        <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('驳回')" type="warning">驳回</ElButton>
                    </template>
                </el-popconfirm>
                <el-popconfirm title="是否拒绝该订单?" @confirm="handleCheck('拒绝')">
                    <template #reference>
                        <ElButton v-show="saleData.fsm_can_trig_data.审核触发.includes('拒绝')" type="danger">拒绝</ElButton>
                    </template>
                </el-popconfirm>
            </div>
        </el-card>

        <!-- 选择客户弹窗 -->
        <DialogSelCustomer v-model:show="showSelCusDlg" :title="'选择客户'" @on-submit="onSelCusCallback" />
        <!-- 选择销售 -->
        <DialogUser :param="''" v-model:show="showSelSaleUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelSaleCallback" />
        <!-- 选择跟单员 -->
        <DialogUser :param="''" v-model:show="showSelFollowerUserDlg" :title="t('msg.selectUser')"
            @on-submit="onSelFollowerCallback" />
        <!-- 显示任务历史记录 -->
        <DialogCheckShow v-model:show="showCheckHisDlg" :checklist="saleData.fsm_log_list" />

        <DialogFileList :path="'file/xs/' + saleData.sell_order_num + '/'" v-model:show="showFileList"
            :files="saleData.hetong_list" @on-update="onUpdateFileList" :type="curSelFileType" :title="curTitle" />
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% !important;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader) {
    color: #333;
    // white-space: nowrap;
    text-align: center;
    font-weight: 500;
    font-size: 13px;
}

//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}


//#ebeef5
//下半部分表格
.table_self {
    border-width: 1px;
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--el-border-color-lighter);
    padding: 7px;
}

//下半部分表格标题
.table_self_title {
    &:extend(.table_self);
    background-color: #f5f7fa;
    font-weight: 700;
    font-size: 13px;
    color: var(--el-text-color-regular);
}

//下半部分输入框文字
:deep(.table_self .el-input__inner) {
    text-align: center;
    font-size: 13px;
}

:deep(.infomode) {
    border: none;
    /* 设置边框为 none */
    border-radius: 0;
    /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none;
    /* 如果有阴影，也可以设置为 none */
}

:deep(.input-search) {
    .el-input__inner {
        text-align: start;
    }
}
</style>