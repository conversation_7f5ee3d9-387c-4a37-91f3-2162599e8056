<script setup lang="ts">
import { reactive, watch } from 'vue';
import { ElTable,ElTableColumn,ElCard, ElButton, ElForm, ElCollapse, ElCollapseItem, ElTag, ElTooltip, ElRadio, ElRadioGroup, ElSelect, ElOption, ElMessage, ElInput, FormInstance, FormRules, ElInputNumber } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted, ref } from 'vue';
import { DialogPreProcessSel } from '@/components/DialogPreProcessSel';
import { DialogProductSel } from '@/components/DialogProductSel'
import { getGUID } from '@/api/tool';
import { updatePreSaleApi, getPreSaleInfoApi } from '@/api/product'
import { useRouter } from 'vue-router'


const { currentRoute, back } = useRouter()
const { t } = useI18n()
//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
  buyer_name: [{ required: true, message: t('msg.noCusID'), trigger: 'blur' }],
  buyer_nick: [{ required: true, message: t('msg.noCusName'), trigger: 'blur' }],
})

const activeItems = ref(['1', '2', '3', '4'])

//产品数据源
//单个产品定义
const props = defineProps<{
  capmode: boolean  //快照模式
  pdt: any,
  fsm_cur_state: string //当前状态
}>()

const emit = defineEmits(['onUpdate'])

const pdtData = reactive(props.pdt)



watch(pdtData, () => {
  emit('onUpdate', pdtData);
})

watch(() => props.fsm_cur_state, () => {
  if (['等待确认', '已确认', '等待审核'].includes(props.fsm_cur_state) || props.capmode) {
    mode.value = '查看'
  }
  else {
    mode.value = '编辑'
  }
})




//显示隐藏工序选择窗口
const showSelProcess = ref(false)
const arrayDirData = reactive([])
const onSelProcess = (array,type)=>{
  showSelProcess.value = true
  arrayDirData.splice(0, arrayDirData.length,...array)
  callType.value = type
}



//选择工序回调
const onSelProcessCallback = (value,type) => {
  console.log(value)
  for(let one of value){
    addItem(type,one.名称)
  }
  // // //增加产品数组
  // value.pdt_list = []
  // value.用量 = 1
  // value.合计 = 0
  // // //追加选择的工序到BOM列表
  // pdtData.process_list.push(value)
  // console.log(value)

  // //更新合计
  // recomputeCount(value)
}

//删除工序
const onDelProcess = (one)=>{
  console.log(one)
  pdtData.process_list.splice(one, 1)
}



//更新工序合计
const recomputeCount = (item) => {
  //工序自己的小计+所有产品的小计

  //计算所有产品的总价
  let nTotle = 0
  for (let pdt of item.pdt_list) {
    nTotle += pdt.单价 * pdt.用量
  }
  nTotle += item.用量 * item.job_fee

  item.合计 = nTotle


  //重新计算总成本
  onReCountTotlePrice()
}

//计算总成本
const onReCountTotlePrice = () => {
  let nTotle = 0
//   for (let one of pdtData.process_list) {
//     nTotle = nTotle + one.合计
//   }

    for(let one of pdtData.主体){
      nTotle = nTotle + one.单价 * one.单个主体所需量
    }
    for(let one of pdtData.其他工艺){
      nTotle = nTotle + one.单价 * one.单个主体所需量
    }
    for(let one of pdtData.包装及其他){
      nTotle = nTotle + one.单价 * one.单个主体所需量
    }
    //加上其他金额

  pdtData.other_data.出厂合计成本 = nTotle.toFixed(2)  //不计算其他模具费和打样费  //+parseFloat(pdtData.other_data.打样费)+parseFloat(pdtData.other_data.大货模具费)+parseFloat(pdtData.other_data.加急大货模具费)
  console.log('出厂合计成本',pdtData.other_data.出厂合计成本)
}

const lastSelProcess = ref({})

//显示隐藏产品弹窗
const showSelProductDlg = ref(false)
//显示选择产品对话框
const onAddPdt = (item) => {
  showSelProductDlg.value = true
  lastSelProcess.value = item
}
//选择产品完毕回调
const onSelProductCallback = (pdt) => {
  console.log('---', pdt)
  lastSelProcess.value.pdt_list.push({
    pdt_id: pdt.id,
    name: pdt.name,
    nick: pdt.nick,
    specs_name: pdt.specs_name,
    specs_text: pdt.specs_text,
    base_unit: pdt.base_unit,
    单价: pdt.单价,
    用量: 1,
    总价: 0,
    排序: 0,
    供应商: '',
    remark: '',
    sell_buy_digit: pdt.sell_buy_digit,
    标识: getGUID(10)
  })
  console.log(pdtData)

  recomputeCount(lastSelProcess.value)
}

//删除工序下产品
const onDelPdt = (one, pdt) => {
  for (let item of pdtData.process_list) {
    if (item.id == one.id) {
      for (let p of item.pdt_list) {
        if (p.标识 == pdt.标识) {
          item.pdt_list.splice(item.pdt_list.indexOf(p), 1)
          recomputeCount(item)
          break
        }
      }
      break
    }
  }
}


//提交出厂报价
const onSubmitBasePrice = async () => {

  //修改数字类型
  pdtData.other_data.出厂合计成本 = parseFloat(pdtData.other_data.出厂合计成本)
  pdtData.status = t('status.wait_final_price')
  const ret = await updatePreSaleApi(pdtData)
  if (ret) {
    //提示提交成功！
    ElMessage.success(t('msg.submitBasePriceSuccess'))
  }
  back()
}


const mode = ref('编辑')
const buttons = ref([]);
const inputs = ref([]);

// 主体
type ZTItem = {
  名称: string;
  材质: string;
  单位: string;
  单价: number;
  单个主体所需量: number;
  小计: number;
  备注: string;
}
const arrayZT = ref<ZTItem[]>([])

// 其他工艺
type QTItem = {
  名称: string,
  颜色: string,
  印刷位:string,
  单价: number,
  单个主体所需量: number,
  小计: number,
  备注: string,
}
const arrayQT = ref<QTItem[]>([])


// 包装及其他
type BZItem = {
  名称: string,
  材质: string,
  单位: string,
  单价: number,
  单个主体所需量: number,
  小计: number,
  备注: string,
} 
const arrayBZ = ref<BZItem[]>([])

onMounted(async () => {
  //申报单进来的只允许查看
  if (currentRoute.value.query.type == 'info') {
    mode.value = '查看'
  }

  if (currentRoute.value.query.mode == '报价审核') {
    mode.value = '查看'
  }

  if (['等待确认', '已确认'].includes(props.fsm_cur_state)) {
    mode.value = '查看'
  }

  if (props.capmode) {
    mode.value = '查看'
  }
  //初始化主体
  for(let one of ['主体','包装','组装']){
    arrayZT.value.push(
      {
          名称: one,
          材质: '',
          单位: '',
          单价: 0,
          单个主体所需量: 1,
          小计: 0,
          备注: '',
      }
    )
  }
  //初始化其他工艺
  for(let one of ['印刷', '丝印', '喷油', '喷光油', '手感油', '水贴', '黏胶', '电镀']){
    arrayQT.value.push(
      {
          名称: one,
          颜色: '',
          印刷位:'',
          单价: 0,
          单个主体所需量: 1,
          小计: 0,
          备注: '',
      }
    )
  }
  //初始化包装及其他
  for(let one of ['彩盒', '铝膜袋', '吸塑', '纸卡', 'OPP袋', 'PE袋', '内盒', '纸箱', '包材', '3M胶', '圆形贴', '内外纸箱/隔板/刀卡', '小纸卡', '五金', '泡棉']){
    arrayBZ.value.push(
      {
          名称: one,
          材质: '',
          单位: '',
          单价: 0,
          单个主体所需量: 1,
          小计: 0,
          备注: '',
      }
    )
  }

})


//------------------------------
const addItem = (type,name='')=>{
    if (type == '主体') {
      pdtData.主体.push({
        名称: name==''?'':name,
        材质: '',
        单位: '',
        单价: 0,
        单个主体所需量: 1,
        小计: 0,
        备注: '',
      })    
    }
    else if(type =='其他工艺'){
      pdtData.其他工艺.push({
        名称: name==''?'':name,
        颜色: '',
        印刷位:'',
        单价: 0,
        单个主体所需量: 1,
        小计: 0,
        备注: '',
      })
    }
    else if(type =='包装及其他'){
      pdtData.包装及其他.push({
        名称: name==''?'':name,
        材质: '',
        单位: '',
        单价: 0,
        单个主体所需量: 1,
        小计: 0,
        备注: '',
      })
    }
}
//修改计算金额
const changeValue = (type,row)=>{
    row.小计 = row.单价 * row.单个主体所需量
    //重新计算总成本
    onReCountTotlePrice()
}
//删除某一行
const onDelItem = (src,row)=>{
    src.splice(src.indexOf(row), 1)
    //重新计算总成本
    onReCountTotlePrice()
}
//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if(['小计','单个主体所需量'].includes(column.property))
        {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                sums[index] = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const callType = ref('')
</script>

<template>
  <div>
    <el-form :rules="rules" :model="pdtData" ref="ruleFormRef">
     
    <el-card >
        <template #header>
        <div class="card-header flex items-center" style="height:5px; line-height: 5px;">
                 <div class="title ">主体</div>
                <div class="w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                    <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="onSelProcess(arrayZT,'主体')">
                    <Icon icon="material-symbols-light:select-window" />
                    </ElButton>
                </div>
                <div class="ml-5 w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                    <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="addItem('主体')">
                    <Icon icon="material-symbols:add" />
                    </ElButton>
                </div>
            </div>
        </template>
        <div>
            <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="pdtData.主体" style="width: 100%;color: #666666;"  border stripe show-summary :summary-method="getSummaries">
              <el-table-column  align="center"  prop="分件品名" label="分件品名" >
                <template #default="scope">
                  <el-input v-model="scope.row.名称" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="材质" label="材质" >
                <template #default="scope">
                  <el-input v-model="scope.row.材质" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="单位" label="单位" >
                <template #default="scope">
                  <el-input v-model="scope.row.单位" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column align="center"   prop="单价" label="单价" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false"  v-model="scope.row.单价" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template>
              </el-table-column>
              <el-table-column   align="center" prop="单个主体所需量" label="单个主体所需量" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false" v-model="scope.row.单个主体所需量" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template> 
              </el-table-column>
              <el-table-column   align="center"  prop="小计" label="小计" />
              <el-table-column    prop="备注" label="备注" >
                <template #default="scope">
                  <el-input v-model="scope.row.备注" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
            <el-table-column   align="center"  prop="操作" label="操作" >
                <template #default="scope">
                 <ElButton plain type="danger" size="small" @click="onDelItem(pdtData.主体,scope.row)"
                    v-if="mode == '编辑'">
                    <Icon icon="material-symbols:delete-outline" />
                  </ElButton>
                </template>
              </el-table-column>
            </el-table>
          </div>
    </el-card>

    <el-card class="mt-5">
        <template #header>
        <div class="card-header flex items-center" style="height:5px; line-height: 5px;">
                 <div class="title ">其他工艺</div>
                <div class="w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                    <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="onSelProcess(arrayQT,'其他工艺')">
                    <Icon icon="material-symbols-light:select-window" />
                    </ElButton>
                </div>
                <div class="ml-5 w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                    <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="addItem('其他工艺')">
                    <Icon icon="material-symbols:add" />
                    </ElButton>
                </div>
            </div>
        </template>
        <div>
            <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="pdtData.其他工艺" style="width: 100%;color: #666666;"  border stripe show-summary :summary-method="getSummaries">
              <el-table-column align="center"   prop="工艺名称" label="工艺名称" >
                <template #default="scope">
                  <el-input v-model="scope.row.名称" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="颜色" label="颜色" >
                <template #default="scope">
                  <el-input v-model="scope.row.颜色" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="印刷位" label="印刷位" >
                <template #default="scope">
                  <el-input v-model="scope.row.印刷位" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column   prop="单价" label="单价" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false"  v-model="scope.row.单价" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template>
              </el-table-column>
              <el-table-column  align="center" prop="单个主体所需量" label="单个主体所需量" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false" v-model="scope.row.单个主体所需量" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template> 
              </el-table-column>
              <el-table-column   align="center"  prop="小计" label="小计" />
              <el-table-column    prop="备注" label="备注" >
                <template #default="scope">
                  <el-input v-model="scope.row.备注" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
            <el-table-column   align="center"  prop="操作" label="操作" >
                <template #default="scope">
                 <ElButton plain type="danger" size="small" @click="onDelItem(pdtData.其他工艺,scope.row)"
                    v-if="mode == '编辑'">
                    <Icon icon="material-symbols:delete-outline" />
                  </ElButton>
                </template>
              </el-table-column>
            </el-table>
          </div>
    </el-card>
    <el-card class="mt-5">
        <template #header>
            <div class="card-header flex items-center" style="height:5px; line-height: 5px;">
                        <div class="title ">包装及其他</div>
                    <div class="w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                        <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="onSelProcess(arrayBZ,'包装及其他')">
                        <Icon icon="material-symbols-light:select-window" />
                        </ElButton>
                    </div>
                    <div class="ml-5 w-[20px] h-20px flex items-center"   v-if="mode == '编辑'">
                      <ElButton plain class="w-20px !h-20px ml-4" circle color="#409EFF"  @click.stop="addItem('包装及其他')">
                      <Icon icon="material-symbols:add" />
                      </ElButton>
                  </div>
                </div>
            </template>
        <div>
             <el-table ref="userTableRef11" header-cell-class-name="tableHeader" :data="pdtData.包装及其他" style="width: 100%;color: #666666;"  border stripe show-summary :summary-method="getSummaries">
              <el-table-column align="center"    prop="名称" label="名称" >
                <template #default="scope">
                  <el-input v-model="scope.row.名称" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="材质" label="材质" >
                <template #default="scope">
                  <el-input v-model="scope.row.材质" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column    prop="单位" label="单位" >
                <template #default="scope">
                  <el-input v-model="scope.row.单位" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
              <el-table-column align="center"   prop="单价" label="单价" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false"  v-model="scope.row.单价" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template>
              </el-table-column>
              <el-table-column   align="center" prop="单个主体所需量" label="单个主体所需量" >
                <template #default="scope">
                  <ElInputNumber class="!w-[100%]" :min="0" :controls="false" v-model="scope.row.单个主体所需量" size="small" :disabled="mode!= '编辑'" @blur="changeValue('主体',scope.row)"/>
                </template> 
              </el-table-column>
              <el-table-column   align="center"  prop="小计" label="小计" />
              <el-table-column    prop="备注" label="备注" >
                <template #default="scope">
                  <el-input v-model="scope.row.备注" size="small" :disabled="mode!= '编辑'" />
                </template>
              </el-table-column>
                <el-table-column   align="center"  prop="操作" label="操作" >
                <template #default="scope">
                 <ElButton plain type="danger" size="small" @click="onDelItem(pdtData.包装及其他,scope.row)"
                    v-if="mode == '编辑'">
                    <Icon icon="material-symbols:delete-outline" />
                  </ElButton>
                </template>
              </el-table-column>
            </el-table>
        </div>
    </el-card>
      <div class="flex mt-4  font-bold">
        <div class="mr-4">出厂成本合计:</div>
        <div class="text-red-500 font-bold flex" v-if="mode == '编辑'">
          <el-input type="number" class="mr-5 totle" v-model="pdtData.other_data.出厂合计成本" size="small" :disabled="mode != '编辑'" />
          <ElButton type="primary" @click="onReCountTotlePrice" v-if="mode == '编辑'">重算</ElButton>
        </div>
        <div class="text-red-500 font-bold flex text-[25px]" v-if="mode != '编辑'">{{ pdtData.other_data.出厂合计成本.toFixed(2) }}</div>
      </div>
      <div class="flex mt-4  font-bold items-center">
        <div>是否含税:</div>
        <el-radio-group v-model="pdtData.other_data.是否含税" class="ml-4">
          <el-radio label="不含税" size="large">不含税</el-radio>
          <el-radio label="2含税" size="large">含税</el-radio>
        </el-radio-group>
        <div class="flex items-center">
          <div class="min-w-[50px] ml-5">税率:</div>
          <el-input type="number"  v-model="pdtData.other_data.税率" size="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <!-- 其他项目时间 -->
      <div class="flex header headerBk w-[100%] mt-5" style="color: #333;">
        <div class="min-w-[20%] text-center font-bold">项目</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">小计/天数</div>
        <div class="min-w-[30%] text-center">备注</div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">打样时间</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">
          <el-input v-model="pdtData.other_data.打样时间" size1="small" :disabled="mode != '编辑'" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.打样时间备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">开模时间</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">
          <el-input v-model="pdtData.other_data.开模时间" size1="small" :disabled="mode != '编辑'" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.开模时间备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">打样费/画图</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">
          <ElInputNumber :min="0" :controls="false" v-model="pdtData.other_data.打样费" size1="small" :disabled="mode != '编辑'" type="number"
            @input="onReCountTotlePrice" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.打样费备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center text-red-500 font-bold">大货模具费</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">
          <ElInputNumber :min="0" :controls="false" v-model="pdtData.other_data.大货模具费" size1="small" :disabled="mode != '编辑'" type="number"
            @input="onReCountTotlePrice" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.大货模具费备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">加急大货模具费</div>
        <div class="min-w-[40%] text-center min-h-7"></div>
        <div class="min-w-[10%] text-center">
          <ElInputNumber :min="0" :controls="false" v-model="pdtData.other_data.加急大货模具费" size1="small" :disabled="mode != '编辑'" type="number"
            @input="onReCountTotlePrice" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.加急大货模具费备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">报价有效期</div>
        <div class="min-w-[40%] text-center min-h-7 font-bold">此价格自报价之日起 {{ pdtData.other_data.报价有效期 }}天内有效</div>
        <div class="min-w-[10%] text-center">
          <el-input v-model="pdtData.other_data.报价有效期" size1="small" :disabled="mode != '编辑'" />
        </div>
        <div class="min-w-[30%] text-center">
          <el-input v-model="pdtData.other_data.报价有效期备注" size1="small" :disabled="mode != '编辑'" />
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">箱规(长X宽X高 单位cm)</div>
        <div class="min-w-[80%] flex">
          <div class="flex items-start ">
            <el-input class="!w-[100px] mr-1" size1="small" v-model="pdtData.other_data.长" :disabled="mode != '编辑'" type="number"/>
            <el-input class="!w-[100px] mr-1" size1="small" v-model="pdtData.other_data.宽" :disabled="mode != '编辑'" type="number"/>
            <el-input class="!w-[100px]" size1="small" v-model="pdtData.other_data.高" :disabled="mode != '编辑'" type="number"/>
          </div>
        </div>

      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">包装信息:</div>
        <div class="min-w-[80%] flex ">
          <div class="flex items-center ">
            <div class="w-[70px]">装箱数:</div>
            <el-input class="!w-[100px]" size1="small" v-model="pdtData.other_data.装箱数" :disabled="mode != '编辑'" />
          </div>
          <div class="flex items-center">
            <div class="w-[100px]">净重(单位KG):</div>
            <el-input class="!w-[100px]" size1="small" v-model="pdtData.other_data.净重" :disabled="mode != '编辑'" />
          </div>
          <div class="flex items-center">
            <div class="w-[100px]">毛重(单位KG):</div>
            <el-input class="!w-[100px]" size1="small" v-model="pdtData.other_data.毛重" :disabled="mode != '编辑'" />
          </div>
        </div>
      </div>
      <div class="flex content items-center">
        <div class="min-w-[20%] text-center font-bold">大货时间</div>
        <div class="min-w-[40%] text-center min-h-7 font-bold">
          <!-- <el-date-picker v-model="pdtData.other_data.生产日期" type="date" placeholder="" format="YYYY/MM/DD"
            value-format="YYYY-MM-DD" /> -->
            <el-input class="!w-[100px]" size1="small" v-model="pdtData.other_data.大货时间" :disabled="mode != '编辑'" type="number"/>
            <span class="ml-2">天</span>
        </div>
        <div class="min-w-[30%] text-center">

        </div>
      </div>
        <div class="flex content items-center">
          <div class="min-w-[20%] text-center font-bold text-red-500">交货周期</div>
          <div class="min-w-[80%] text-center min-h-7 font-bold">
            <!-- <el-date-picker v-model="pdtData.other_data.生产日期" type="date" placeholder="" format="YYYY/MM/DD"
              value-format="YYYY-MM-DD" /> -->
              <el-input class="!w-[100%]" size1="small" v-model="pdtData.other_data.交货周期" :disabled="mode != '编辑'" />
          </div>
          <!-- <div class="min-w-[30%] text-center">

          </div> -->
      </div>
      <!-- <div class="flex mt-10 justify-center">
          <ElButton type="primary" style="scale: 1.3;" @click="onSubmitBasePrice" v-if="mode == '编辑'">提交出厂报价</ElButton>
        </div> -->

      <!-- 选择工序弹窗 -->
      <!-- <DialogProcessSel v-model:show="showSelProcess" :title="t('bom.sel_process')" @on-submit="onSelProcessCallback" /> -->
      <!-- 产品选择弹窗 -->
      <DialogProductSel v-model:show="showSelProductDlg" :title="t('bom.sel_pdt')" @on-submit="onSelProductCallback" />
      <!-- 选择工序弹窗 -->
      <DialogPreProcessSel v-model:show="showSelProcess" :type="callType" :data="arrayDirData" :title="t('bom.sel_process')" @on-submit="onSelProcessCallback" />
      <div style="height: 10px;"></div>
    </el-form>
  </div>
</template>

<style lang="less" scoped>
//标题设置
.title {
  font-weight: 900;
  font-size: large;
  margin-left: 20px;
}

//标题前提示
.title::before {
  content: 'I';
  color: red;
  margin-right: 10px;
}

//展开控件标题颜色


.el-form-item--default {
  margin-bottom: unset;
}

.formItem {
  height: 100%;
  min-width: max-content;
  display: flex;
  align-items: center;
}

.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}

.headerBk {
  // background-color: RGBA(246, 246, 246, 1) !important;
  background-color: #fff !important;
  font-weight: bold;
}

.headerBK_specs {
  background-color: rgba(222, 226, 230, var(--tw-bg-opacity));
  background-color: #fafafa;
}

.headerBK_specs div {
  color: black;
}

.content {
  &:extend(.header);
  font-size: 14px;
  height: 40px
}

.header>div,
.content>div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px;
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  max-height: 90px;
  font-size: 13px;
  height: 40px;
  align-items: center;
}

.header>div:last-child,
.content>div:last-child {
  border-right: none;
}

:deep(.el-input__inner) {
  text-align: center;

}

:deep(.totle .el-input__inner) {
  text-align: center;
  font-weight: 900;
  color: red;
  font-size: 20px;
}


.el-input.is-disabled /deep/ .el-input__inner {
  color: #000000;
  background-color: white;
}

:deep(.el-input__wrapper) {
  padding-left: 1px;
  padding-right: 1px;
}

:deep(.el-input__prefix-inner)>:first-child, .el-input__prefix-inner>:first-child.el-input__icon {
    margin-left: 8px;
}
</style>