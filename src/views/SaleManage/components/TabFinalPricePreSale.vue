<script setup lang="ts">
import { reactive,watch } from 'vue';
import { ElInputNumber,ElTable,ElTableColumn,ElSelect,ElOption, ElMessage,ElInput, ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted,ref,computed } from 'vue';
import { updatePreSaleApi,getPreSaleInfoApi } from '@/api/product'
import { useRouter } from 'vue-router'
import { createRouter } from 'vue-router';

const { currentRoute,back } = useRouter()
const { t } = useI18n()

//单个产品定义
const props = defineProps<{
  capmode:boolean, //快照模式
  pdt: any,
  fsm_cur_state:string //当前状态
}>()

const emit = defineEmits(['onUpdate'])

//const pdtData = reactive(props.pdt)

//单子数据源
const pdtData = reactive(props.pdt)


watch(pdtData, () => {
  pdtData.other_data.销售单价不含税 = 销售单价不含税.value
  pdtData.other_data.销售单价含税 = 销售单价含税.value
  emit('onUpdate', pdtData);
})

watch(()=>props.fsm_cur_state,()=>{
  console.log('++++++++++++++++')
  if(['等待确认','已确认','等待审核'].includes(props.fsm_cur_state) || props.capmode)
  {
    mode.value = '查看'
  }
  else
  {
    mode.value = '编辑'
  }
})



const mode = ref('编辑')
onMounted(async()=>{
  // console.log('--------------------',props.pdt)
  Object.assign(pdtData,props.pdt)
  pdtData.other_data.出厂合计成本 = parseFloat(pdtData.other_data.出厂合计成本)

  if(currentRoute.value.query.mode == '报价审核' || currentRoute.value.query.type == 'info')
  {
    mode.value = '查看'
  }
  
  console.log('==========',props.fsm_cur_state)
  if(['等待确认','已确认','等待审核'].includes(props.fsm_cur_state))
  {
    mode.value = '查看'
  }

  if(props.capmode)
  {
    mode.value = '查看'
  }

  // const ret = await getPreSaleInfoApi({
  //           id:currentRoute.value.query.id,
  //           page:1,
  //           count:100
  //       })
  //   if(ret)
  //   {
  //       console.log(ret)
  //      // ret.data.other_data.出厂合计成本 = parseFloat(ret.data.other_data.出厂合计成本)
  //       Object.assign(pdtData, ret.data)        
  //   }
})

//计算销管费小计
const 销管费小计 = computed(()=>{
  return pdtData.other_data.运费单价*pdtData.other_data.运费数量+pdtData.other_data.其他费用单价*pdtData.other_data.其他费用数量+pdtData.other_data.出厂合计成本*pdtData.other_data.销管费数量/100
})

//总成本合计
const 总成本合计 = computed(()=>{
  return parseFloat((Number(pdtData.other_data.出厂合计成本)+销管费小计.value).toFixed(2))
})

//毛利率数
const 毛利率小计 = computed(()=>{
  return parseFloat(((Number(pdtData.other_data.出厂合计成本)+销管费小计.value)*pdtData.other_data.毛利率/100).toFixed(2))
})
//增值税
const 增值税小计 = computed(()=>{
  return parseFloat(((Number(pdtData.other_data.出厂合计成本)+销管费小计.value+毛利率小计.value)*pdtData.other_data.增值税率/100).toFixed(2))
})

//销售单价不含税
const 销售单价不含税 = computed(()=>{
  const value = parseFloat((Number(pdtData.other_data.出厂合计成本)+销管费小计.value+毛利率小计.value).toFixed(2))
  // console.log('pdtData.other_data.销售单价不含税',pdtData.other_data.销售单价不含税)
  return value
})
//销售单价含税
const 销售单价含税 = computed(()=>{
  const value = parseFloat((Number(pdtData.other_data.出厂合计成本)+销管费小计.value+毛利率小计.value+增值税小计.value).toFixed(2))
   console.log('pdtData.other_data.销售单价含税',pdtData.other_data.销售单价含税)
  return value
})

/* ====== ② 梯度常量 —— 折扣系数 + 毛利阈值 ====== */
const DISCOUNT_LIST = [1.02, 1.01, 1, 0.99, 0.98]
const PROFIT_THRESHOLD_LIST = [0.6, 0.6, 0.23, 0.23, 0.23] // 对应 60 %, 60 %, 23 %…

/* ====== ③ 各梯度含税报价（自动校正到阈值） ====== */
const 梯度报价含税List = computed(() => {
  const cost = Number(pdtData.other_data.出厂合计成本)
  const vat  = Number(pdtData.other_data.增值税率) / 100
  const basePrice = 销售单价含税.value

  return DISCOUNT_LIST.map((factor, idx) => {
    let price = parseFloat((basePrice * factor).toFixed(2))  // 先按正常折扣算
    const margin = 1 - cost / (price / (1 + vat))            // 实际毛利率

    const threshold = PROFIT_THRESHOLD_LIST[idx]             // 0.6 或 0.23
    if (margin < threshold) {                                // 低于阈值 ⇒ 反推
      const raw = cost * (1 + vat) / (1 - threshold)         // 精确价
      price = Math.ceil(raw * 100) / 100                     // **始终向上取到分**
    }
    return price
  })
})


/* ====== ④ 由校正后报价反算出的实际毛利率 / 销管费率 / 净利率 ====== */
const 实际毛利率List = computed(() => {
  const cost = Number(pdtData.other_data.出厂合计成本)
  const vat  = Number(pdtData.other_data.增值税率) / 100

  return 梯度报价含税List.value.map(price =>
    ((1 - cost / (price / (1 + vat))) * 100).toFixed(0) + '%'   // **toFixed(0)**
  )
})


const 销管费率List = computed(() => {
  const vat = Number(pdtData.other_data.增值税率) / 100
  const fee = 销管费小计.value

  return 梯度报价含税List.value.map(price =>
    ((fee / (price / (1 + vat)) * 100).toFixed(2))
  )
})

const 净利率List = computed(() => {
  const totalCost = 总成本合计.value
  const vat = Number(pdtData.other_data.增值税率) / 100

  return 梯度报价含税List.value.map(price =>
    ((1 - totalCost / (price / (1 + vat))) * 100).toFixed(2)
  )
})

/* ====== ⑤ 最终表格数据 ====== */
const tdData = reactive([
  {
    name: '梯度数据',
    param: ['1000', '1000-3000', '3000-5000', '5000-10000', '10000以上', '']
  },
  {
    name: '梯度折扣',
    param: ['102%', '101%', '100%', '99%', '98%', '']
  },
  {
    name: '梯度报价/元',
    param: [
      computed(() => 梯度报价含税List.value[0]),
      computed(() => 梯度报价含税List.value[1]),
      computed(() => 梯度报价含税List.value[2]),
      computed(() => 梯度报价含税List.value[3]),
      computed(() => 梯度报价含税List.value[4]),
      ''
    ]
  },
  {
    name: '实际毛利率',
    param: [
      computed(() => 实际毛利率List.value[0]),
      computed(() => 实际毛利率List.value[1]),
      computed(() => 实际毛利率List.value[2]),
      computed(() => 实际毛利率List.value[3]),
      computed(() => 实际毛利率List.value[4]),
      ''
    ]
  },
  {
    name: '销管费率',
    param: [
      computed(() => 销管费率List.value[0]),
      computed(() => 销管费率List.value[1]),
      computed(() => 销管费率List.value[2]),
      computed(() => 销管费率List.value[3]),
      computed(() => 销管费率List.value[4]),
      '不能低于10％'
    ]
  },
  {
    name: '净利率',
    param: [
      computed(() => 净利率List.value[0]),
      computed(() => 净利率List.value[1]),
      computed(() => 净利率List.value[2]),
      computed(() => 净利率List.value[3]),
      computed(() => 净利率List.value[4]),
      ''
    ]
  }
])


// //梯度数据
// const tdData = reactive([
//   {
//     name:'梯度数据',
//     param:['1000','1000-2000','2000-5000','5000-10000','10000以上','']
//   },
//   {
//     name:'梯度折扣',
//     param:['102%','101%','100%','99%','98%','']
//   },
//   {
//     name:'梯度报价/元',
//     param:[
//       computed(()=>{return parseFloat((销售单价含税.value*1.02).toFixed(2))}),
//       computed(()=>{return parseFloat((销售单价含税.value*1.01).toFixed(2))}),
//       computed(()=>{return parseFloat((销售单价含税.value*1).toFixed(2))}),
//       computed(()=>{return parseFloat((销售单价含税.value*0.99).toFixed(2))}),
//       computed(()=>{return parseFloat((销售单价含税.value*0.98).toFixed(2))}),
//       '']
//   },
//   {
//     name:'实际毛利率',
//     param:[
//       computed(()=>{return (parseFloat(((1-pdtData.other_data.出厂合计成本/(销售单价含税.value*1.02/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2)))+'%'}),
//       computed(()=>{return (parseFloat(((1-pdtData.other_data.出厂合计成本/(销售单价含税.value*1.01/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2)))+'%'}),
//       computed(()=>{return (parseFloat(((1-pdtData.other_data.出厂合计成本/(销售单价含税.value*1/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2)))+'%'}),
//       computed(()=>{return (parseFloat(((1-pdtData.other_data.出厂合计成本/(销售单价含税.value*0.99/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2)))+'%'}),
//       computed(()=>{return (parseFloat(((1-pdtData.other_data.出厂合计成本/(销售单价含税.value*0.98/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2)))+'%'}),
//       '']
//   },
//   {
//     name:'销管费率',
//     param:[
//       computed(()=>{return parseFloat((销管费小计.value/(销售单价含税.value*1.02/(1+pdtData.other_data.增值税率/100))*100).toFixed(2))}),
//       computed(()=>{return parseFloat((销管费小计.value/(销售单价含税.value*1.01/(1+pdtData.other_data.增值税率/100))*100).toFixed(2))}),
//       computed(()=>{return parseFloat((销管费小计.value/(销售单价含税.value*1/(1+pdtData.other_data.增值税率/100))*100).toFixed(2))}),
//       computed(()=>{return parseFloat((销管费小计.value/(销售单价含税.value*0.99/(1+pdtData.other_data.增值税率/100))*100).toFixed(2))}),
//       computed(()=>{return parseFloat((销管费小计.value/(销售单价含税.value*0.98/(1+pdtData.other_data.增值税率/100))*100).toFixed(2))}),
//       '不能低于10％']
//   },
//   {
//     name:'净利率',
//     param:[
//       computed(()=>{return parseFloat(((1-总成本合计.value/(销售单价含税.value*1.02/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2))}),
//       computed(()=>{return parseFloat(((1-总成本合计.value/(销售单价含税.value*1.01/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2))}),
//       computed(()=>{return parseFloat(((1-总成本合计.value/(销售单价含税.value*1/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2))}),
//       computed(()=>{return parseFloat(((1-总成本合计.value/(销售单价含税.value*0.99/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2))}),
//       computed(()=>{return parseFloat(((1-总成本合计.value/(销售单价含税.value*0.98/(1+pdtData.other_data.增值税率/100)))*100).toFixed(2))}),
//       '']
//   },
// ])

//设置阶梯列颜色
const getColClass = (index)=>{
  if(index ==0 && pdtData.pdt_amount<=1000)
  {
    return 'hightTxt'
  }
  else if(index == 1 && (pdtData.pdt_amount>1000&&pdtData.pdt_amount<=2000))
  {
    return 'hightTxt'
  }
  else if(index == 2 && (pdtData.pdt_amount>2000&&pdtData.pdt_amount<=5000))
  {
    return 'hightTxt'
  }
  else if(index == 3 && (pdtData.pdt_amount>5000&&pdtData.pdt_amount<=10000))
  {
    return 'hightTxt'
  }
  else if(index == 4 && (pdtData.pdt_amount>10000))
  {
    return 'hightTxt'
  }
  else
  {
    return 'normalTxt'
  }
}

</script>

<template>
  <div class="w-[100%]">
    <div class="flex header headerBk w-[100%] mt-5">              
        <div class="min-w-[40%]  font-bold">项目</div>
        <div class="min-w-[10%] ">单价</div>
        <div class="min-w-[15%] ">单个所需数量</div>
        <div class="min-w-[10%] ">小计</div>
        <div class="min-w-[25%] ">备注</div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%]  font-bold">运费</div>
      <div class="min-w-[10%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.运费单价" size1="small" :disabled="mode != '编辑'" type="number"/>
      </div>
      <div class="min-w-[15%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.运费数量" size1="small" :disabled="mode != '编辑'" type="number"/>
      </div>
      <div class="min-w-[10%]  font-bold">{{ pdtData.other_data.运费单价*pdtData.other_data.运费数量 }}</div>
      <div class="min-w-[25%] ">
        <el-input  v-model="pdtData.other_data.运费备注" size1="small" :disabled="mode != '编辑'"/>
      </div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%]  font-bold">其他费用</div>
      <div class="min-w-[10%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.其他费用单价" size1="small" :disabled="mode != '编辑'" type="number"/>
      </div>
      <div class="min-w-[15%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.其他费用数量" size1="small" :disabled="mode != '编辑'" type="number"/>
      </div>
      <div class="min-w-[10%]  font-bold">{{ pdtData.other_data.其他费用单价*pdtData.other_data.其他费用数量 }}</div>
      <div class="min-w-[25%] ">
        <el-input  v-model="pdtData.other_data.其他费用备注" size1="small" :disabled="mode != '编辑'"/>
      </div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%]  font-bold">销管费</div>
      <div class="min-w-[10%] ">
        
      </div>
      <div class="min-w-[15%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.销管费数量" size1="small" class="!w-[50%]" :disabled="mode != '编辑'" type="number"/>%
      </div>
      <div class="min-w-[10%]  font-bold">{{ pdtData.other_data.出厂合计成本*pdtData.other_data.销管费数量/100 }}</div>
      <div class="min-w-[25%] ">
        <!-- <el-input  v-model="pdtData.other_data.销管费备注" size1="small" :disabled="mode != '编辑'"/> -->
        低于9%需要提交审核
      </div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%] font-bold  text-black !pl-[20px]" style="justify-content: left; font-size: 16px">销管费合计</div>
      <div class="min-w-[10%] "></div>
      <div class="min-w-[15%] "></div>
      <div class="min-w-[10%]  font-bold">{{ 销管费小计 }}</div>
      <div class="min-w-[25%] "></div>
    </div>

    <!-- 总成本合计 -->
    <div class="flex content items-center mt-2">              
      <div class="min-w-[40%] font-bold  text-black !pl-[20px]" style="justify-content: left; font-size: 16px">总成本合计</div>
      <div class="min-w-[10%] "></div>
      <div class="min-w-[15%] "></div>
      <div class="min-w-[10%]  font-bold text-red-400" style="font-size: large;">{{ 总成本合计 }}</div>
      <div class="min-w-[25%] "></div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%]  font-bold">毛利率(按出厂成本)</div>
      <div class="min-w-[10%] ">
        
      </div>
      <div class="min-w-[15%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.毛利率" size1="small" class="!w-[50%]" :disabled="mode != '编辑'" type="number"/>%
      </div>
      <div class="min-w-[10%]  font-bold">{{ 毛利率小计 }}</div>
      <div class="min-w-[25%] ">低于20％需要提交审核</div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%] font-bold  text-black !pl-[20px] " style="justify-content: left; font-size: 16px;">销售单价(不含税)</div>
      <div class="min-w-[10%] "></div>
      <div class="min-w-[15%] "></div>
      <div class="min-w-[10%]  font-bold">{{ 销售单价不含税 }}</div>
      <div class="min-w-[25%] "></div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%]  font-bold">增值税率</div>
      <div class="min-w-[10%] "></div>
      <div class="min-w-[15%] ">
        <ElInputNumber :min="0" :controls="false"  v-model="pdtData.other_data.增值税率" size1="small" class="!w-[50%]" :disabled="mode != '编辑'" type="number"/>%
      </div>
      <div class="min-w-[10%]  font-bold">{{ 增值税小计 }}</div>
      <div class="min-w-[25%] "></div>
    </div>
    <div class="flex content items-center">              
      <div class="min-w-[40%] font-bold  text-black !pl-[20px] " style="justify-content: left; font-size: 16px;">销售单价(含税)</div>
      <div class="min-w-[10%] "></div>
      <div class="min-w-[15%] "></div>
      <div class="min-w-[10%]  font-bold text-green-600" style="font-size: large;">{{ 销售单价含税 }}</div>
      <div class="min-w-[25%] "></div>
    </div>
    <!-- 阶梯报价 -->
    <div class="flex header headerBk w-[100%] mt-5 justify-center">              
        <div class="font-bold">阶梯价格表</div>
    </div>
    <div class="flex content items-center" v-for="item in tdData" :key="item.name">              
      <div class="min-w-[10%]  font-bold">{{ item.name }}</div>
      <div class="min-w-[13%] " :class="getColClass(0)">{{ item.param[0] }}</div>
      <div class="min-w-[13%] " :class="getColClass(1)">{{ item.param[1] }}</div>
      <div class="min-w-[13%] " :class="getColClass(2)">{{ item.param[2] }}</div>
      <div class="min-w-[13%] " :class="getColClass(3)">{{ item.param[3] }}</div>
      <div class="min-w-[13%] " :class="getColClass(4)">{{ item.param[4] }}</div>
      <div class="min-w-[20%] " >{{ item.param[5] }}</div>
    </div>
    <!-- <div class="flex mt-10 justify-center">
      <ElButton v-if="pdtData.status == t('status.wait_cus_comfirm')" class="mr-10" type="success" style="scale: 1.3;" @click="onSubmitFinalPriceOK">客户确认报价</ElButton>
      <ElButton  type="primary" style="scale: 1.3;" @click="onSubmitFinalPrice">提交销售价格</ElButton>      
    </div> -->
  </div>
</template>

<style lang="less" scoped>
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #f4f4f5 !important;
  color: #333;
  font-weight: bold;
}
.headerBK_specs{
  background-color: rgba(222, 226, 230, var(--tw-bg-opacity));
}
.headerBK_specs div{
  color: black;
}
.content{
  &:extend(.header);
  font-size: 14px;
  height:40px
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
 // white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  max-height: 90px;
  font-size: 13px;
  height:40px;
  align-items:center;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

:deep(.el-input__inner){
  text-align: center;
}

.hightTxt{
  color: green; 
  font-weight: 900;
}
.normalTxt{
  color: var(--el-text-color-regular);
}

.el-input.is-disabled /deep/ .el-input__inner {
  color: #000000;
  background-color: white;
}
:deep(.el-input__wrapper){
  padding-left: 1px;
  padding-right: 1px;
}
</style>