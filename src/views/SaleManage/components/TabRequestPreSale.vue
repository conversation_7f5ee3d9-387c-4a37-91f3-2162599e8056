<script setup lang="ts">
import { reactive, ref } from 'vue';
import { ElDescriptions, ElDescriptionsItem, ElUpload, ElDialog,ElForm,ElFormItem,ElInput } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { onMounted,watch } from 'vue';
import { addFollowInfoApi, getFollowInfoListApi } from '@/api/customer'
import type { UploadProps } from 'element-plus'
import { getOssSignApi, ossUpload } from '@/api/oss';
import { useRouter } from 'vue-router'
const { currentRoute } = useRouter()

const { t } = useI18n()
const props = defineProps<{
    capmode:boolean, //快照模式
    pdt: any,
    fsm_cur_state:string, //当前状态,
    presaleData:any
}>()

const emit = defineEmits(['onUpdate'])
const pdtData = reactive(props.pdt)


watch(pdtData, () => {
  emit('onUpdate', pdtData);
})

const mode = ref('编辑')
watch(() => props.fsm_cur_state, () => {
    console.log('---------------------')
  if (['等待确认', '已确认', '等待审核'].includes(props.fsm_cur_state) || props.capmode) {
    mode.value = '查看'
  }
  else {
    mode.value = '编辑'
  }
})

onMounted(()=>{
    if(currentRoute.value.query.mode == '报价审核' || currentRoute.value.query.type == 'info')
    {
        mode.value = '查看'
    }
    
    console.log('==========',props.fsm_cur_state)
    if(['等待确认','已确认','等待审核'].includes(props.fsm_cur_state))
    {
        mode.value = '查看'
    }

    if(props.capmode)
    {
        mode.value = '查看'
    }
})


const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
    for (let one of pdtData.pics) {
        if (one.url == uploadFile.raw.weburl) {
            pdtData.pics.splice(pdtData.pics.indexOf(one), 1)
            break
        }
    }
    console.log('删除图片', pdtData.pics)
}

const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePreviewPic: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
    console.log('预览图片')
}

const uploadImg = async (file) => {
    let path = 'presale/' + props.presaleData.sell_offer_num + '/'
    console.log('====', file)

    const ret = await getOssSignApi({ upload_dir: path })
    if (ret) {
        const end = await ossUpload(ret.data.token, file.file, path, (pro) => {
            console.log('pppp', pro)
        })

        pdtData.pics.push({
            name: file.file.name,
            url: end.url
        })
        file.file.weburl = end.url
        console.log('完成', pdtData.pics, file)
    }
}
//修改数量需要根据阈值调整默认毛利率
const onModifyAmount =()=>{
    if(pdtData.pdt_amount <= 3000)
    {
        pdtData.other_data.毛利率 = 60
    }
    else{
        pdtData.other_data.毛利率 = 23
    }
    console.log('修改数量')
}
</script>

<template>
    <div class="w-[100%] flex justify-center">
        <div class="w-[70%]">
            <el-descriptions class="flex-1 mt-2" :column="2" border direction="vertical">
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('product_manage.name')+':'" class="flex" :span="2">
                    <el-form-item prop="pdt_nick">
                        <el-input v-model="pdtData.pdt_nick" class="!w-[100%]" placeholder="客户产品简称+材质+品类 如：李白滴胶钥匙扣" :disabled="mode != '编辑'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('presale.count')+':'" class="flex" :span="2">
                    <el-form-item prop="pdt_amount">
                        <el-input v-model="pdtData.pdt_amount" type="number" placeholder="请输入数量" :disabled="mode != '编辑'" @blur="onModifyAmount"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('sale.material')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_stuff" placeholder="请输入材质" :disabled="mode != '编辑'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('presale.specs_weight')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_specs" clearable :autosize="{ minRows: 3, maxRows: 3 }" :disabled="mode != '编辑'"
                            type="textarea" :placeholder="'例如：公仔主体：50*27*55\nMM带子：16*237MM\n小吊牌10.5*32ＭＭ'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle" 
                    :label="t('presale.rule')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_skill" clearable :autosize="{ minRows: 3, maxRows: 3 }" :disabled="mode != '编辑'"
                            type="textarea" :placeholder="'例如：1.手感漆\n2.眼睛亮油\n3.模具抛光'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('presale.print')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_print" clearable :autosize="{ minRows: 3, maxRows: 3 }" :disabled="mode != '编辑'"
                            type="textarea" placeholder="请输入Logo要求/印刷"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('presale.color')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_color" placeholder="请输入颜色" :disabled="mode != '编辑'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('presale.pack_requir')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.pdt_pack" placeholder="例如：彩盒+铝箔袋" :disabled="mode != '编辑'"/>
                    </el-form-item>
                </el-descriptions-item>
                <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                    :label="t('presale.other')+':'" class="flex" :span="2">
                    <el-form-item prop="name">
                        <el-input v-model="pdtData.note" clearable :autosize="{ minRows: 3, maxRows: 3 }" :disabled="mode != '编辑'"
                            type="textarea" placeholder=""/>
                    </el-form-item>
                </el-descriptions-item>
                    <!-- <el-descriptions-item label-class-name='labelStyle ' class-name="conentStyle"
                        :label="t('presale.img')+':'" class="flex" :span="2">
                        <el-upload :disabled="mode == '查看'" :file-list="pdtData.pics"
                            list-type="picture-card" :on-preview="handlePreviewPic" :on-remove="handleRemove"
                            :accept="'.jpg,.png,.bmp'" :http-request="(file) => uploadImg(file)">
                            <Icon icon="fluent:add-24-filled" />
                        </el-upload>
                    </el-descriptions-item> -->
            </el-descriptions>

        </div>
        <div class="w-[30%]">
            <el-upload :disabled="mode == '查看'" :file-list="pdtData.pics"
                list-type="picture-card" :on-preview="handlePreviewPic" :on-remove="handleRemove"
                :accept="'.jpg,.png,.bmp'" :http-request="(file) => uploadImg(file)">
                <Icon icon="fluent:add-24-filled" />
            </el-upload>
        </div>
        <el-dialog v-model="dialogVisible">
            <img w-full :src="dialogImageUrl" alt="预览" />
        </el-dialog>
        
    </div>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 8% !important;
    background-color: #fff !important;
    white-space: nowrap;
}

:deep(.conentStyle) {
    width: 25% ;
}

.el-form-item--default {
    margin-bottom: unset;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

:deep(.tableHeader) {
    // background-color: #6d92b4 !important;
    background-color: RGBA(246, 246, 246, 1) !important;
    color: #333;
    white-space: nowrap;
    text-align: center;
}

//增加表格最后一行与横向滚动条距离
:deep(.el-scrollbar__wrap) {
    margin-bottom: 10px;
}

//设置表单元格属性
:deep(.table_cell .cell) {
    padding-left: 3px;
    padding-right: 3px;
    text-align: center;
}

:deep(.table_cell .el-input__inner) {
    /* 添加你的样式 */
    text-align: center;
}

:deep(.bakinput .el-input__wrapper) {
    background-color: #f4f4f4;
}

:deep(.infomode) {
    border: none;
    /* 设置边框为 none */
    border-radius: 0;
    /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none;
    /* 如果有阴影，也可以设置为 none */
}

:deep(.el-card) {
    background-color: var(--app-content-bg-color);
    border-color: var(--app-content-bg-color);
}

:deep(.el-card__body) {
    // padding: 0 240px var(--el-card-padding);
    display: flex;
    justify-content: center;
}

.paper-div {
    background-color: #fff;
    box-shadow: 0px 6px 14px 4px rgba(208, 208, 208, 0.34);
    border-radius: 4px;
    padding: var(--el-card-padding) var(--el-card-padding) calc(6 * var(--el-card-padding));
    width: 100%;
}

:deep(.el-tabs__item) {
    border-radius: 4px;
}

:deep(.el-descriptions__body) .el-descriptions__table.is-bordered .el-descriptions__cell {
    border: none;
    padding: 8px 11px;
}

:deep(.el-tabs--border-card)>.el-tabs__content {
    padding: 15px 15px 64px ;
}
</style>