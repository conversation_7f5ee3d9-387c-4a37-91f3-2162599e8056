<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElSwitch,ElPopconfirm,ElDialog, ElButton,ElForm,ElFormItem, ElTable, ElTree, ElMessage, ElMessageBox, ElRadio,ElImage, ElRadioGroup, ElTableColumn, ElInput, ElSelect, ElOption, ElDescriptions, ElDescriptionsItem, ElCheckboxGroup, ElDatePicker, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getStoreListApi,getCategListApi, addCategApi, updateCategApi, delCategApi,getInventoryListApi, updateProductApi,delProductApi } from '@/api/product'
import { onBeforeMount } from 'vue'
import { RightMenu } from '@/components/RightMenu'
import { Dialog } from '@/components/Dialog'
import { computed,nextTick } from 'vue';
import { useCache } from '@/hooks/web/useCache'
import { cloneDeep } from 'lodash-es';
import { downloadFile } from '@/api/tool';
import { exportStoneListApi } from '@/api/extra';
import { onBeforeUnmount } from 'vue';

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()

//分类树
const categTree = ref()
//当前选中分类节点
const currentCatg = ref('')
//当前选中分类节点详细信息
const currentCatgData = ref({})
//分类树数据源
const categData = reactive([])
//分类树默认属性
const defaultProps = {
  children: 'sub_categ',
  label: 'name',
}
//分类树默认展开节点
const expandCateg = reactive([])
//分类树右键菜单classname
const categMenuClassName = 'categ_menu'
//分类右键菜单
const menuCateg = reactive([
  {
    icon: "icon-park-outline:add",
    name: 'add_categ',
    title: t('product_manage.add_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'update_categ',
    title: t('product_manage.update_categ')
  },
  {
    icon: "icon-park-outline:add",
    name: 'delete_categ',
    title: t('product_manage.delete_categ')
  },
])

//库存数据
const inventorytData = reactive([])

//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const tableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
  store_id:'',
  categ_id:'',
  仓库:'',
  产品编码:'',
  产品名称:'',
  规格:'',
  产品品牌:'',
  产品简称:'',
  最后变动时间条件:'',
  最后变动时间:'',
  客户:'',
  条形码:'',
  助记码:'',
  客户对应编码:'',
  库存数量:'大于零',
  是否计算货物价值:'是',
  page: 1,
  count: 30
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})
//高级选项
const senior = ref(false)

//查询产品分类树
const getCategList = async () => {
  const ret = await getCategListApi({
    page: 1,
    count: 10000
  })
  if (ret) {
    console.log(ret)
    console.log('当前选中')

    //设置默认选中
    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    console.log(categTree.value)
    console.log(lastSelCateg.id)
    currentCatgData.value = lastSelCateg
    currentCatg.value = lastSelCateg.id
    searchCondition.categ_id = lastSelCateg.id
    if(lastSelCateg.id != undefined)
    {
      nextTick(()=>{
        categTree.value.setCurrentNode(lastSelCateg)
        console.log(categTree.value?.getCurrentNode())
      })
    }


    categData.splice(0, categData.length, ret.data.all_categs)
    //设置默认展开
    expandCateg.splice(0, expandCateg.length, ret.data.all_categs.id)
    if (currentCatg.value) {
      expandCateg.push(currentCatg.value)
    }
  }
}
//分类树点击左键
const leftClick = (data) => {
  currentCatgData.value = data
  searchCondition.categ_id = data.id
  //更新最后一次选中
  wsCache.set('last_sel_categ',data)
  getInventoryList()
}

//分类树点击右键菜单
const rightClick = (event, data) => {
  currentCatg.value = data.id
  currentCatgData.value = data
  //隐藏之前的菜单
  hideRightMenu()

  //如果是未分组人员行不允许弹出菜单
  if (data.name == '未分组人员') {
    event.preventDefault();
    return
  }


  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  if (!menu) return;

  event.preventDefault();
  menu.style.left = `${event.pageX - 200}px`;
  menu.style.top = `${event.pageY - 200}px`;
  menu.style.display = "block";
  menu.style.zIndex = "1000";

  document.addEventListener("click", hideRightMenu);
}
//隐藏右键菜单
const hideRightMenu = () => {
  const menu = document.querySelector("#" + categMenuClassName) as HTMLElement;
  menu!.style.display = "none";
  document.removeEventListener("click", hideRightMenu);
}
//处理分类树菜单消息
const handleMenuEvent = (item) => {
  console.log(item)
  hideRightMenu()
  //添加/修改分类
  if (item === 'add_categ' || item === 'update_categ') {
    ElMessageBox.prompt(t('msg.inputCategName'), t('title.notify'),
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        inputErrorMessage: t('msg.inputCategName'),
        inputValidator: (val) => {
          if (val === null || val.length < 1) {
            return false;
          }
        }
      })
      .then(async ({ value }) => {
        if (item === 'add_categ') {
          const ret = await addCategApi({
            parent_id: currentCatg.value,
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }
        else {
          const ret = await updateCategApi({
            ids: [currentCatg.value],
            name: value,
            note: ''
          })
          if (ret) {
            getCategList()
            ElMessage({
              type: 'success',
              message: t('msg.success'),
            })
          }
        }


      })
  }
  //删除分类
  else if (item === 'delete_categ') {
    ElMessageBox.confirm(
      t('msg.delCateg'),
      'Warning',
      {
        confirmButtonText: t('button.ok'),
        cancelButtonText: t('button.cancel'),
        type: 'warning',
      }
    )
      .then(async () => {
        const ret = await delCategApi({
          ids: [currentCatg.value],
        })
        if (ret) {
          getCategList()
          ElMessage({
            type: 'success',
            message: t('msg.success'),
          })
        }



      })
  }
  //设置分类参数
  else if (item === 'categ_param_set') {
    showEncoderWin.value = true
    categEncoderSet.splice(0, categEncoderSet.length, ...currentCatgData.value.pdt_num_role)
    console.log(categEncoderSet)
  }
  else if(item === 'add_product')
  {
    onAddProduct()
  }
}

const loading = ref(false)
//仓库数据源
const storeData = reactive([])
//获取仓库数据
const getStoreList = async () => {
  const res = await getStoreListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    storeData.splice(0,storeData.length,...[{nick:'全部仓库',id:''},...res.data])
  }
}

//开始查询
const onSearch = () => {
  getInventoryList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (tableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getInventoryList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getInventoryList(val)
}
//处理表格对象操作
const handleOper = (type, item) => {
  if(type === 'del') //启用
  {
    ElMessageBox.confirm(t('msg.confirm_del')+'--> '+item.nick, t('msg.notify'), {
      confirmButtonText: t('msg.ok'),
      cancelButtonText: t('msg.channel'),
      type: 'error',
    }
    ).then(async () => {
      console.log(item)
      const ret = await delProductApi({
        ids: [item.id],
      })
      if (ret) {
        ElMessage({
          type: 'success',
          message: t('msg.success'),
        })
        getCategList()
        getInventoryList()
      }
    }
    ).catch(() => {})
  }
}


//查询库存列表
const getInventoryList = async (page = 1) => {
  const lastSelCateg = wsCache.get('last_sel_categ') || {}
  searchCondition.categ_id = lastSelCateg.id
  searchCondition.page = page
  let tmp = cloneDeep(searchCondition)
  delete tmp.最后变动时间条件
  delete tmp.最后变动时间

  tmp.仓库 = ''
  tmp.store_id = searchCondition.仓库 =='全部仓库'?'':searchCondition.仓库
  
  tmp.最后变动时间早于 = searchCondition.最后变动时间条件=='早于'?searchCondition.最后变动时间:''
  tmp.最后变动时间晚于 = searchCondition.最后变动时间条件 == '晚于' ? searchCondition.最后变动时间 : ''

  loading.value = true
  const ret = await getInventoryListApi(tmp)
  if(ret)
  {
    inventorytData.splice(0,inventorytData.length, ...ret.data)
    console.log(inventorytData)
    totleCount.value = parseInt(ret.count)

    for(let one of inventorytData)
    {
      one.库存数量 = parseFloat(one.库存数量)
      one.良品数量 = parseFloat(one.良品数量)
      one.不良品数量 = parseFloat(one.不良品数量)
    }
    总库存数量.value = 0
    inventorytData.forEach(item=>{
      总库存数量.value += parseInt(item.库存数量)
    })
  }
  loading.value = false
}

//查看库存明细
const onShowDetail = (item)=>{
  push({
      path: '/inventorymanage/inventorydetail',
      query:{
        pdt_id:item.pdt_id,
        pdt_name:item.pdt_name,
        pdt_nick:item.pdt_nick
      }
    })
}

onMounted(() => {
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //更新分类树
  getCategList()

  //更新库存列表
  getInventoryList()

  //更新仓库数据
  getStoreList()
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
});


//--------------中间拖拽调整大小-------------
const isResizing = ref(false);
const startX = ref(0);
const leftWidth = ref(15);
const rightWidth = ref(85);

const startResize = (event: MouseEvent) => {
  event.preventDefault(); // 阻止默认行为
  isResizing.value = true;
  startX.value = event.clientX;

  document.addEventListener('mousemove', resize);
  document.addEventListener('mouseup', stopResize);
};

const resize = (event: MouseEvent) => {
  if (isResizing.value) {
    event.preventDefault(); // 阻止默认行为
    const deltaX = event.clientX - startX.value;
    leftWidth.value += (deltaX / window.innerWidth) * 100;
    rightWidth.value -= (deltaX / window.innerWidth) * 100;
    startX.value = event.clientX;
  }
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', resize);
  document.removeEventListener('mouseup', stopResize);
};



const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    // const tmp = cloneDeep(searchCondition)

    const lastSelCateg = wsCache.get('last_sel_categ') || {}
    searchCondition.categ_id = lastSelCateg.id
    let tmp = cloneDeep(searchCondition)
    delete tmp.最后变动时间条件
    delete tmp.最后变动时间

    tmp.仓库 = ''
    tmp.store_id = searchCondition.仓库 =='全部仓库'?'':searchCondition.仓库
    
    tmp.最后变动时间早于 = searchCondition.最后变动时间条件=='早于'?searchCondition.最后变动时间:''
    tmp.最后变动时间晚于 = searchCondition.最后变动时间条件=='晚于'?searchCondition.最后变动时间:''

    const ret = await exportStoneListApi(tmp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
          downloadFile(ret.data.download,ret.data.filename)
          loadingExport.value = false
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}

const showModifyPrice = ref(false)
const selItem = reactive({})
const onModifyPrice = async(item) => {
  // let tmp = cloneDeep(item)

  // tmp.ids = [tmp.id]
  // tmp.buy_price_aft_tax
  //   const res = await updateProductApi(tmp)
  //   if(res)
  //   {
  //     console.log(res)
  //     getInventoryList()
  //   }
  showModifyPrice.value = true
  Object.assign(selItem,item)
}
const onModifyPriceOK = async () => {
  showModifyPrice.value = false
  console.log(selItem) 
  let tmp = {  }
  tmp.id = selItem.pdt_id
  tmp.avg_price_bef_tax = selItem.avg_price_bef_tax
  tmp.avg_price_aft_tax = selItem.avg_price_aft_tax
  console.log(tmp)
    const res = await updateProductApi(tmp)
    if(res)
    {
      console.log(res)
      getInventoryList()
    }
}

const 总库存数量 = ref(0)


</script>

<template>
  <div class="flex absolute top-5 right-5 left-5 bottom-5">
    <!-- 左侧分类栏 -->
    <div class=" bg-white p-2 overflow-y-auto" :style="{ width: leftWidth + '%'}">
      <el-tree ref="categTree" :data="categData" :props="defaultProps" :default-expanded-keys="expandCateg" node-key="id"
        @node-contextmenu="rightClick" @node-click="leftClick" highlight-current :current-node-key="currentCatg"
        :expand-on-click-node="false" :render-after-expand="true">
        <template #default="{ node }">
          <Icon icon="bx:category" />
          <div class="pl-2">{{ node.data.name  }}
          </div>
        </template>
      </el-tree>
      <RightMenu :id="categMenuClassName" :menuDate="menuCateg" @on-menu-event="handleMenuEvent" />
    </div>
    <div class="drag w-2  bg-gray-100 " style="cursor: col-resize;" @mousedown="startResize"></div> 
    <!-- 右侧产品列表 -->
    <div ref="rootRef" class="relative !bg-white flex-grow overflow-y-auto" :style="{ width: rightWidth + '%'}">
      <div class="absolute top-3 left-10">
                <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                    <Icon icon="carbon:export" />
                    <div class="pl-2">{{ t('button.export') }}</div>
                </ElButton>
            </div>
      <div class="h-[100%] bg-white p-3">
        <div class="text-center mb-1 font-bold">{{ t('inventory.list') }}</div>
        <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 bg-light-200">
          <!-- 检索条件 -->
          <div class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('store.nick') }}</div>
            <el-select  size="small" class="searchItem" v-model="searchCondition.仓库" placeholder="选择仓库" >
              <el-option v-for="item in storeData" :key="item.nick" :label="item.nick" :value="item.id" />
            </el-select>
          </div>
          <div class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">产品编码</div>
            <el-input  size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
          </div>
          <div class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.name') }}</div>
            <el-input  size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.specify_info') }}</div>
            <el-input  size="small" v-model="searchCondition.规格" placeholder="" class="searchItem" />
          </div>
          <div  class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle !w-[120px]">计算货物价值</div>
            <el-select  size="small" class="searchItem" v-model="searchCondition.是否计算货物价值" placeholder="" >
              <el-option v-for="item in [{name:'计算',value:'是'},{name:'不计算',value:'否'}]" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.brand') }}</div>
            <el-input  size="small" v-model="searchCondition.产品品牌" placeholder="" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.short_name') }}</div>
            <el-input  size="small" v-model="searchCondition.产品简称" placeholder="" class="searchItem" />
          </div>
          
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">客户</div>
            <el-input  size="small" v-model="searchCondition.客户" placeholder="" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">条形码</div>
            <el-input  size="small" v-model="searchCondition.条形码" placeholder="" class="searchItem" />
          </div>

          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">{{ t('product_manage.help_name') }}</div>
            <el-input  size="small" v-model="searchCondition.助记码" placeholder="" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">客户对应编码</div>
            <el-input  size="small" v-model="searchCondition.客户对应编码" placeholder="" class="searchItem" />
          </div>
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">库存数量</div>
            <el-select  size="small" class="searchItem" v-model="searchCondition.库存数量" placeholder="选择仓库" >
              <el-option v-for="item in ['全部','大于零','大于等于零','等于零']" :key="item" :label="item" :value="item" />
            </el-select>
          </div>
          <div v-if="senior" class="inline-flex items-center mr-1 mb-1">
            <div class="searchTitle">变动时间</div>
            <el-select  size="small" class="searchItem !w-[60px]" v-model="searchCondition.最后变动时间条件" placeholder="条件" >
              <el-option v-for="item in ['早于','晚于']" :key="item" :label="item" :value="item" />
            </el-select>
            <el-date-picker
              class='!w-[120px]'
              size="small"
              v-model="searchCondition.最后变动时间"
              type="date"
              placeholder="最后变动时间"
            />
          </div>

          <div  class=" flex items-center justify-end">
            <el-checkbox :label="t('customer.senior')" v-model="senior" size="small"/>
            <ElButton class="ml-5" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
        </div>
        <!-- 产品列表 -->
        <el-table :height11="tableHeight" v-loading.lock="loading" scrollbar-always-on ref="tableRef" :data="inventorytData" style="width: 100%; margin-bottom: 20px" row-key="id" border stripe
          header-cell-class-name="tableHeader">
          <el-table-column show-overflow-tooltip align="center" min-width="150"  prop="pdt_name" :label="t('product_manage.id')" />
          <el-table-column show-overflow-tooltip min-width="200" align="center"  prop="pdt_nick" :label="t('product_manage.name')" >
            <template #default="scope">
              <span class="whitespace-normal">{{ scope.row.pdt_nick }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"   prop="产品规格" label="规格" />
          <el-table-column show-overflow-tooltip align="center"  :label="t('inventory.count')" >
            <template #default="scope">
              {{ scope.row.良品数量+scope.row.不良品数量 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="pdt_base_unit" :label="t('product_manage.b_unit')" />
          <el-table-column show-overflow-tooltip align="center"  prop="锁定数量" :label="t('inventory.lock_count')" />
          <el-table-column show-overflow-tooltip align="center"  prop="良品数量" :label="t('inventory.avalid_count')" >
            <template #default="scope">
              {{ scope.row.良品数量-scope.row.锁定数量 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip align="center"  prop="不良品数量" :label="t('inventory.bad_count')" />
          <el-table-column show-overflow-tooltip align="center" prop='货物价值_未税'  :label="t('inventory.price_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center" prop='货物价值_含税'  :label="t('inventory.price_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_bef_tax" :label="t('inventory.price_avr_bef_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="avg_price_aft_tax" :label="t('inventory.price_avr_aft_tax')" />
          <el-table-column show-overflow-tooltip align="center"  prop="modify_date" :label="t('inventory.last_date')" min-width="200"/>
    
          <el-table-column show-overflow-tooltip fixed="right" align="center" width="80px" prop="name" :label="t('roleTable.opt')" >
            <template #default="scope">
              <!-- <ElButton type="primary" size="small" @click="onShowDetail(scope.row)">{{ t('userOpt.info') }}</ElButton> -->
              <el-dropdown  trigger="click" placement="bottom">
                <span class="el-dropdown-link">
                  <ElButton type="primary" size="small">{{ t('userTable.operate') }}</ElButton>
                </span>
                <template #dropdown>
                  <ElDropdownMenu>
                    <el-dropdown-item @click="onShowDetail(scope.row)">{{ t('userOpt.info') }}</el-dropdown-item>
                    <el-dropdown-item  @click="onModifyPrice(scope.row)">修改单价</el-dropdown-item>
                  </ElDropdownMenu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex mt-5 items-center">
          <div class="ml-auto mr-10">
            总库存数量:{{ 总库存数量 }}
          </div>
          <el-pagination 
            v-model:current-page="searchCondition.page"
            v-model:page-size="searchCondition.count"
            :page-sizes="[10, 50, 100, 300]"
            :background="true"
            layout="sizes, prev, pager, next"
            :total="totleCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      <!-- <div class="mb-[300px]"></div> -->
      </div>

      <el-dialog
        title="请输入要修改的价格"
        v-model="showModifyPrice"
        width="500"
        align-center
        destroy-on-close
      >
      <el-form>
        <el-form-item label="产品编号:">
          {{selItem.pdt_name}}
        </el-form-item>
        <el-form-item label="产品名称:">
          {{selItem.pdt_nick}}                      
        </el-form-item>
        <el-form-item label="税前单价:">
          <el-input type="number" v-model="selItem.avg_price_bef_tax" placeholder="请输入税前单价"></el-input>
        </el-form-item>
        <el-form-item label="税后单价:">
          <el-input type="number" v-model="selItem.avg_price_aft_tax" placeholder="请输入税后单价"></el-input>
        </el-form-item>
      </el-form> 
      <template #footer>
          <div class="dialog-footer">
            <el-button @click="showModifyPrice = false">取消</el-button>
            <!-- <el-button type="danger" @click="onModifyPriceOK">修改</el-button> -->
            <el-popconfirm  title="确定是否修改价格?" @confirm="onModifyPriceOK">
                <template #reference>
                  <el-button type="danger">修改</el-button> 
                </template>
            </el-popconfirm>  
          </div>
        </template>

      </el-dialog>


    </div>
  </div>
</template>

<style lang="less" scoped>
//修改选中时的颜色
// :deep(.current-row) {
//   background-color: #ffe48d !important;
// }

// :deep(.tableHeader) {
//   background-color: #6d92b4 !important;
//   color: #fff;
//   font-weight: 400;
//   font-size: 11px;
//   font-weight: 500;
// }

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e1f3d8 !important;
}

.searchItem{
  width: 130px;
}

//自定义表格
.header {
  display: flex;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;
  width: 100%;
  color: var(--el-text-color-regular);
  font-size: 15px;
}
.headerBk{
  background-color: #6d92b4 !important;
}
.content{
  &:extend(.header);
  font-size: 14px;
}

.header > div ,
.content > div {
  display: flex;
  border-right: 1px solid #e2e2e2;
  padding: 10px; 
  // max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  padding: 5px;
  justify-content: center;
  min-width: 50px;
  max-height: 90px;
}
.header > div:last-child,
.content > div:last-child {
  border-right: none;
}

//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 90px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}

//解决tree没有横向滚动条问题
:deep(.el-tree>.el-tree-node) {
    display: inline-block;
    min-width: 100%;
  }

</style>
