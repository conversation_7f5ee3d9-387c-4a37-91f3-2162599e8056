<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElInput, ElButton, ElTable, ElTableColumn, ElDatePicker, ElSelect, ElOption, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { reactive, ref, onMounted, onActivated } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { checkPermissionApi, closeOneTagByName, closeOneTagByPath, downloadFile, getDateArea } from '@/api/tool';
import { getBuyReportAllApi, getSellBayBillTJApi, getSellReportAllApi, getStoneReportMonthApi } from '@/api/tj';
import { getStoreListApi } from '@/api/product';
import { cloneDeep } from 'lodash-es';
import { exportStoneReportMonthListApi } from '@/api/extra';


const { currentRoute, back, push } = useRouter()
const { t } = useI18n()


//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    库存变动日期:['',''],
    月份: '',
    仓库: '',
    产品编码: '',
    产品名称: '',
    page: 1,
    count: 30
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})
//高级选项
const senior = ref(false)

const reportData = reactive([])
//当前选中行
const currentRow = ref(null)


const getStoneReportMonth = async () => {
    loading.value = true
    let temp = cloneDeep(searchCondition)
    temp.库存变动日期 = searchCondition.库存变动日期[0] + ',' + searchCondition.库存变动日期[1]
    const ret = await getStoneReportMonthApi(temp)
    if (ret) {
        console.log(ret)
        reportData.splice(0, reportData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
    loading.value = false
}

//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getStoneReportMonth()
}
//清除条件
const onClear = () => {
    searchCondition.reset()
    //     //设置默认年份
    //     const currentDate = new Date();
    // const currentYear = currentDate.getFullYear();
    // searchCondition.月份 = currentYear.toString() + '-' + (currentDate.getMonth()).toString().padStart(2, '0')
    if (currentRoute.value.name == 'StoneReportMonth') {
            //设置默认年份
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        searchCondition.月份 = currentYear.toString() + '-' + (currentDate.getMonth()).toString().padStart(2, '0')
    }
    else {
        let days = getDateArea(7)
        defaultCondition.库存变动日期 = [days[0], days[1]]
        searchCondition.reset()
        console.log('====',defaultCondition)
    }

}

//page控件发生切换
const handleSizeChange = (val: number) => {
    getStoneReportMonth()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getStoneReportMonth()
}

//处理表格对象操作
const handleOper = (type, row) => {
    if (type == 'info') {

    }

}
//设置当前选中行
const setCurrentRow = (value) => {
    currentRow.value = value
}

//仓库
const storeData = reactive<any[]>([])
//获取仓库数据
const getStoreList = async () => {
    const res = await getStoreListApi({
        page: 1,
        count: 1000
    })
    if (res) {
        console.log(res.data)
        storeData.splice(0, storeData.length, ...res.data)
        searchCondition.仓库 = storeData[0].nick
    }

}

onMounted(async () => {
    console.log(currentRoute.value.name)

    if (currentRoute.value.name == 'StoneReportMonth') {
            //设置默认年份
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        searchCondition.月份 = currentYear.toString() + '-' + (currentDate.getMonth()).toString().padStart(2, '0')
    }
    else {
        let days = getDateArea(7)
        defaultCondition.库存变动日期 = [days[0], days[1]]
        searchCondition.reset()
        console.log('====',defaultCondition)
    }


    await getStoreList()
    //刷新表格
    await getStoneReportMonth()

    tableHeight.value = document.getElementById('mainscroll')?.clientHeight - 20
    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
        if (event.key === 'Enter') {
            onSearch();
        }
        });
    });
})

//显示合计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            return;
        }
        if (['期初未税金额', '期初含税金额', '入库未税金额', '入库含税金额', '出库未税金额', '出库含税金额', '期末未税金额', '期末含税金额'].includes(column.property)) {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
                const sum = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return parseFloat((prev + curr).toFixed(2));
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] = sum
            } else {
                sums[index] = '/';
            }
        }
    })
    return sums;
}

const tableHeight = ref(60)
const loading = ref(false)

const onShowDetail = (row) => {
    push({
        path: '/stonereport/stonereportdetail',
        query: {
            stone: searchCondition.仓库,
            month: searchCondition.月份,
            pdt_name: row.产品编码,
            pdt_nick: row.产品名称,
            any:searchCondition.库存变动日期[0] + '_' + searchCondition.库存变动日期[1]
        }
    })
}


//页面切换后恢复滚动位置
let scrollY = ref(0);
onBeforeRouteLeave((to, from, next) => {
    scrollY.value = document.getElementById('mainscroll')?.scrollTop
    console.log('离开了', scrollY)
    next()
})
onActivated(() => {
    document.getElementById('mainscroll')?.scrollTo(0, scrollY.value)
})

const loadingExport = ref(false)
//导出列表
const onExport = async () => {
  //确认是否导出当前质检单列表
  ElMessageBox.confirm('确认是否要导出当前查询条件的所有结果？', t('msg.notify'), {
    confirmButtonText: t('msg.ok'),
    cancelButtonText: t('msg.channel'),
    type: 'warning',
  }
  ).then(async () => {
    loadingExport.value = true
    let temp = cloneDeep(searchCondition)
    temp.库存变动日期 = searchCondition.库存变动日期[0] + ',' + searchCondition.库存变动日期[1]

    const ret = await exportStoneReportMonthListApi(temp)
    if (ret) {
        
        ElMessage.success('导出成功，等待下载！')
        setTimeout(() => {
            loadingExport.value = false
            downloadFile(ret.data.download,ret.data.filename)
        }, 4000)
    }


  })

  setTimeout(() => {
    loadingExport.value = false
    }, 10000)

}
</script>

<template>
    <div ref="rootRef">
        <div class="pb-[100px] relative w-[100%] !bg-white flex-grow " style="color:#666666">
            <div class="absolute top-3 left-10">
                <ElButton type="primary" @click="onExport" plain v-loading.fullscreen.lock="loadingExport">
                    <Icon icon="carbon:export" />
                    <div class="pl-2">{{ t('button.export') }}</div>
                </ElButton>
            </div>
            <div class="text-center pt-3 mb-5 font-bold" style="color:#333">{{ searchCondition.月份 + ' ' +
                searchCondition.仓库 + (currentRoute.name=='StoneReportMonth'?'月度报表':'进出存报表') }}</div>
            <div class="inline-flex items-center ml-1 mb-1" v-if="currentRoute.name == 'StoneReportMonth'">
                <div class="searchTitle">月份</div>
                <el-date-picker size="small" @change="onSearch" @calendar-change="onSearch" :clearable="false"
                    v-model="searchCondition.月份" type="month" placeholder="-" format="YYYY-MM" value-format="YYYY-MM" />
            </div>
            <div class="inline-flex items-center ml-1 mb-1" v-if="currentRoute.name == 'StoneReportAny'">
                <div class="searchTitle">时间范围</div>
                <el-date-picker size="small"  class="searchItem" v-model="searchCondition.库存变动日期" type="daterange" range-separator="To"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">仓库</div>
                <el-select size="small" v-model="searchCondition.仓库" placeholder="请选择" @change="getStoneReportMonth">
                    <el-option v-for="item in storeData" :key="item.id" :label="item.nick" :value="item.nick" />
                </el-select>
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">{{ t('product_manage.id') }}</div>
                <el-input size="small" v-model="searchCondition.产品编码" placeholder="" class="searchItem" />
            </div>
            <div class="inline-flex items-center ml-1 mb-1">
                <div class="searchTitle">{{ t('product_manage.name') }}</div>
                <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
            </div>
            <div  class=" flex items-center justify-end">
            <ElButton class="ml-5 mb-2" type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton  type="warning" @click="onClear" class="mr-2 mb-2">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>
          </div>
            <el-table v-loading="loading" scrollbar-always-on :height="tableHeight" header-cell-class-name="tableHeader"
                :data="reportData" style="width: 100%;color: #666666;" show-summary :summary-method="getSummaries"
                border stripe>

                <el-table-column align="center" fixed show-overflow-tooltip prop="序号" :label="'序号'" width="50">
                    <template #default="scope">
                        <div>{{ scope.$index + 1 }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed show-overflow-tooltip prop="产品编码" :label="'产品编码'" width="100" />
                <el-table-column align="center" fixed show-overflow-tooltip prop="产品名称" :label="'产品名称'" width="150">
                    <template #default="scope">
                        <div class="whitespace-normal">{{ scope.row.产品名称 }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="单位" :label="'单位'" width="60" />
                <el-table-column align="center" show-overflow-tooltip prop="期初" :label="'期初'">
                    <el-table-column align="center" show-overflow-tooltip prop="期初未税单价" :label="'未税单价'">
                        <template #default="scope">
                            {{ scope.row.期初未税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期初含税单价" :label="'含税单价'">
                        <template #default="scope">
                            {{ scope.row.期初含税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期初数量" :label="'数量'">
                        <template #default="scope">
                            {{ scope.row.期初数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期初未税金额" :label="'未税金额'">
                        <template #default="scope">
                            {{ scope.row.期初未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期初含税金额" :label="'含税金额'">
                        <template #default="scope">
                            {{ scope.row.期初含税金额 }}
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="入库" :label="'入库'">
                    <el-table-column align="center" show-overflow-tooltip prop="采购入库数量" :label="'采购入库数量'">
                        <template #default="scope">
                            {{ scope.row.采购入库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="采购入库未税金额" :label="'采购入库未税金额'">
                        <template #default="scope">
                            {{ scope.row.采购入库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="采购入库含税金额" :label="'采购入库含税金额'">
                        <template #default="scope">
                            {{ scope.row.采购入库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="调拨入库数量" :label="'调拨入库数量'">
                        <template #default="scope">
                            {{ scope.row.调拨入库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="调拨入库未税金额" :label="'调拨入库未税金额'">
                        <template #default="scope">
                            {{ scope.row.调拨入库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="调拨入库含税金额" :label="'调拨入库含税金额'">
                        <template #default="scope">
                            {{ scope.row.调拨入库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="手动入库数量" :label="'手动入库数量'">
                        <template #default="scope">
                            {{ scope.row.手动入库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="手动入库未税金额" :label="'手动入库未税金额'">
                        <template #default="scope">
                            {{ scope.row.手动入库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="手动入库含税金额" :label="'手动入库含税金额'">
                        <template #default="scope">
                            {{ scope.row.手动入库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="其它入库数量" :label="'其它入库数量'">
                        <template #default="scope">
                            {{ scope.row.其它入库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="其它入库未税金额" :label="'其它入库未税金额'">
                        <template #default="scope">
                            {{ scope.row.其它入库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="其它入库含税金额" :label="'其它入库含税金额'">
                        <template #default="scope">
                            {{ scope.row.其它入库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="销售退货数量" :label="'销售退货数量'">
                        <template #default="scope">
                            {{ scope.row.销售退货数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="销售退货未税金额" :label="'销售退货未税金额'">
                        <template #default="scope">
                            {{ scope.row.销售退货未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="销售退货含税金额" :label="'销售退货含税金额'">
                        <template #default="scope">
                            {{ scope.row.销售退货含税金额 }}
                        </template>
                    </el-table-column>


                    <el-table-column align="center" show-overflow-tooltip prop="入库未税单价" :label="'未税单价'">
                        <template #default="scope">
                            {{ scope.row.入库未税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="入库含税单价" :label="'含税单价'">
                        <template #default="scope">
                            {{ scope.row.入库含税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="入库数量" :label="'数量'">
                        <template #default="scope">
                            {{ scope.row.入库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="入库未税金额" :label="'未税金额'">
                        <template #default="scope">
                            {{ scope.row.入库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="入库含税金额" :label="'含税金额'">
                        <template #default="scope">
                            {{ scope.row.入库含税金额 }}
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="出库" :label="'出库'">
                    <el-table-column align="center" show-overflow-tooltip prop="采购退货数量" :label="'采购退货数量'">
                        <template #default="scope">
                            {{ scope.row.采购退货数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="采购退货未税金额" :label="'采购退货未税金额'">
                        <template #default="scope">
                            {{ scope.row.采购退货未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="采购退货含税金额" :label="'采购退货含税金额'">
                        <template #default="scope">
                            {{ scope.row.采购退货含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="调拨出库数量" :label="'调拨出库数量'">
                        <template #default="scope">
                            {{ scope.row.调拨出库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="调拨出库未税金额" :label="'调拨出库未税金额'">
                        <template #default="scope">
                            {{ scope.row.调拨出库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="调拨出库含税金额" :label="'调拨出库含税金额'">
                        <template #default="scope">
                            {{ scope.row.调拨出库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="手动出库数量" :label="'手动出库数量'">
                        <template #default="scope">
                            {{ scope.row.手动出库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="手动出库未税金额" :label="'手动出库未税金额'">
                        <template #default="scope">
                            {{ scope.row.手动出库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="手动出库含税金额" :label="'手动出库含税金额'">
                        <template #default="scope">
                            {{ scope.row.手动出库含税金额 }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" show-overflow-tooltip prop="其它出库数量" :label="'其它出库数量'">
                        <template #default="scope">
                            {{ scope.row.其它出库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="其它出库未税金额" :label="'其它出库未税金额'">
                        <template #default="scope">
                            {{ scope.row.其它出库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="其它出库含税金额" :label="'其它出库含税金额'">
                        <template #default="scope">
                            {{ scope.row.其它出库含税金额 }}
                        </template>
                    </el-table-column>


                    <el-table-column align="center" show-overflow-tooltip prop="销售发货数量" :label="'销售发货数量'">
                        <template #default="scope">
                            {{ scope.row.销售发货数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="销售发货未税金额" :label="'销售发货未税金额'">
                        <template #default="scope">
                            {{ scope.row.销售发货未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="销售发货含税金额" :label="'销售发货含税金额'">
                        <template #default="scope">
                            {{ scope.row.销售发货含税金额 }}
                        </template>
                    </el-table-column>





                    <el-table-column align="center" show-overflow-tooltip prop="出库未税单价" :label="'未税单价'">
                        <template #default="scope">
                            {{ scope.row.出库未税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="出库含税单价" :label="'含税单价'">
                        <template #default="scope">
                            {{ scope.row.出库含税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="出库数量" :label="'数量'">
                        <template #default="scope">
                            {{ scope.row.出库数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="出库未税金额" :label="'未税金额'">
                        <template #default="scope">
                            {{ scope.row.出库未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="出库含税金额" :label="'含税金额'">
                        <template #default="scope">
                            {{ scope.row.出库含税金额 }}
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="期末" :label="'期末'">
                    <el-table-column align="center" show-overflow-tooltip prop="期末未税单价" :label="'未税金额'">
                        <template #default="scope">
                            {{ scope.row.期末未税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期末含税单价" :label="'含税单价'">
                        <template #default="scope">
                            {{ scope.row.期末含税单价 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期末数量" :label="'数量'">
                        <template #default="scope">
                            {{ scope.row.期末数量 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期末未税金额" :label="'未税金额'">
                        <template #default="scope">
                            {{ scope.row.期末未税金额 }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" show-overflow-tooltip prop="期末含税金额" :label="'含税金额'">
                        <template #default="scope">
                            {{ scope.row.期末含税金额 }}
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column fixed="right" align="center" show-overflow-tooltip :label="'明细'">
                    <template #default="scope">
                        <el-button type="primary" size="small" @click="onShowDetail(scope.row)" plain>明细</el-button>
                    </template>
                </el-table-column>
                <!-- <el-table-column align="center" show-overflow-tooltip prop="签约总金额_元" :label="'签约总金额(元)'">
                    <template #default="scope">
                        <div>{{ '￥' + scope.row.签约总金额_元.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" show-overflow-tooltip prop="付款总金额_元" :label="'付款总金额(元)'">
                    <template #default="scope">
                        <div>{{ '￥' + scope.row.付款总金额_元.toLocaleString('en-US') }}</div>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination class="justify-end mt-8" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[30,50, 100, 300, 500]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<style lang="less" scoped>
:deep(.tableHeader) {
    background-color: #f6f6f6 !important;
    color: #333;
    font-weight: 600;
}

.nameStyle {
    color: rgb(60, 60, 255);
    color: #00BA80;
    cursor: pointer;
}

//修改选中时的颜色
:deep(.current-row) {
    background-color: #EAF8F2 !important;
    --el-table-current-row-bg-color: #EAF8F2 !important;
    --el-table-row-hover-bg-color: #EAF8F2;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 90px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}

.searchItem {
    width: 150px !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
    width: 150px !important;
}

:deep(.el-table .cell.el-tooltip),
:deep(.el-table--default .cell) {
    font-size: 12px;
    white-space: normal;
    color: black;
    padding-left: 1px;
    padding-right: 1px;
}
</style>
