<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElSelect,ElOption,ElButton, ElTable, ElTableColumn,ElMessageBox, ElInput, ElForm,ElFormItem,ElMessage,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSpecListApi,addSpecApi,updateSpecApi,delSpesApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { Dialog } from '@/components/Dialog'
import { getMessageListApi, updateMessageApi } from '@/api/tj';
import { useCache } from '@/hooks/web/useCache';
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es';
import { globalState } from '@/api/tool/globalState';

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    状态: '全部',
    用户编号: '',
    page: 1,
    count:30
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

const messageData = reactive([])
const getMessageList = async () => {
  const info = wsCache.get(appStore.getUserInfo)
  let tmp = cloneDeep(searchCondition)
  tmp.用户编号 = info.id
  tmp.状态 = (tmp.状态 == '全部' ? '' : tmp.状态)
  let ret = await getMessageListApi(tmp)
  if(ret) {
    console.log(ret) 
    messageData.splice(0,messageData.length, ...ret.data)
    totleCount.value = parseInt(ret.count)
  }
  getUnReadCount()
}

const unreadCount = ref(0)
const getUnReadCount = async () => {
  const info = wsCache.get(appStore.getUserInfo)
  let ret = await getMessageListApi({
    状态: '未读',
    用户编号: info.id,
    page: 1,
    count:1
  }) 
  if (ret) {
    unreadCount.value = parseInt(ret.count) 
  }
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getMessageList()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
  getMessageList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
  getMessageList()
}


//处理表格对象操作
const handleOper = async(type,row) => {
  console.log(row)
  if (type === 'info') {
    loading.value = true
    globalState.notification = row;
    //修改已读
    let tmp = cloneDeep(row)
    tmp.ids = [row.id]
    tmp.msg_status = '已读'
    let ret = await updateMessageApi(tmp)
    if(ret) {
      await getMessageList()
      globalState.updateMessage = tmp;
    }

  }
  else if(type==='read'){
    //修改已读
    let tmp = cloneDeep(row)
    tmp.ids = [row.id]
    tmp.msg_status = '已读'
    let ret = await updateMessageApi(tmp)
    if(ret) {
      await getMessageList()
      ElMessage.success('已读取消息')
      globalState.updateMessage = tmp;
    }
  }
}


onMounted(()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //刷新表格
  getMessageList()

//监听键盘事件
const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});

const loading = ref(false)
</script>

<template>
  <div v-loading.lock="loading" ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <!-- <div class="absolute top-5 left-10">
      <ElButton type="success" @click="getMessageList">
        <Icon icon="carbon:document-add" />
        <div class="pl-2">{{ t('button.add') }}</div>
      </ElButton>
    </div> -->
    <div  class="h-[100%] bg-white p-7">
      <div class="text-center mb-5 font-bold">消息中心</div>
      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
        <div  class="inline-flex items-center ml-1 mb-1">
            <div class="searchTitle">消息类型</div>
            <el-select size="small"  class="searchItem mr-5" v-model="searchCondition.状态" placeholder="" >
              <el-option v-for="item in ['全部','未读','已读']" :key="item" :label="item" :value="item" />
            </el-select>
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">查询</div>
            </ElButton>
            <ElButton type="warning" @click="onClear">
              <Icon icon="ant-design:clear-outlined" />
              <div class="pl-2">清除</div>
            </ElButton>  
          </div>
        <div  class="flex justify-end items-center mr-6  mb-2">
        
        </div>
      </div>
      <div class="flex">
        <div class="text-red-500">【未读消息】:</div>
        <div class="text-red-500">{{ unreadCount>=99?'99+':unreadCount }}</div>
      </div>
      <el-table ref="userTableRef" header-cell-class-name="tableHeader" :data="messageData" style="width: 100%"
        :height="tableHeight" border stripe>
        <!-- <el-table-column   prop="create_date" label="操作" width="60" >
          <template #default="scope">
            <ElButton v-if="scope.row.msg_status === '未读'" type="success" size="small" @click="handleOper('read', scope.row)">已读</ElButton>
            <div v-else ></div>
          </template>
        </el-table-column> -->
        <el-table-column   label="序号" width="60" >
          <template #default="scope">
            <div>{{ (searchCondition.page - 1) * searchCondition.count + scope.$index + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column   prop="create_date" label="消息日期" width="160" />
        <el-table-column  label="状态" width="90">
          <template #default="scope">
            <div v-if="scope.row.msg_status==='未读'" class="text-red-500 ">【未读】</div>
            <div v-else>【已读】</div>
          </template>
        </el-table-column>
        <el-table-column   prop="msg_type" label="类型" width="120" />
        <el-table-column   prop="msg_type" label="订单类型" width="120" >
          <template #default="scope">
            <div v-if="scope.row.src_obj_name==='oem_order'" >委外订单</div>
            <div v-else-if="scope.row.src_obj_name==='buy_order'">采购订单</div>
            <div v-else-if="scope.row.src_obj_name==='sell_order'">销售订单</div>
          </template>
        </el-table-column>
        <el-table-column   prop="src_attr_value" label="单号" width="120" />
        <el-table-column   prop="msg_text" label="消息内容" />
        <el-table-column fixed="right" :label="t('userTable.operate')" width="120">
          <template #default="scope">
            <div class="flex items-center">
              <ElButton type="primary" size="small" @click="handleOper('info', scope.row)">查看</ElButton>
              <ElButton v-if="scope.row.msg_status === '未读'" type="success" size="small" @click="handleOper('read', scope.row)">已读</ElButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="flex justify-end mt-4 mb-4"
        v-model:current-page="searchCondition.page"
        v-model:page-size="searchCondition.count"
        :page-sizes="[30, 50, 100, 300]"
        :background="true"
        layout="sizes, prev, pager, next"
        :total="totleCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>


  </div>
</template>

<style lang="less" scoped>

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 120px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
</style>
