<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElSelect,ElOption,ElButton, ElTable, ElTableColumn,ElMessageBox, ElInput, ElForm,ElFormItem,ElMessage,ElCheckbox,ElDropdown,ElDropdownItem,ElDropdownMenu,ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSpecListApi,addSpecApi,updateSpecApi,delSpesApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { Dialog } from '@/components/Dialog'
import { getMessageListApi, getOrderWaringListApi, updateMessageApi } from '@/api/tj';
import { useCache } from '@/hooks/web/useCache';
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es';
import { globalState } from '@/api/tool/globalState';

const { push } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(500)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    page: 1,
    count:1
}
const searchCondition = reactive({
  ...defaultCondition,
  reset() {
    Object.assign(this, defaultCondition)
  }
})

const tableData = reactive([
])
const getTojingData = async () => {
    let data = []
    let ret = await getOrderWaringListApi({
        warning_type: '逾期',
        realrole:'sale.list',
        page: 1,
        count:1
    })
    if(ret) {
      data.push({
        type: '销售订单',
        count:ret.other.warning_order_count,
      })
    }

    ret = await getOrderWaringListApi({
        warning_type: '逾期',
        realrole:'purchase.list',
        page: 1,
        count:1
    })
    if(ret) {
      data.push({
        type: '采购订单',
        count:ret.other.warning_order_count,
      })
    }

    ret = await getOrderWaringListApi({
        warning_type: '逾期',
        realrole:'oem.order_list',
        page: 1,
        count:1
    })
    if(ret) {
      data.push({
        type: '委外订单',
        count:ret.other.warning_order_count,
      })
    }

    tableData.splice(0,tableData.length,...data)
}

//开始查询
const onSearch = () => {
  console.log(searchCondition)
  getTojingData()
}
//清除条件
const onClear = () => {
  searchCondition.reset()
}
//更新表高度
const updateTableHeight = () => {
  if (userTableRef.value && rootRef.value) {
    tableHeight.value = rootRef.value.clientHeight - 300
  }
}
//浏览器大小变化
const handleWindowResize = () => {
  updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getTojingData()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getTojingData()
}


//处理表格对象操作
const handleOper = async(type,row) => {
    if (type == 'info') {
        var tmp = ''
        if(row.type == '销售订单') {
            tmp = 'sale.list'
        }
        if(row.type == '采购订单') {
            tmp ='purchase.list'
        }
        if(row.type == '委外订单') {
            tmp ='oem.order_list' 
        }
        push({
            path: '/systemmanage/warningdetail',
            query: {
                warning_type: '逾期',
                realrole:tmp,
            }
        })
    }
}


onMounted(()=>{
  updateTableHeight(); // 首次设置表格高度
  window.addEventListener('resize', handleWindowResize);

  //刷新表格
  getTojingData()

//监听键盘事件
const inputs = document.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('keyup', event => {
      if (event.key === 'Enter') {
        onSearch();
      }
    });
  });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
  window.removeEventListener('resize', handleWindowResize);
});

const loading = ref(false)
</script>

<template>
  <div v-loading.lock="loading" ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
    <div  class="h-[100%] bg-white p-7">
      <div class="text-center mb-5 font-bold">订单逾期中心</div>
      <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
        <div  class="inline-flex items-center ml-1 mb-1">
            <ElButton type="primary" @click="onSearch">
              <Icon icon="ri:phone-find-line" />
              <div class="pl-2">刷新</div>
            </ElButton>
          </div>
        <div  class="flex justify-end items-center mr-6  mb-2">
        
        </div>
      </div>
      <div class="flex flex-col justify-center items-center">
        <el-table ref="userTableRef" header-cell-class-name="tableHeader" :data="tableData" style="width: 50%"
            :height="tableHeight" border stripe>
            <el-table-column   prop="type" label="类型" />
            <el-table-column  prop="count" label="订单条数" />
            <el-table-column fixed="right" :label="t('userTable.operate')" width="120">
            <template #default="scope">
                <div class="flex items-center">
                <ElButton type="primary" size="small" @click="handleOper('info', scope.row)">查看</ElButton>
                </div>
            </template>
            </el-table-column>
        </el-table>
      </div>


    </div>


  </div>
</template>

<style lang="less" scoped>

.nameStyle {
  color: rgb(60, 60, 255);
  cursor: pointer;
}
//搜索标题文本
.searchTitle{
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  color: var(--el-text-color-regular);
  width: 120px;
  text-align: right;
}
.searchTitle::after{
  content: ':';
  margin-left: 4px;
  margin-right: 5px;
}
</style>
