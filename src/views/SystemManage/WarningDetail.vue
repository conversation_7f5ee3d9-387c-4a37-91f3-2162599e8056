<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElDatePicker, ElOption, ElButton, ElTable, ElTableColumn, ElMessageBox, ElInput, ElForm, ElFormItem, ElMessage, ElCheckbox, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPagination } from 'element-plus';
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getSpecListApi, addSpecApi, updateSpecApi, delSpesApi } from '@/api/product'
import { onBeforeMount } from 'vue';
import { Dialog } from '@/components/Dialog'
import { getMessageListApi, getOrderWaringListApi, updateMessageApi } from '@/api/tj';
import { useCache } from '@/hooks/web/useCache';
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es';
import { globalState } from '@/api/tool/globalState';
import { ContentDetailWrap } from '@/components/ContentDetailWrap'

const { push, back,currentRoute } = useRouter()
const { t } = useI18n()
const { wsCache } = useCache()
const appStore = useAppStore()
//rootRef
const rootRef = ref<HTMLElement | null>(null)
//表对象
const userTableRef = ref<InstanceType<typeof ElTable>>()
//表对象高度
const tableHeight = ref(800)
//总条数
const totleCount = ref(0)
//查询条件
//定义搜索条件
const defaultCondition = {
    订单号: '',
    产品编号: '',
    产品名称: '',
    创建人: '',
    开始日期: '',
    warning_type: '',
    realrole:'',
    page: 1,
    count: 30
}
const searchCondition = reactive({
    ...defaultCondition,
    reset() {
        Object.assign(this, defaultCondition)
    }
})

const messageData = reactive([])
const getMessageList = async () => {
    let ret = await getOrderWaringListApi(searchCondition)
    if (ret) {
        messageData.splice(0, messageData.length, ...ret.data)
        totleCount.value = parseInt(ret.count)
    }
}


//开始查询
const onSearch = () => {
    console.log(searchCondition)
    getMessageList()
}
//清除条件
const onClear = () => {
    searchCondition.订单号 = ''
    searchCondition.产品编号 = ''
    searchCondition.产品名称 = ''
    searchCondition.创建人 = ''
    searchCondition.开始日期 = ''
    
}
//更新表高度
const updateTableHeight = () => {
    if (userTableRef.value && rootRef.value) {
        tableHeight.value = rootRef.value.clientHeight - 300
    }
}
//浏览器大小变化
const handleWindowResize = () => {
    updateTableHeight()
}
//page控件发生切换
const handleSizeChange = (val: number) => {
    getMessageList()
}
//page控件发生切换
const handleCurrentChange = (val: number) => {
    getMessageList()
}

const helpName = {
    'sale.list': 'sell_order',
    'purchase.list': 'buy_order',
    'oem.order_list': 'oem_order'
}
const helpNum = {
    'sale.list': '销售单号',
    'purchase.list': '采购单号',
    'oem.order_list': '委外单号'
}
//处理表格对象操作
const handleOper = async (type, row) => {
    console.log(row)
    if (type === 'info') {
        globalState.notification = {
            src_obj_name: helpName[currentRoute.value.query.realrole as string],
            src_attr_value: row[helpNum[currentRoute.value.query.realrole as string]] ,
        }
    }

}

const title = ref('')
onMounted(() => {
    if(currentRoute.value.query.warning_type != undefined) {
        searchCondition.warning_type = currentRoute.value.query.warning_type as string
    }
    if(currentRoute.value.query.realrole!= undefined) {
        searchCondition.realrole = currentRoute.value.query.realrole as string 
    }
    if(searchCondition.realrole == 'purchase.list' && searchCondition.warning_type == '预警') {
        title.value = '采购订单预警明细'
    }
    if(searchCondition.realrole == 'oem.order_list' && searchCondition.warning_type == '预警') {
        title.value = '委外订单预警明细' 
    }
    if(searchCondition.realrole == 'sale.list' && searchCondition.warning_type == '预警') {
        title.value = '销售订单预警明细' 
    }

    if(searchCondition.realrole == 'purchase.list' && searchCondition.warning_type == '逾期') {
        title.value = '采购订单逾期明细'
    }
    if(searchCondition.realrole == 'oem.order_list' && searchCondition.warning_type == '逾期') {
        title.value = '委外订单逾期明细' 
    }
    if(searchCondition.realrole == 'sale.list' && searchCondition.warning_type == '逾期') {
        title.value = '销售订单逾期明细' 
    }

    updateTableHeight(); // 首次设置表格高度
    window.addEventListener('resize', handleWindowResize);

    //刷新表格
    getMessageList()

    //监听键盘事件
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('keyup', event => {
            if (event.key === 'Enter') {
                onSearch();
            }
        });
    });
})
// 在组件销毁前移除窗口大小变化的监听器
onBeforeMount(() => {
    window.removeEventListener('resize', handleWindowResize);
});

const loading = ref(false)
//返回上一页
const baskFront = () => {
    back()
}
</script>

<template>
    <div ref="rootRef" class="absolute top-[20px] right-[20px] left-[20px] bottom-[20px]">
        <div class="h-[100%] bg-white p-7">
            <div class="absolute top-3 left-10">
                <ElButton type="primary" @click="baskFront()" plain>
                    <Icon icon="carbon:export" />
                    <div class="pl-2">返回</div>
                </ElButton>
            </div>
            <div class="text-center mb-5 font-bold">{{ title }}</div>
            <div style1="border: 1px solid rgb(143, 143, 143);" class="pt-5 pl-5 pr-5 pb-1 mb-5 bg-light-200">
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">订单号</div>
                    <el-input size="small" v-model="searchCondition.订单号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品编号</div>
                    <el-input size="small" v-model="searchCondition.产品编号" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">产品名称</div>
                    <el-input size="small" v-model="searchCondition.产品名称" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">创建人</div>
                    <el-input size="small" v-model="searchCondition.创建人" placeholder="" class="searchItem" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <div class="searchTitle">开始日期</div>
                    <el-date-picker v-model="searchCondition.开始日期" type="date" placeholder="" format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD" />
                </div>
                <div class="inline-flex items-center ml-1 mb-1">
                    <ElButton type="primary" @click="onSearch">
                        <Icon icon="ri:phone-find-line" />
                        <div class="pl-2">查询</div>
                    </ElButton>
                    <ElButton type="warning" @click="onClear">
                        <Icon icon="ant-design:clear-outlined" />
                        <div class="pl-2">清除</div>
                    </ElButton>
                </div>
                <div class="flex justify-end items-center mr-6  mb-2">

                </div>
            </div>

            <el-table ref="userTableRef" header-cell-class-name="tableHeader" :data="messageData" style="width: 100%"
                :height="tableHeight" border stripe>
                <el-table-column v-if="currentRoute.query.realrole == 'sale.list'" prop="销售单号" label="销售单号" />
                <el-table-column v-if="currentRoute.query.realrole == 'purchase.list'" prop="采购单号" label="采购单号" />
                <el-table-column v-if="currentRoute.query.realrole == 'oem.order_list'" prop="委外单号" label="委外单号" />
                <el-table-column prop="产品名称" label="产品名称" />
                <el-table-column prop="交货日期" label="交货日期" />

                <el-table-column v-if="currentRoute.query.realrole == 'sale.list'" prop="销售数量" label="销售数量" />
                <el-table-column v-if="currentRoute.query.realrole == 'sale.list'" prop="已发货" label="已发货" />
                <el-table-column v-if="currentRoute.query.realrole == 'sale.list'" prop="未发货" label="未发货" />

                <el-table-column v-if="currentRoute.query.realrole == 'purchase.list'" prop="采购数量" label="采购数量" />
                <el-table-column v-if="currentRoute.query.realrole == 'purchase.list'" prop="已收货" label="收货数量" />
                <el-table-column v-if="currentRoute.query.realrole == 'purchase.list'" prop="未收货" label="未收货数量" />

                <el-table-column v-if="currentRoute.query.realrole == 'oem.order_list'" prop="委外数量" label="委外数量" />
                <el-table-column v-if="currentRoute.query.realrole == 'oem.order_list'" prop="已收货" label="已收货" />
                <el-table-column v-if="currentRoute.query.realrole == 'oem.order_list'" prop="未收货" label="未收货" />


                <el-table-column v-if="currentRoute.query.warning_type == '预警'" prop="预警天数" label="预警天数" />
                <el-table-column v-if="currentRoute.query.warning_type == '逾期'" prop="逾期天数" label="逾期天数" />
                <el-table-column fixed="right" :label="t('userTable.operate')" width="120">
                    <template #default="scope">
                        <div class="flex items-center">
                            <ElButton type="primary" size="small" @click="handleOper('info', scope.row)">查看</ElButton>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination class="flex justify-end mt-4 mb-4" v-model:current-page="searchCondition.page"
                v-model:page-size="searchCondition.count" :page-sizes="[30, 50, 100, 300]" :background="true"
                layout="sizes, prev, pager, next" :total="totleCount" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>


    </div>
</template>

<style lang="less" scoped>
.nameStyle {
    color: rgb(60, 60, 255);
    cursor: pointer;
}

//搜索标题文本
.searchTitle {
    font-size: 0.875rem;
    /* 14px */
    line-height: 1.25rem;
    /* 20px */
    color: var(--el-text-color-regular);
    width: 120px;
    text-align: right;
}

.searchTitle::after {
    content: ':';
    margin-left: 4px;
    margin-right: 5px;
}
</style>
