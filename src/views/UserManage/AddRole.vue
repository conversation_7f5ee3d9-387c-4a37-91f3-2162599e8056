<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElForm,ElFormItem,ElDescriptions, ElDescriptionsItem, ElInput, ElCheckbox,ElButton,ElMessage, FormInstance, FormRules } from 'element-plus';
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { onMounted } from 'vue';
import { addRoleApi,getRoleListApi,updateRoleApi } from '@/api/usermanage'
import { nextTick } from 'process';
import {getAbilityTabMap} from '@/router'
import {checkFormRule} from '@/api/tool'

const { push,currentRoute } = useRouter()
const { t } = useI18n()
const title = ref(t('roleTable.addRole'))
const srcPath = ref('/usermanage/rolemanage')
//界面数据配置
const uiData = reactive([])
//角色数据源
const roleData = reactive({
    name: '',
    note: '',
    routes:[]
})

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: t('msg.noRoleName'), trigger: 'blur' }] ,   
})

//报错角色
const onSaveRole = async ()=>{
    await getAllCheck()
    console.log(roleData.routes)


    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //新增角色
    if (currentRoute.value.query.id == undefined)
    {
        const ret = await addRoleApi({
            name: roleData.name,
            note: roleData.note,
            routes:roleData.routes
        })
        if(ret)
        {
            ElMessage.success('新增成功')

            push({
                path: currentRoute.value.name === 'HumanAddRole'? '/humanmanage/rolemanage':'/usermanage/rolemanage'
            })
        }
    }
    else //修改角色
    {
        const ret = await updateRoleApi({
            ids: [currentRoute.value.query.id],
            name: roleData.name,
            note: roleData.note,
            routes:roleData.routes
        })
        if(ret)
        {
            ElMessage.success('修改成功')
            push({
                path: currentRoute.value.name === 'HumanAddRole'? '/humanmanage/rolemanage':'/usermanage/rolemanage'
            })

        }
    }
}

//拿到所有勾选了的权限
const getAllCheck = async()=>{  
    roleData.routes.splice(0,roleData.routes.length)
    for(let root of uiData)
    {
        let one = {}
        one.name = root.name
        one.title = root.title
        one.routes = []
        one.others = []
        for(let parent of root.routes)
        {
            if(!parent.check)
                continue
            one.routes.push(parent.path)
            let arrTmp = []
            let bCheck = false
            for(let child of parent.children)
            {
                if(child.meta.hidden)
                {
                    arrTmp.push(parent.path+'/'+child.path)
                    continue
                }
                if(child.check)
                {
                    one.routes.push(parent.path+'/'+child.path)
                    bCheck = true
                }
            }
            //如果有构造子集，则暂时直接把所有操作页面一起加上
            if(bCheck)
            {
                one.routes.push(...arrTmp)
            }

            //添加额外功能权限
            if(parent.otherroles != undefined)
            {
                for(let other of parent.otherroles)
                {
                    if(other.check)
                    {
                        one.others.push(other.name)
                    }
                }
            }

        }
        if(one.routes.length<=0)
            continue
        roleData.routes.push(one)
    }    
    return roleData.routes
}


const getCheck = async(item,arrCheck)=>{
    if(item.check)
    {
        arrCheck.push(item)
    }
    if(item.routes && item.check)
    {
        for(let one of item.routes)
        {
            await getCheck(one,arrCheck)
        }
    }
    if(item.children && item.check)
    {
        for(let one of item.children)
        {
            await getCheck(one,arrCheck)
        }
    }
    //处理额外权限
    if(item.otherroles && item.check)
    {
        for(let one of item.otherroles)
        {
            await getCheck(one,arrCheck)
        }
    }
    return 
}

//处理CHECK事件
const onCheckChange = async(value,item,parent,root)=>{
    //勾上父节点
    if(parent != undefined)
    {
        //校验父节点下是否所有都取消了CHECK  
        if(value)
        {
            parent.check = value
        }
        else
        {
            let arrCheck = []
            await getCheck(parent,arrCheck)            
            if(arrCheck.length <= 1)
            {
                parent.check = false
            }
        }
    }
    //勾上root节点
    if(root != undefined)
    {
        //校验父节点下是否所有都取消了CHECK  
        if(value)
        {
            root.check = value
        }
        else
        {
            let arrCheck = []
            await getCheck(root,arrCheck)            
            if(arrCheck.length <= 1)
            {
                root.check = false
            }
        }
    }
    onCheckFun(value,item,parent)
}
const onCheckFun = (value,item,parent)=>{
    if(item.meta != undefined)
    {
        if(item.meta.hidden != true)
            item.check = value
    }


    if(item.routes)
    {
        for(let one of item.routes)
        {
            onCheckFun(value,one,parent)
        }
    }

    if(item.children)
    {
        for(let one of item.children)
        {
            onCheckFun(value,one,parent)
        }
    }

}


const onCheckOtherRole = (value,item,parent,root)=>{

}

//设置指定节点check状态
const onUpdateSelCheck = async(item,arrSrc)=>{
    for(let root of item)
    {
        for(let parent of root.routes)
        {
            parent.check = false
            //校验parent层级是否check
            if(checkSet(parent.path,arrSrc))
            {
                parent.check = true
            }
            //校验子层级
            if(parent.children)
            {
                for(let one of parent.children)
                {
                    if(one.meta.hidden)
                        continue
                    one.check = false
                    let path = parent.path+'/'+one.path
                    if(checkSet(path,arrSrc))
                    {
                        one.check = true
                    }
                }
            }
            //校验额外权限
            if(parent.otherroles)
            {
                for(let one of parent.otherroles)
                {
                    one.check = false
                    if(checkOtherSet(one.name,arrSrc))
                    {
                        one.check = true
                    }
                }
            }
        }
    }
}
//检测是否设置
const checkSet = (path,arrSrc)=>{
    for(let one of arrSrc)
    {
        for(let item of one.routes)
        {
            if(item  == path)
            {
                return true
            }
        }
    }
}

//检测ohter是否设置
const checkOtherSet = (path,arrSrc)=>{
    for(let one of arrSrc)
    {
        if(one.others == undefined)
            continue
        for(let item of one.others)
        {
            if(item  == path)
            {
                return true
            }
        }
    }
}

// const updateSelCheck = async(item,arrSrc)=>{
//     if(arrSrc.includes(item.name))
//     {
//         item.check = true
//     }

//     if(item.children)
//     {
//         for(let one of item.children)
//         {
//             await updateSelCheck(one,arrSrc)
//         }

//     }
// }


onMounted(async()=>{

    //导入路由数据
    Object.assign(uiData,getAbilityTabMap())
    console.log(uiData)

    if(currentRoute.value.query.src != undefined)
    {
        srcPath.value = currentRoute.value.query.src as string
    }
    if (currentRoute.value.query.id == undefined) {
        title.value = t('roleTable.addRole')
        await onUpdateSelCheck(uiData,[])
    }
    else
    {
        title.value = t('roleTable.modifyRole')
        let ret = await getRoleListApi({
            ids:[currentRoute.value.query.id as number],
            page: 1,
            count: 1000
        })
        if(ret)
        {
            if(ret.data.length<=0)
            {
                //提示未找到该角色
                ElMessage.error(t('msg.noRole'))
                return
            }
            roleData.name = ret.data[0].name
            roleData.note = ret.data[0].note
            roleData.routes.splice(0,roleData.routes.length,...ret.data[0].routes)

            await onUpdateSelCheck(uiData,roleData.routes)
        }
    }
})

</script>

<template>
    <ContentDetailWrap :title="title" @back="push(srcPath)">
        <template #right>
            <ElButton color="#409EFF" style="color: #fff;"  @click="onSaveRole">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="roleData" ref="ruleFormRef">
            <el-descriptions class="flex-1" :column="2" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('roleTable.name')" class="flex">
                    <el-form-item prop="name">
                        <el-input :disabled="currentRoute.query.id != undefined" v-model="roleData.name" clearable />
                    </el-form-item>                    
                </el-descriptions-item>
            </el-descriptions>
        </el-form>


        <div v-for="item in uiData" class="flex text-center mt-1" :key="item.value">
            <div class="boxitem w-[150px]">
                <el-checkbox :label="t(item.title)" v-model="item.check" @change="onCheckChange($event,item)"/>
            </div>
            <div class="w-[100%]">
                <div class="boxitem2 w-[100%] !border-l-0 !flex-nowrap" v-for="subItem in item.routes" :key="subItem.path">
                    <el-checkbox class="w-[130px]"  v-model="subItem.check" :label="t(subItem.meta.title)"  @change="onCheckChange($event,subItem,item)"/>
                    <div class="boxitem flex  flex-wrap w-[100%] !justify-start p-3">
                        <el-checkbox v-model="ss.check"  v-for="ss in subItem.children.filter(ss=>!ss.meta.hidden)" :key="ss" :label="t(ss.meta.title)"  @change="onCheckChange($event,ss,subItem,item)"/>
                        <el-checkbox style="color: rgb(255, 181, 83) !important;" v-model="other.check"  v-for="other in subItem.otherroles" :key="other.name" :label="other.name"  @change="onCheckOtherRole($event,other,subItem,item)"/>
                    </div>
                </div>
            </div>

        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 10px !important;
    min-width: 100px;
}

:deep(.conentStyle) {
    width: 90%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

.boxitem {
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgba(209, 213, 219, var(--tw-border-opacity));
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    align-items: center;
}

.boxitem2 {
    &:extend(.boxitem);
    justify-content: left;
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
}

:deep(.el-checkbox__label) {
    //font-size: 10px !important; 
    font-weight: bold
}
</style>
