<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { ElForm,ElFormItem,ElDescriptions, ElDescriptionsItem, ElInput, ElRadio,ElRadioGroup,ElButton,ElMessage, FormInstance, FormRules } from 'element-plus';
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { onMounted } from 'vue';
import { addRoleApi,getRoleListApi,updateRoleApi } from '@/api/usermanage'
import {getAbilityTabMap} from '@/router'
import {checkFormRule} from '@/api/tool'

const { push,currentRoute,back } = useRouter()
const { t } = useI18n()

//界面数据配置
const uiData = reactive([])
//角色数据源
const roleData = reactive({
    name: '',
    note: '',
    routes:[],
    rights:[]
})

//表单ref
const ruleFormRef = ref<FormInstance>()
//表单规则
const rules = reactive<FormRules>({
    name: [{ required: true, message: t('msg.noRoleName'), trigger: 'blur' }] ,   
})

//保存角色权限
const onSaveRole = async ()=>{
    await getAllCheck()
    console.log(roleData.rights)

    const rule = await checkFormRule(ruleFormRef.value)
    if(!rule)
    {
        ElMessage.warning(t('msg.checkRule'))
        return
    }

    //新增角色
    if (currentRoute.value.query.id == undefined)
    {

    }
    else //修改角色
    {
        const ret = await updateRoleApi({
            ids: [currentRoute.value.query.id],
            name: roleData.name,
            note: roleData.note,
            rights:roleData.rights
        })
        if(ret)
        {
            ElMessage.success('修改成功')
            push({
                path: currentRoute.value.name === 'HumanAddRoleData'? '/humanmanage/rolemanage':'/usermanage/rolemanage'
            })

        }
    }
}

//拿到所有勾选了的权限
const getAllCheck = async()=>{  
    roleData.rights.splice(0,roleData.rights.length)
    for(let root of uiData)
    {
        for(let parent of root.routes)
        {
            for(let child of parent.children)
            {
                if(child.meta.hidden)
                {
                    continue
                }
                roleData.rights.push({
                    name:child.meta.title,
                    value:child.数据权限
                })
            }
        }
    }    
    return roleData.routes
}


//设置指定节点check状态
const onUpdateSelCheck = async(item,arrSrc)=>{
    for(let root of item)
    {
        for(let parent of root.routes)
        {
            //校验子层级
            if(parent.children)
            {
                for(let one of parent.children)
                {
                    if(one.meta.hidden)
                        continue
                    one.数据权限 = checkSet(one.meta.title,arrSrc)
                }
            }
        }
    }
}
//检测是否设置
const checkSet = (path,arrSrc)=>{
    for(let one of arrSrc)
    {
        if(one.name == path)
        {
            return one.value
        }
    }
    return '自己'
}



onMounted(async()=>{
    //导入路由数据
    Object.assign(uiData,getAbilityTabMap())
    console.log(uiData)

    if (currentRoute.value.query.id == undefined) {

        await onUpdateSelCheck(uiData,[])
    }
    else
    {

        let ret = await getRoleListApi({
            ids:[currentRoute.value.query.id as number],
            page: 1,
            count: 1000
        })
        if(ret)
        {
            if(ret.data.length<=0)
            {
                //提示未找到该角色
                ElMessage.error(t('msg.noRole'))
                return
            }
            roleData.name = ret.data[0].name
            roleData.note = ret.data[0].note
            roleData.rights.splice(0,roleData.rights.length,...ret.data[0].rights)

            await onUpdateSelCheck(uiData,roleData.rights)
        }
    }
})

</script>

<template>
    <ContentDetailWrap title="编辑角色数据权限" @back="back()">
        <template #right>
            <ElButton type="primary"  @click="onSaveRole">
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <el-form :rules="rules" :model="roleData" ref="ruleFormRef">
            <el-descriptions class="flex-1" :column="2" border>
                <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle"
                    :label="t('roleTable.name')" class="flex">
                    <el-form-item prop="name">
                        <el-input :disabled="currentRoute.query.id != undefined" v-model="roleData.name" clearable />
                    </el-form-item>                    
                </el-descriptions-item>
            </el-descriptions>
        </el-form>


        <div v-for="item in uiData" class="flex text-center mt-1" :key="item.value">
            <div class="boxitem w-[150px]">
                <div>{{ t(item.title) }}</div>
            </div>
            <div class="w-[100%]">
                <div class="boxitem2 w-[100%] !border-l-0 !flex-nowrap" v-for="subItem in item.routes" :key="subItem.path">
                    <div class="w-[130px]">{{ t(subItem.meta.title) }}</div>
                    <div class="boxitem flex flex-col  flex-wrap w-[100%] !justify-start !items-start p-3">
                        <div v-for="ss in subItem.children.filter(ss=>!ss.meta.hidden)" :key="ss" class="flex justify-start items-center boxitem2">
                            <div class="w-[150px] text-left">{{ t(ss.meta.title) }}</div>
                            <el-radio-group v-model="ss.数据权限" class="ml-3">
                                <el-radio label="全部">全部</el-radio>
                                <el-radio label="部门">部门</el-radio>
                                <el-radio label="自己">自己</el-radio>
                            </el-radio-group>
                        </div>                      
                        
                    </div>
                </div>
            </div>

        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
:deep(.labelStyle) {
    width: 10px !important;
    min-width: 100px;
}

:deep(.conentStyle) {
    width: 90%;
}

:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}

.boxitem {
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgba(209, 213, 219, var(--tw-border-opacity));
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    align-items: center;
}

.boxitem2 {
    &:extend(.boxitem);
    justify-content: left;
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
}

:deep(.el-checkbox__label) {
    //font-size: 10px !important; 
    font-weight: bold
}
</style>
