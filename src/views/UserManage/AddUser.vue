<script setup lang="ts">
import { ContentDetailWrap } from '@/components/ContentDetailWrap'
import { ref, reactive } from 'vue'
import { ElProgress,ElUpload,ElButton, ElDescriptions, ElDescriptionsItem, ElImage, ElCascader, ElSelect, ElOption, ElDatePicker, ElNotification, ElInput,ElMessage } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { addUserApi, updateUserApi, getUserListApi,getDepartmentListApi,getRoleListApi, getJobListApi } from '@/api/usermanage'
import { onMounted } from 'vue'
import { getOssSignApi,ossUpload } from '@/api/oss'
import { getTodayDate } from '@/api/tool'


const { push, currentRoute } = useRouter()
const { t } = useI18n()
const loading = ref(false)
//部门选择器属性修改
const props = {
    multiple: true,
    checkStrictly: true,
    label: 'name',
    value:'id', 
    children: 'sub_dept',
    emitPath:false
}
//角色选择器
const propsRole = {
    multiple: true,
    checkStrictly: true,
    label: 'name',
    value:'id', 
    children: 'children',
    emitPath:false
}
//当前界面绑定的用户数据
const userData = reactive({
    username: '',          //用户名
    resident_name: '',    //姓名
    depts: [],                  //部门
    roles: [],                   //角色
    job: '',                    //职务
    nation: '汉族',             //民族
    birthday: '',               //生日
    sex: '男',                    //性别
    age: '',                    //年龄
    marry_status: '',
    phone: '',
    telephone: '',
    email: '',
    sos_man: '',
    sos_phone: '',
    resident_pic1: '',
    resident_pic2: '',
    resident_id: '',
    resident_address: '',
    address: '',
    resident_begin: '',
    resident_end: '',
    edu_status: '',
    edu_school: '',
    edu_subject: '',
    entry_date: '',
    remark: '',
    password: '123456',
    pwd_check: '123456',
    base_pay: 0,
    extra_pay: 0,
    "funcs": [],
    emp_status: '在职', //在职
    leave_date: '',
    staff_date: '',
    type:'正式员工', //员工类型   '试用员工','临时员工','正式员工','实习生','兼职员工','退休返聘'
    work_date: '', //参工日期
    leave_note:''//离职原因
})

//页面标题
const title = ref("")
//来源url
const srcPath = ref('/usermanage/usermanage')


//角色数据源
const checkRoleList = reactive([])
//部门数据源
const checkDeptList = reactive([])

//职位 
const jobLiST = reactive(
    [  
    "设计",  
    "财务",  
    "出纳",  
    "会计",  
    "工程文员",  
    "员工",  
    "PMC",  
    "业务助理",  
    "总经理",  
    "总经理助理",  
    "经理",  
    "总监",  
    "主管",  
    "包装组长",  
    "组长",  
    "司机",  
    "行政",  
    "人事",  
    "保安",  
    "保洁员",  
    "网管",  
    "电工",  
    "助理",  
    "行政经理",  
    "行政主管",  
    "业务经理",  
    "业务主管",  
    "业务员",  
    "业务助理",  
    "工人"  
    ]
)

//获取职务数据
const getJobList = async () => {
  const res = await getJobListApi({
    page: 1,
    count: 1000
  })
  if (res) {
      console.log(res.data);
      jobLiST.splice(0, jobLiST.length);
      for (let one of res.data) {
          jobLiST.push(one.name);
      }
    
    // jobLiST.splice(0,jobLiST.length,...res.data)
  }

}

//性别数据源
const sexList = reactive([
    '男',
    '女'
])
//婚姻数据源
const marryList = reactive([
    '已婚',
    '未婚',
    '离异',
    '丧偶'
])

//校验表单规则
const checkRule = (): [boolean, string] => {
    if (userData.username == "") {
        return [false, t('msg.noUserID')]
    }
    else if (userData.resident_name == "") {
        return [false, t('msg.noName')]
    }
    else if (userData.password == "" || userData.pwd_check == "") {
        return [false, t('msg.pwdinput')]
    }
    else if (userData.password != userData.pwd_check) {
        return [false, t('msg.pwdcheck')]
    }
    else if (userData.depts.length == 0) {
        return [false, '请选择部门']
    }
    else if (userData.roles.length == 0) {
        return [false, '请选择角色']
    }
    else if (userData.job == "") {
        return [false, '请选择职务']
    }
    else if (userData.entry_date == "") {
        return [false, '请选择入职日期']
    }
    
    if (currentRoute.value.query.emp_status == '离职' && userData.leave_date == '') {
        return [false, '请选择离职日期']
    }
    if (currentRoute.value.query.emp_status == '离职' && userData.leave_note == '') {
        return [false, '请输入离职原因']
    }
    
    return [true, '']
}

//新增用户
const onAddUser = async () => {
    //拿到数据
    const rule = checkRule()
    if (!rule[0]) {
        ElNotification({
            title: t('msg.notify'),
            message: rule[1],
            type: 'error',
            duration: 1000
        })
        return
    }
    let path = currentRoute.value.name === 'UserManage'?'/usermanage/usermanage':'/humanmanage/usermanage'

    console.log(userData)
    if (currentRoute.value.query.id == undefined) {
        const ret = await addUserApi(userData)
        if (ret) {
            console.log(ret)
            push(path)
        }
    }
    else {
        userData.ids = [userData.id]
        const ret = await updateUserApi(userData)
        if (ret) {
            console.log(ret)
            push(path)
        }
    }
}

//查询组织信息
const getDepartmentTreeInfo = async () => {
  const ret = await getDepartmentListApi({})
  console.log(ret)
  if (ret) {
    checkDeptList.splice(0, checkDeptList.length, ...ret.data.all_depts)
  }
}
//查询角色信息
const getRoleList = async () => {
  const res = await getRoleListApi({
    page: 1,
    count: 1000
  })
  if (res) {
    console.log(res.data)
    checkRoleList.splice(0,checkRoleList.length,...res.data)
  }

}

onMounted(async () => {
    //更新组织信息
    getDepartmentTreeInfo()
    //更新角色信息
    getRoleList()
    //更新职务信息
    getJobList();

    if(currentRoute.value.query.src != undefined)
    {
        srcPath.value = currentRoute.value.query.src as string
    }
    if (currentRoute.value.query.id == undefined) {
        title.value = t("userTable.addUser")
        userData.entry_date = getTodayDate()
        if (currentRoute.value.query.dep != undefined) {
            userData.depts.push(currentRoute.value.query.dep as string)
        }
    }
    else {
        if(currentRoute.value.query.type == 'info')
        {

                title.value = t('userTable.modifyUser')
        }
        else
        {
            if (currentRoute.value.query.emp_status == '离职') {
                title.value = '员工离职'
            }
            else
                title.value = '查看员工'
        }
        

        //查询人员信息
        const ret = await getUserListApi({
            "ids": [currentRoute.value.query.id],
            "page": 1,
            "count": 100
        })
        console.log(ret)
        if (ret) {
            if (ret.data.length < 0) {
                //提示获取查询失败
                ElMessage({
                    type: 'error',
                    message: t('msg.searchFalse'),
                })
                return
            }
            else
            {
                Object.assign(userData, ret.data[0]);
                userData.pwd_check = userData.password
                userData.leave_date = getTodayDate()
                userData.emp_status = '离职'
            }
        }

        if(currentRoute.value.query.type === 'info') //查看模式
        {
            let components = document.querySelectorAll('.el-input__inner');

            components.forEach((component) => {
                component.setAttribute('disabled', true);
            });
            components = document.querySelectorAll('.el-input__wrapper');
            components.forEach((component) => {
                component.classList.add('infomode')
            });
            const suffixElements = document.querySelectorAll('.el-input__suffix');
            suffixElements.forEach(suffixElement => {
                suffixElement.style.display = 'none';
            });
        }


    }
})


const uploadImg = async(file,type) => {
    let path = ''
    if(type == 'photo')
    {
        path = 'photo/'
        headProgress.value = 0
    }
    else if(type == 'sfz1' )
    {
        path = 'sfz/'
        sfz1Progress.value = 0
    }
    else if(type == 'sfz2')
    {
        path = 'sfz/'
        sfz2Progress.value = 0
    }
    const ret =await getOssSignApi({upload_dir:path})
    if(ret)
    {    
        const end = await ossUpload(ret.data.token,file.file,path,(pro)=>{
            console.log('pppp',pro)
            
            if(type == 'photo')
            {
                headProgress.value = pro.progress*100
            }
            else if(type == 'sfz1')
            {
                sfz1Progress.value = pro.progress*100
            }
            else if(type == 'sfz2')
            {
                sfz2Progress.value = pro.progress*100
            }
        })
        console.log('完成',end)
        if(type == 'photo')
        {
            userData.photo = end.url
        }
        else if(type == 'sfz1')
        {
            userData.resident_pic1 = end.url
        }
        else if(type == 'sfz2')
        {
            userData.resident_pic2 = end.url
        }
        
    }
}

const colors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 },
]

//头像进度
const headProgress = ref(0)
//身份证1进度
const sfz1Progress = ref(0)
//身份证2进度
const sfz2Progress = ref(0)

</script>

<template>
    <ContentDetailWrap :title="title" @back="push(srcPath)">

        <template #right>
            <ElButton v-show="currentRoute.query.type != 'info'" type="primary" color="#409EFF" style="color: white;" :loading="loading" @click="onAddUser">
                <Icon class="mr-0.5" icon="carbon:save" />
                {{ t('exampleDemo.save') }}
            </ElButton>
        </template>
        <div class="flex justify-center">
            <div class="w-[80%] <lg:w-[100%] flex border-dark-900">
                <el-descriptions class="flex-1" :column="2" border>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="离职日期" :span="2" v-if="currentRoute.query.emp_status == '离职'">
                        <el-date-picker v-model="userData.leave_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD"  :clearable="false"/>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' label="离职原因" :span="2" v-if="currentRoute.query.emp_status == '离职'">
                        <el-input v-model="userData.leave_note"   :autosize="{ minRows: 10, maxRows: 4 }"
                            type="textarea" />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="员工账号"
                        class="flex" >
                        <el-input v-model="userData.username"  :disabled="currentRoute.query.emp_status == '离职'"/>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="姓名">
                        <el-input v-model="userData.resident_name"   :disabled="currentRoute.query.emp_status == '离职'"/>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="所属部门" :span="2">
                        <el-cascader v-model="userData.depts" class="w-[100%]" :options="checkDeptList" :props="props" :show-all-levels="false" :disabled="currentRoute.query.emp_status == '离职'"
                              />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="角色" :span="2">
                        <el-cascader v-model="userData.roles" class="w-[100%]" :options="checkRoleList" :props="propsRole" :show-all-levels="false" :disabled="currentRoute.query.emp_status == '离职'"
                              />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="员工类型" :span="2">
                        <el-select class="w-[100%]" v-model="userData.type">
                            <el-option v-for="item in ['试用员工','临时员工','正式员工','实习生','兼职员工','退休返聘']" :key="item" :label="item" :disabled="currentRoute.query.emp_status == '离职'"
                                :value="item" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="在职状态" :span="2">
                        <el-select class="w-[100%]" v-model="userData.emp_status" :disabled="currentRoute.query.emp_status == '离职'">
                            <el-option v-for="item in ['在职','离职']" :key="item" :label="item"
                                :value="item" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="职务">
                        <el-select class="w-[100%]" v-model="userData.job" filterable >
                            <el-option v-for="item in jobLiST" :key="item" :label="item" 
                                :value="item" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="民族" :span="2">
                        <el-input v-model="userData.nation"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="出生日期">
                        <el-date-picker v-model="userData.birthday" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="性别">
                        <el-select class="w-[100%]" v-model="userData.sex">
                            <el-option v-for="item in sexList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="年龄">
                        <el-input v-model="userData.age"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="婚否">
                        <el-select class="w-[100%]" v-model="userData.marry_status">
                            <el-option v-for="item in marryList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="手机">
                        <el-input v-model="userData.phone"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="电话">
                        <el-input v-model="userData.telephone"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="邮箱">
                        <el-input v-model="userData.email"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="紧急联系人">
                        <el-input v-model="userData.sos_man"   />
                    </el-descriptions-item>

                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="登录密码">
                        <el-input v-model="userData.password"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' class-name="conentStyle" label="确认密码">
                        <el-input v-model="userData.pwd_check"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle require' label="入职时间" :span="1">
                        <el-date-picker v-model="userData.entry_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" :clearable="false"/>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="参工时间" :span="1">
                        <el-date-picker v-model="userData.work_date" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" :clearable="false"/>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' class-name="conentStyle" label="紧急联系电话1" :span="2">
                        <el-input v-model="userData.sos_phone"   />
                    </el-descriptions-item>

                    <el-descriptions-item label-class-name='labelStyle' label="身份证正面">
                        <div class="flex justify-center items-center">
                            <el-upload
                                :disabled="currentRoute.query.type == 'info'"
                                class="avatar-uploader "                                
                                :http-request="(file) => uploadImg(file, 'sfz1')"
                                :show-file-list="false"
                                :accept="'image/*'" 
                            >
                                <img v-if="userData.resident_pic1" :src="userData.resident_pic1" class="object-fill w-[230px] h-[150px]" />
                                <Icon v-else-if="currentRoute.query.type != 'info'" class="avatar-uploader-icon" icon="fluent:add-12-filled" />
                                <el-progress v-if="sfz1Progress>0&&sfz1Progress<1" type="dashboard" :percentage="sfz1Progress" :color="colors" />
                            </el-upload>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="身份证背面">
                        <div class="flex justify-center items-center">
                            <el-upload
                                :disabled="currentRoute.query.type == 'info'"
                                class="avatar-uploader "                                
                                :http-request="(file) => uploadImg(file, 'sfz2')"
                                :show-file-list="false"
                                :accept="'image/*'" 
                            >
                                <img v-if="userData.resident_pic2" :src="userData.resident_pic2" class="object-fill w-[230px] h-[150px]" />
                                <Icon v-else class="avatar-uploader-icon" icon="fluent:add-12-filled" />
                                <el-progress v-if="sfz2Progress>0&&sfz2Progress<1" type="dashboard" :percentage="sfz2Progress" :color="colors" />
                            </el-upload>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="身份证号" :span="2">
                        <el-input v-model="userData.resident_id"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="身份证地址">
                        <el-input v-model="userData.resident_address"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="居住地" :span="2">
                        <el-input v-model="userData.address"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="身份证签发日期">
                        <el-date-picker v-model="userData.resident_begin" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="身份证有效日期">
                        <el-date-picker v-model="userData.resident_end" type="date" placeholder="" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="学历">
                        <el-input v-model="userData.edu_status"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="毕业院校">
                        <el-input v-model="userData.edu_school"   />
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name='labelStyle' label="专业" :span="2">
                        <el-input v-model="userData.edu_subject"   />
                    </el-descriptions-item>

                    <el-descriptions-item label-class-name='labelStyle' label="备注" :span="2">
                        <el-input v-model="userData.remark"   :autosize="{ minRows: 10, maxRows: 4 }"
                            type="textarea" />
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 显示身份证照片 -->
                <div class="flex items-start w-[150px]">
                    <el-upload
                        class="avatar-uploader"
                        :http-request="(file) => uploadImg(file, 'photo')"
                        :show-file-list="false"
                        :accept="'image/*'" 
                    >
                        <img v-if="userData.photo" :src="userData.photo" class="xiangpian" />
                        <Icon v-else class="avatar-uploader-icon" icon="fluent:add-12-filled" />
                        <el-progress v-if="headProgress>0&&headProgress<1" type="dashboard" :percentage="headProgress" :color="colors" />
                    </el-upload>


                </div>
            </div>
        </div>
    </ContentDetailWrap>
</template>

<style lang="less" scoped>
// :deep(.contentStyle){
//     width: 70% !important;
// }

:deep(.labelStyle) {
    width: 15% !important;
}

:deep(.conentStyle) {
    width: 30%;
}


.xiangpian {
    padding: 0 5px;
    width: 178px;
    height: 178px;
}





/* 只针对 .require 元素添加伪元素 */
:deep(.el-descriptions__cell.labelStyle.require:after) {
    content: '*';
    color: red;
    margin-left: 4px;
}


.avatar-uploader .avatar {
  width: 4;
  height: 17px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 2px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  scale: 1.5;
}

//查看模式
:deep(.infomode){
    border: none; /* 设置边框为 none */
    border-radius: 0; /* 可以选择设置圆角为 0，如果需要的话 */
    box-shadow: none; /* 如果有阴影，也可以设置为 none */
}
</style>