<script setup lang="ts">
import { ref } from 'vue';
import { ElRow, ElCol, ElButton } from 'element-plus';
import 'element-plus/dist/index.css';
import { useRouter } from 'vue-router'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()

const { currentRoute, push, back } = useRouter();

const menuItems = ref([
  { icon: 'streamline:information-desk-customer', title: '销售订单', color: '#FF6B6B' ,path:'/mobile_salemanage/salemanage'},
  { icon: 'hugeicons:sale-tag-02', title: 'PMC交期审核', color: '#FFD93D' ,path:'/mobile_salemanage/pmcsalecheck'},
  { icon: 'f7:purchased-circle', title: '生产经理交期审核', color: '#6BCB77' ,path:'/mobile_salemanage/productmgrsalecheck'},
  { icon: 'emojione-monotone:outbox-tray', title: '业务经理审核', color: '#AB47BC' ,path:'/mobile_salemanage/projectmgrsalecheck'},
]);

const handleMenuClick = (path) => {
  if(path != undefined)
    push({ path: path });
};
</script>

<template>
  <div class="container">
    <!-- <el-button icon="el-icon-arrow-left" @click="back" class="back-button">返回</el-button> -->
    <el-button @click="back">
      <Icon icon="ep:arrow-left" class="mr-5px" />
      {{ t('common.back') }}
    </el-button>
    <div class="header">
      <h1>销售管理主页</h1>
    </div>
    <el-row :gutter="10">
      <el-col :span="24" v-for="(item, index) in menuItems" :key="index" class="menu-item " @click="handleMenuClick(item.path)">
        <Icon :icon="item.icon" :style="{ backgroundColor: item.color, width: '40px', height: '40px', borderRadius: '50%', marginRight: '10px', marginLeft:'20px' }"/>
        <div class="title">{{ item.title }}</div>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="less" scoped>
.container {
  padding: 1px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin: 1px 0 20px; /* 调整标题栏的上下边距 */
  color: #333;
}

.back-button {
  font-size: 24px;
  margin-bottom: 20px; /* 调整按钮与标题栏之间的距离 */
}

.el-row {
  margin-bottom: 10px; /* 纵向间距 */
}

.el-row:last-child {
  margin-bottom: 0;
}

.menu-item {
  margin-right: 0;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row; /* 更改为水平方向 */
  align-items: center;
  justify-content: flex-start; /* 调整对齐方式 */
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;

  .icon {
    font-size: 50px;
    color: #fff;
    margin-right: 20px; /* 调整图标和标题之间的间距 */
    padding: 10px; /* 调整图标内边距 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
  }
}
</style>
