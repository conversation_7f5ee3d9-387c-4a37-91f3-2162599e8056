import { getGUID,decodeString } from '@/api/tool';
import {  type App } from 'vue'
import { useCache } from '@/hooks/web/useCache'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { resetRouter } from '@/router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { encode,decode  } from "@msgpack/msgpack"
import { useAppStore } from '@/store/modules/app'
import { cloneDeep } from 'lodash-es';

const { wsCache } = useCache()
const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()

let socket: WebSocket | null = null;
let isPageVisible = true;
const socketUrl = import.meta.env.VITE_WS_URL;

let lastHeartbeatTime = Date.now()
let timmerReConnect:NodeJS.Timeout

let bCloseWS = false

let isCreatingWebSocket = false;

function createWebSocket() {
    if(bCloseWS || isCreatingWebSocket)
        return

    isCreatingWebSocket = true;

    // 如果已存在连接，则先关闭旧连接
    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.close();
    }
    //清理历史map 
    mapCallback?.clear()

    // 创建新的WebSocket连接
    socket = new WebSocket(socketUrl);
    socket.addEventListener('open', () => {
        console.log('WebSocket连接成功',arrayHisCmd.length,socket.readyState);
        clearTimeout(timmerReConnect)

       // setTimeout(() => {
                    //连接成功之后重新调用之前的方法
            while(arrayHisCmd.length>0)
            {
                const one = arrayHisCmd.pop()
                sendCommandEx(one.resolve,one.reject, one.type,one.command,one.params,one.content)
            }
       // }, 500);

    });

    socket.addEventListener('message', (event) => {
        // 在这里处理接收到的消息
        

        //转发处理
        try{
            //更新上次收到心跳时间
            lastHeartbeatTime = Date.now()

            if (event.data instanceof Blob) {
                const reader = new FileReader();        
                reader.onload = function () {
                    if (reader.result instanceof ArrayBuffer) {
                    const uint8Array = new Uint8Array(reader.result);        
                    // 使用 MsgPack 库解码为 JavaScript 对象
                    const ret = decode(uint8Array);     
                    //console.log('---',ret)   
                    if(ret.response.code === '9999') //需要退出
                    {
                        bCloseWS = true
                        console.log('---',ret)
                        ElMessageBox.alert('退出系统:->>>'+ret.response.message, '提示', {
                        confirmButtonText: '确定',
                        callback: () => {
                            //如果TOKEN 错误或者超时，则需要退出登录
                            wsCache.clear()
                            tagsViewStore.delAllViews()
                            resetRouter() // 重置静态路由表
                            location.reload()
                            // window.location.reload()
                            }
                        })


                        return
                    }

                    //心跳不处理内容
                    if(ret.context.route == '/erp/user/heartbeat')
                    {
                        return
                    }
                    else if(ret.context.route == '/erp/home/<USER>') //服务器通知
                    {
                        if(ret.context.guuid == undefined) //不是发起的回调
                        {
                            ElNotification({
                                title: '通知',
                                message: ret.response.data.note                                ,
                                type: 'error',
                                duration:0,
                                position: 'bottom-right',
                              })
                            return
                        }
                    }



                    console.log('收到消息:'+ret.context.route+'------>', ret);

                    if(mapCallback?.has(ret.context.guuid))
                    {
                        const callback = mapCallback.get(ret.context.guuid)
                        if(callback?.length>0)
                        {
                            if(ret.response.code === '0000') //正常
                            {
                                ret.response.context = ret.context //把外层的数据传到里面

                                //特殊处理catag目录路由
                                
                                if(ret.context.route === '/pdt/categ/list')
                                {
                                    const msg = decodeURIComponent(decodeString(ret.response.data.all_categs,true))
                                    ret.response.data.all_categs = JSON.parse(msg)
                                }
                                //产品查询的解压
                                // if(ret.response.context.route == '/erp/pdt/list')
                                // {
                                //     const msg = decodeURIComponent(decodeString(ret.response.data,true))
                                //     ret.response.data = JSON.parse(msg)
                                // }
                                //所有data为stirng 的都是经过压缩的内容
                                if(typeof(ret.response.data) == 'string')
                                {
                                    console.log('qian',ret.response.data.length)
                                    let msg = decodeURIComponent(decodeString(ret.response.data,true))
                                    console.log('hou:',msg.length)
                                    //处理下破坏JSON 的特殊字符
                                    // msg = msg.replace(/\\/g,'')   //pdt 列表查询时 如果规格里面有双引号  这里把转义替换了会导致问题，暂时关闭
                                    msg = msg.replace(/<[^>]*>/g, '')  //删除html标签
                                    // console.log('hou:',msg)
                                    ret.response.data = JSON.parse(msg) 
                                }

                                callback[0](ret.response)
                            }                            
                            else{ //报错
                                ElMessage.error(ret.response.message)
                                console.error(ret.response.code+' '+ret.response.message)
                            }
                        }
                        //移除
                        mapCallback.delete(ret.context.guuid)
                    }
                    else
                    {
                        console.log('找不到  '+ret.context.guuid+'  命令',mapCallback)
                    }
                    
                    }
                };
            
                reader.readAsArrayBuffer(event.data);
            }
 
        }
        catch(error)
        {
            console.log(error)
        }

    });

    socket.addEventListener('close', (event) => {
        console.log('-------------------WebSocket关闭:', event);

        if (isPageVisible) {
            clearTimeout(timmerReConnect);
            timmerReConnect = setTimeout(createWebSocket, 1000);
        }
        isCreatingWebSocket = false; // WebSocket 关闭后重置标志
    });

    socket.addEventListener('error', (error) => {
        console.error('WebSocket错误:', error);
    });
}
  
function handleVisibilityChange() {
    isPageVisible = !document.hidden;

    if (isPageVisible && socket && socket.readyState === WebSocket.CLOSED) {
        createWebSocket();
    }
}

let mapCallback: Map<string, any[]> | null = null;

let arrayHisCmd:any[] = []

// 封装WebSocket发送命令的函数
export const sendCommand = (type:string,command: string,params:any = null,content='',port=''): Promise<any> =>{
    if(bCloseWS)
    {
        console.log('WebSocket连接已关闭,'+type+'-'+command)
        return Promise.reject('WebSocket连接已关闭')
    }
    if(mapCallback == null || mapCallback == undefined)
    {
        mapCallback = new Map<string, any[]>();
    }
    return new Promise((resolve, reject) => {

        if (socket && socket.readyState === WebSocket.OPEN) {
        
            const guuid = getGUID(5);
            mapCallback?.set(guuid, [resolve, reject]);
            let msg = ''
            if(port != ''){
                msg = JSON.stringify({
                    context:{"guuid":guuid,"content":content,"route":command},
                    token:wsCache.get('Token'),
                    type:type,
                    cmd:command,
                    params:params,
                    port:port
                    })
            }
            else{
                msg = JSON.stringify({
                    context:{"guuid":guuid,"content":content,"route":command},
                    token:wsCache.get('Token'),
                    type:type,
                    cmd:command,
                    params:params
                    })
            }

            
            //打印警告日志
            console.log('sendCommand=>'+msg)
            // 发送命令
            
            const tmp: Uint8Array = encode(JSON.parse(msg));
            const encoded = new Blob([tmp]);            
            socket.send(encoded);

            // const  decodedString = decode(encoded)
            // console.log(decodedString)


        } else {
            arrayHisCmd.push({resolve:resolve,reject:reject,type:type,command:command,params:params,content:content})
            console.log('WebSocket未连接');  
            createWebSocket();
        }
    });
}

const sendCommandEx = (resolve,reject,type:string,command: string,params:any = null,content='')=>
{
    if(bCloseWS)
        return
    if (socket && socket.readyState === WebSocket.OPEN) {
        
        const guuid = getGUID(5);
        mapCallback?.set(guuid, [resolve, null]);
        const msg = JSON.stringify({
            context:{"guuid":guuid,"content":content,"route":command},
            token:wsCache.get('Token'),
            type:type,
            cmd:command,
            params:params
            })
        console.warn('sendCommandEx=>'+msg)
        // // 发送命令
        const tmp: Uint8Array = encode(JSON.parse(msg));
        const encoded = new Blob([tmp]);            
        socket.send(encoded);
    }
    else
    {
        // arrayHisCmd.push({resolve:resolve,type:type,command:command,params:params,content:content})
        console.log('WebSocket未连接');
        createWebSocket()
        // arrayHisCmd.push({resolve:resolve,reject:reject,type:type,command:command,params:params,content:content})
    }
}

//定时发送心跳heartbeat
const sendHeartbeat = () => {
    if(bCloseWS)
        return
    if (socket && socket.readyState === WebSocket.OPEN) {

        if(wsCache.get('Token') == null)
            return
        const info = wsCache.get(appStore.getUserInfo)
        const msg = JSON.stringify({
            context:{"guuid":'',"content":'',"route":'/erp/user/heartbeat'},
            type:'post',
            token:wsCache.get('Token'),
            cmd:'/erp/user/heartbeat',
            params:{"username":info.username}
            }
        );
       // console.log(JSON.parse(msg))
        const tmp: Uint8Array = encode(JSON.parse(msg));
        const encoded = new Blob([tmp]);            
        socket.send(encoded);
        //检测心跳
        // if(Date.now() - lastHeartbeatTime > 3000)
        // {
        //     console.log('心跳超时，重新发起连接')
        //     //createWebSocket()
        //    // socket.close();
        //     return
        // }
    }


}


  


export const setupWebSocket = () => {
    mapCallback = new Map();
    arrayHisCmd = []
    document.addEventListener('visibilitychange', handleVisibilityChange);
    createWebSocket();
    //定时发送心跳
    setInterval(sendHeartbeat, 1000);
    console.log('初始ws')
}